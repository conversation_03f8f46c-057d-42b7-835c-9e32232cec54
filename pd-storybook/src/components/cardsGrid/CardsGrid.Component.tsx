import React from 'react';

import { CircleLoader } from '../loaders/CircleLoader.Component';
import { Pagination } from '../pagination/Pagination.Component';

type CardsGridProps<T = unknown> = {
  loading: boolean;
  items: T[];
  renderItem: (item: T) => React.ReactNode;
  currentPage: number;
  onPageChange: (page: number) => void;
  totalItems: number;
  itemsPerPage: number;
  gridColsClass?: string;
  emptyMessage?: string;
};

// Compute total pages locally to avoid importing from infrastructure layer
const computeTotalPages = (totalItems: number, itemsPerPage: number) => {
  if (itemsPerPage <= 0) return 0;
  return Math.ceil(totalItems / itemsPerPage);
};

export function CardsGrid<T = unknown>({
  loading,
  items,
  renderItem,
  currentPage,
  onPageChange,
  totalItems,
  itemsPerPage,
  gridColsClass,
  emptyMessage,
}: CardsGridProps<T>): JSX.Element {
  if (loading) {
    return (
      <div
        data-testid="cards-grid-loader"
        className="pd-animate-spin pd-absolute pd-text-zinc-500"
      >
        <CircleLoader />
      </div>
    );
  }

  if (!items || items.length === 0) {
    return <div className="pd-text-sm pd-text-gray-500">{emptyMessage ?? 'Sin elementos'}</div>;
  }

  // Internal key extractor: prefer `id`, then `key`, then fallback to index
  const getKey = (item: T, index: number): string | number => {
    // try common properties safely
    const anyItem = item as unknown as { id?: string | number; key?: string | number };
    if (anyItem && (anyItem.id !== undefined && anyItem.id !== null)) return anyItem.id;
    if (anyItem && (anyItem.key !== undefined && anyItem.key !== null)) return anyItem.key;
    return index;
  };

  return (
    <div>
      <div className={`pd-grid ${gridColsClass ?? 'pd-grid-cols-3'} pd-gap-4 pd-items-stretch`}>
        {items.map((item, index) => (
          <div key={getKey(item, index)}>{renderItem(item)}</div>
        ))}
      </div>

      {items.length > 0 && (
        <div className="pd-pt-4">
          <Pagination
            initialPage={currentPage}
            totalPages={computeTotalPages(totalItems, itemsPerPage)}
            onPageChange={onPageChange}
            isLoading={loading}
          />
        </div>
      )}
    </div>
  );
}

export default CardsGrid;
