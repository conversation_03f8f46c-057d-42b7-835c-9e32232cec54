import React from 'react';

import { CircleLoader } from '../loaders/CircleLoader.Component';
import { Pagination } from '../pagination/Pagination.Component';

type CardsGridProps<T = unknown> = {
  loading: boolean;
  items: T[];
  renderItem: (item: T) => React.ReactNode;
  currentPage: number;
  onPageChange: (page: number) => void;
  totalItems: number;
  itemsPerPage: number;
  gridColsClass?: string;
  emptyMessage?: string;
  /** Whether to use pd- prefix for CSS classes. Defaults to true for Storybook compatibility */
  usePdPrefix?: boolean;
};

// Compute total pages locally to avoid importing from infrastructure layer
const computeTotalPages = (totalItems: number, itemsPerPage: number) => {
  if (itemsPerPage <= 0) return 0;
  return Math.ceil(totalItems / itemsPerPage);
};

export function CardsGrid<T = unknown>({
  loading,
  items,
  renderItem,
  currentPage,
  onPageChange,
  totalItems,
  itemsPerPage,
  gridColsClass,
  emptyMessage,
  usePdPrefix = true,
}: CardsGridProps<T>): JSX.Element {
  const prefix = usePdPrefix ? 'pd-' : '';

  if (loading) {
    return (
      <div
        data-testid="cards-grid-loader"
        className={`${prefix}animate-spin ${prefix}absolute ${prefix}text-zinc-500`}
      >
        <CircleLoader />
      </div>
    );
  }

  if (!items || items.length === 0) {
    return <div className={`${prefix}text-sm ${prefix}text-gray-500`}>{emptyMessage ?? 'Sin elementos'}</div>;
  }

  // Internal key extractor: prefer `id`, then `key`, then fallback to index
  const getKey = (item: T, index: number): string | number => {
    // try common properties safely
    const anyItem = item as unknown as { id?: string | number; key?: string | number };
    if (anyItem && (anyItem.id !== undefined && anyItem.id !== null)) return anyItem.id;
    if (anyItem && (anyItem.key !== undefined && anyItem.key !== null)) return anyItem.key;
    return index;
  };

  // Default grid class with proper prefix
  const defaultGridClass = `${prefix}grid-cols-3`;
  const gridClass = gridColsClass || defaultGridClass;

  return (
    <div>
      <div className={`${prefix}grid ${gridClass} ${prefix}gap-4 ${prefix}items-stretch`}>
        {items.map((item, index) => (
          <div key={getKey(item, index)}>{renderItem(item)}</div>
        ))}
      </div>

      {items.length > 0 && (
        <div className={`${prefix}pt-4`}>
          <Pagination
            initialPage={currentPage}
            totalPages={computeTotalPages(totalItems, itemsPerPage)}
            onPageChange={onPageChange}
            isLoading={loading}
          />
        </div>
      )}
    </div>
  );
}

export default CardsGrid;
