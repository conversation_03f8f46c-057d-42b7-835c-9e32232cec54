import { render, screen } from '@testing-library/react';

import { CardsGrid } from './CardsGrid.Component';

// Mock data for tests
interface MockItem {
  id: string;
  title: string;
}

const mockItems: MockItem[] = [
  { id: '1', title: 'Item 1' },
  { id: '2', title: 'Item 2' },
  { id: '3', title: 'Item 3' },
];

const renderMockItem = (item: MockItem) => (
  <div data-testid={`item-${item.id}`}>{item.title}</div>
);

const mockOnPageChange = jest.fn();

const defaultProps = {
  loading: false,
  items: mockItems,
  renderItem: renderMockItem,
  currentPage: 1,
  onPageChange: mockOnPageChange,
  totalItems: mockItems.length,
  itemsPerPage: 10,
  usePdPrefix: true,
};

describe('CardsGrid', () => {
  it('renders items correctly', () => {
    render(<CardsGrid {...defaultProps} />);

    expect(screen.getByTestId('item-1')).toBeInTheDocument();
    expect(screen.getByTestId('item-2')).toBeInTheDocument();
    expect(screen.getByTestId('item-3')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<CardsGrid {...defaultProps} loading />);

    expect(screen.getByTestId('cards-grid-loader')).toBeInTheDocument();
    expect(screen.queryByTestId('item-1')).not.toBeInTheDocument();
  });

  it('shows empty message when no items', () => {
    render(<CardsGrid {...defaultProps} items={[]} totalItems={0} />);

    expect(screen.getByText('Sin elementos')).toBeInTheDocument();
  });

  it('shows custom empty message', () => {
    const customMessage = 'No hay productos disponibles';
    render(
      <CardsGrid
        {...defaultProps}
        items={[]}
        totalItems={0}
        emptyMessage={customMessage}
      />,
    );

    expect(screen.getByText(customMessage)).toBeInTheDocument();
  });

  it('applies custom grid columns class', () => {
    const { container } = render(
      <CardsGrid {...defaultProps} gridColsClass="pd-grid-cols-4" />,
    );

    const gridElement = container.querySelector('.pd-grid-cols-4');
    expect(gridElement).toBeInTheDocument();
  });

  it('uses default grid columns class when not provided', () => {
    const { container } = render(<CardsGrid {...defaultProps} />);

    const gridElement = container.querySelector('.pd-grid-cols-3');
    expect(gridElement).toBeInTheDocument();
  });

  it('shows pagination when items are present', () => {
    render(<CardsGrid {...defaultProps} />);

    // Pagination component should be rendered (assuming it has some identifiable content)
    const paginationContainer = screen.getByRole('navigation', { hidden: true });
    expect(paginationContainer).toBeInTheDocument();
  });

  it('handles items with id property for keys', () => {
    const itemsWithId = [{ id: 'test-id', title: 'Test Item' }];
    render(
      <CardsGrid
        {...defaultProps}
        items={itemsWithId}
        renderItem={(item) => <div data-testid={`item-${item.id}`}>{item.title}</div>}
      />,
    );

    expect(screen.getByTestId('item-test-id')).toBeInTheDocument();
  });

  it('handles items with key property for keys', () => {
    const itemsWithKey = [{ key: 'test-key', title: 'Test Item' }];
    render(
      <CardsGrid
        {...defaultProps}
        items={itemsWithKey}
        renderItem={(item) => <div data-testid={`item-${item.key}`}>{item.title}</div>}
      />,
    );

    expect(screen.getByTestId('item-test-key')).toBeInTheDocument();
  });

  it('falls back to index for keys when no id or key property', () => {
    const itemsWithoutKeys = [{ title: 'Test Item' }];
    render(
      <CardsGrid
        {...defaultProps}
        items={itemsWithoutKeys}
        renderItem={(item: { title: string }) => <div data-testid="item-without-key">{item.title}</div>}
      />,
    );

    expect(screen.getByTestId('item-without-key')).toBeInTheDocument();
  });

  it('uses pd- prefix when usePdPrefix is true', () => {
    const { container } = render(<CardsGrid {...defaultProps} usePdPrefix />);

    const gridElement = container.querySelector('.pd-grid');
    expect(gridElement).toBeInTheDocument();
  });

  it('does not use pd- prefix when usePdPrefix is false', () => {
    const { container } = render(<CardsGrid {...defaultProps} usePdPrefix={false} />);

    const gridElement = container.querySelector('.grid');
    const pdGridElement = container.querySelector('.pd-grid');
    expect(gridElement).toBeInTheDocument();
    expect(pdGridElement).not.toBeInTheDocument();
  });
});
