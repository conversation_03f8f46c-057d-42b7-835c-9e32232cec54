import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { Card } from '../components/card/Card.Component';
import { CardContent } from '../components/card/CardContent.Component';
import { CardHeader } from '../components/card/CardHeader.Component';
import { CardTitle } from '../components/card/CardTitle.Component';
import { CardsGrid } from '../components/cardsGrid/CardsGrid.Component';

// Mock data for the stories
interface MockItem {
  id: string;
  title: string;
  description: string;
  category: string;
}

const mockItems: MockItem[] = [
  { id: '1', title: 'Producto 1', description: 'Descripción del producto 1', category: 'Categoría A' },
  { id: '2', title: 'Producto 2', description: 'Descripción del producto 2', category: 'Categoría B' },
  { id: '3', title: 'Producto 3', description: 'Descripción del producto 3', category: 'Categoría A' },
  { id: '4', title: 'Producto 4', description: 'Descripción del producto 4', category: 'Categoría C' },
  { id: '5', title: 'Producto 5', description: 'Descripción del producto 5', category: 'Categoría B' },
  { id: '6', title: 'Producto 6', description: 'Descripción del producto 6', category: 'Categoría A' },
  { id: '7', title: 'Producto 7', description: 'Descripción del producto 7', category: 'Categoría C' },
  { id: '8', title: 'Producto 8', description: 'Descripción del producto 8', category: 'Categoría B' },
  { id: '9', title: 'Producto 9', description: 'Descripción del producto 9', category: 'Categoría A' },
];

// Render function for mock items
const renderMockItem = (item: MockItem) => (
  <Card className="pd-h-full">
    <CardHeader>
      <CardTitle>{item.title}</CardTitle>
    </CardHeader>
    <CardContent>
      <p className="pd-text-sm pd-text-gray-600 pd-mb-2">{item.description}</p>
      <span className="pd-inline-block pd-bg-blue-100 pd-text-blue-800 pd-text-xs pd-px-2 pd-py-1 pd-rounded">
        {item.category}
      </span>
    </CardContent>
  </Card>
);

const meta: Meta<typeof CardsGrid> = {
  title: 'Components/CardsGrid',
  component: CardsGrid,
  parameters: {
    layout: 'padded',
  },
  args: {
    loading: false,
    items: mockItems.slice(0, 6),
    renderItem: renderMockItem,
    currentPage: 1,
    onPageChange: fn(),
    totalItems: 6,
    itemsPerPage: 6,
    gridColsClass: 'pd-grid-cols-3',
    emptyMessage: 'No hay elementos para mostrar',
  },
  argTypes: {
    loading: {
      control: 'boolean',
      description: 'Muestra el estado de carga',
    },
    items: {
      control: false,
      description: 'Array de elementos a mostrar',
    },
    renderItem: {
      control: false,
      description: 'Función para renderizar cada elemento',
    },
    currentPage: {
      control: { type: 'number', min: 1 },
      description: 'Página actual',
    },
    totalItems: {
      control: { type: 'number', min: 0 },
      description: 'Total de elementos',
    },
    itemsPerPage: {
      control: { type: 'number', min: 1 },
      description: 'Elementos por página',
    },
    gridColsClass: {
      control: 'select',
      options: ['pd-grid-cols-1', 'pd-grid-cols-2', 'pd-grid-cols-3', 'pd-grid-cols-4', 'pd-grid-cols-6'],
      description: 'Clase CSS para el número de columnas del grid',
    },
    emptyMessage: {
      control: 'text',
      description: 'Mensaje a mostrar cuando no hay elementos',
    },
    onPageChange: {
      control: false,
      description: 'Callback para cambio de página',
    },
  },
};

export default meta;
type Story = StoryObj<typeof CardsGrid>;

export const Default: Story = {};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Empty: Story = {
  args: {
    items: [],
    totalItems: 0,
  },
};

export const CustomEmptyMessage: Story = {
  args: {
    items: [],
    totalItems: 0,
    emptyMessage: 'No se encontraron productos',
  },
};

export const TwoColumns: Story = {
  args: {
    gridColsClass: 'pd-grid-cols-2',
  },
};

export const FourColumns: Story = {
  args: {
    gridColsClass: 'pd-grid-cols-4',
  },
};

export const WithPagination: Story = {
  args: {
    items: mockItems.slice(0, 3),
    totalItems: mockItems.length,
    itemsPerPage: 3,
    currentPage: 1,
  },
};

export const ManyItems: Story = {
  args: {
    items: mockItems,
    totalItems: mockItems.length,
    itemsPerPage: 6,
    gridColsClass: 'pd-grid-cols-3',
  },
};
