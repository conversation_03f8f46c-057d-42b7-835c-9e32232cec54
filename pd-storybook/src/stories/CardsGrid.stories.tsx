import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { Card } from '../components/card/Card.Component';
import { CardsGrid } from '../components/cardsGrid/CardsGrid.Component';

// Mock data for the stories
interface MockItem {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  avatar?: string;
  color?: string;
}

const mockItems: MockItem[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '584126697244',
    location: 'Villa Marina',
    color: '#FFEB3B',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '584123456789',
    location: 'Caracas',
    color: '#4CAF50',
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '584198765432',
    location: 'Maracaibo',
    color: '#2196F3',
  },
  {
    id: '4',
    name: '<PERSON>',
    email: 'ma<PERSON>.<PERSON><PERSON><PERSON><PERSON>@email.com',
    phone: '584187654321',
    location: 'Valencia',
    color: '#FF5722',
  },
  {
    id: '5',
    name: 'José <PERSON>',
    email: '<EMAIL>',
    phone: '584176543210',
    location: 'Barquisimeto',
    color: '#9C27B0',
  },
  {
    id: '6',
    name: 'Laura Fernández',
    email: '<EMAIL>',
    phone: '584165432109',
    location: 'Maracay',
    color: '#FF9800',
  },
  {
    id: '7',
    name: 'Pedro Sánchez',
    email: '<EMAIL>',
    phone: '584154321098',
    location: 'San Cristóbal',
    color: '#795548',
  },
  {
    id: '8',
    name: 'Carmen Díaz',
    email: '<EMAIL>',
    phone: '584143210987',
    location: 'Puerto La Cruz',
    color: '#607D8B',
  },
  {
    id: '9',
    name: 'Roberto Torres',
    email: '<EMAIL>',
    phone: '584132109876',
    location: 'Mérida',
    color: '#E91E63',
  },
];

// Render function for mock items with contact card style
const renderMockItem = (item: MockItem) => (
  <Card className="pd-h-full pd-p-4 pd-bg-white pd-border pd-border-gray-200 pd-rounded-lg pd-shadow-sm hover:pd-shadow-md pd-transition-shadow">
    <div className="pd-flex pd-items-start pd-justify-between pd-h-full">
      <div className="pd-flex pd-items-start pd-space-x-3 pd-flex-1">
        {/* Avatar */}
        <div
          className="pd-w-12 pd-h-12 pd-rounded-full pd-flex pd-items-center pd-justify-center pd-text-black pd-font-bold pd-text-lg pd-flex-shrink-0"
          style={{ backgroundColor: item.color }}
        >
          {item.name.charAt(0).toUpperCase()}
        </div>

        {/* Contact Info */}
        <div className="pd-flex-1 pd-min-w-0">
          <h3 className="pd-text-lg pd-font-semibold pd-text-gray-900 pd-mb-2">
            {item.name}
          </h3>

          <div className="pd-space-y-1">
            <div className="pd-flex pd-items-center pd-text-sm pd-text-gray-600">
              <span className="pd-mr-2">✉</span>
              <span className="pd-truncate">{item.email}</span>
            </div>

            <div className="pd-flex pd-items-center pd-text-sm pd-text-gray-600">
              <span className="pd-mr-2">📞</span>
              <span>{item.phone}</span>
            </div>

            <div className="pd-flex pd-items-center pd-text-sm pd-text-gray-600">
              <span className="pd-mr-2">📍</span>
              <span>{item.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Action Button */}
      <div className="pd-ml-4 pd-flex-shrink-0">
        <button className="pd-bg-yellow-400 hover:pd-bg-yellow-500 pd-text-black pd-px-4 pd-py-2 pd-rounded-lg pd-text-sm pd-font-medium pd-transition-colors">
          Ver Detalles
        </button>
      </div>
    </div>
  </Card>
);

const meta: Meta<typeof CardsGrid> = {
  title: 'Components/CardsGrid',
  component: CardsGrid,
  parameters: {
    layout: 'padded',
  },
  args: {
    loading: false,
    items: mockItems.slice(0, 6),
    renderItem: renderMockItem,
    currentPage: 1,
    onPageChange: fn(),
    totalItems: 6,
    itemsPerPage: 6,
    gridColsClass: 'pd-grid-cols-1',
    emptyMessage: 'No hay contactos para mostrar',
  },
  argTypes: {
    loading: {
      control: 'boolean',
      description: 'Muestra el estado de carga',
    },
    items: {
      control: false,
      description: 'Array de elementos a mostrar',
    },
    renderItem: {
      control: false,
      description: 'Función para renderizar cada elemento',
    },
    currentPage: {
      control: { type: 'number', min: 1 },
      description: 'Página actual',
    },
    totalItems: {
      control: { type: 'number', min: 0 },
      description: 'Total de elementos',
    },
    itemsPerPage: {
      control: { type: 'number', min: 1 },
      description: 'Elementos por página',
    },
    gridColsClass: {
      control: 'select',
      options: ['pd-grid-cols-1', 'pd-grid-cols-2', 'pd-grid-cols-3'],
      description: 'Clase CSS para el número de columnas del grid',
    },
    emptyMessage: {
      control: 'text',
      description: 'Mensaje a mostrar cuando no hay contactos',
    },
    onPageChange: {
      control: false,
      description: 'Callback para cambio de página',
    },
  },
};

export default meta;
type Story = StoryObj<typeof CardsGrid>;

export const Default: Story = {};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Empty: Story = {
  args: {
    items: [],
    totalItems: 0,
  },
};

export const CustomEmptyMessage: Story = {
  args: {
    items: [],
    totalItems: 0,
    emptyMessage: 'No se encontraron contactos',
  },
};

export const SingleColumn: Story = {
  args: {
    gridColsClass: 'pd-grid-cols-1',
  },
};

export const TwoColumns: Story = {
  args: {
    gridColsClass: 'pd-grid-cols-2',
  },
};

export const ThreeColumns: Story = {
  args: {
    gridColsClass: 'pd-grid-cols-3',
  },
};

export const WithPagination: Story = {
  args: {
    items: mockItems.slice(0, 3),
    totalItems: mockItems.length,
    itemsPerPage: 3,
    currentPage: 1,
  },
};

export const ManyContacts: Story = {
  args: {
    items: mockItems,
    totalItems: mockItems.length,
    itemsPerPage: 6,
    gridColsClass: 'pd-grid-cols-1',
  },
};
