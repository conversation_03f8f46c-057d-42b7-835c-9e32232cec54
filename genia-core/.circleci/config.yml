version: 2.1
orbs:
  slack: circleci/slack@5.1.1

parameters:
  deploy_tag:
    type: string
    default: ""
commands:
  send-slack-message:
    parameters:
      title:
        type: string
      event:
        type: enum
        enum: [always, pass, fail]
      workflow_name:
        type: string
    steps:
      - slack/notify:
          custom: |
            {
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "Genia Core: << parameters.title >>",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Project:*\n${CIRCLE_PROJECT_REPONAME}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Tag:*\n${CIRCLE_TAG:-<< pipeline.parameters.deploy_tag >>}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Workflow name/Build Id:*\n<< parameters.workflow_name >>/${CIRCLE_WORKFLOW_ID}"
                    }
                  ]
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":link: *Links*\n<${CIRCLE_BUILD_URL}|View Build Details>"
                  }
                }
              ]
            }
          event: << parameters.event >>
          channel: "#ci_cd"

jobs:
  build-and-push:
    docker:
      - image: cimg/gcp:2024.08
    parameters:
      workflow_name:
        type: string
    steps:
      - send-slack-message:
          title: ":hourglass_flowing_sand: Image Build and push in progress"
          event: always
          workflow_name: << parameters.workflow_name >>
      - checkout
      - setup_remote_docker:
          docker_layer_caching: true
      - run:
          name: Authenticate with Google Cloud
          command: |
            echo $DEPLOY_CREDENTIALS > /tmp/service-account.json
            gcloud auth activate-service-account --key-file=/tmp/service-account.json
      - run:
          name: authenticate with docker
          command: |
            gcloud auth configure-docker us-south1-docker.pkg.dev
      - run:
          name: build and tag image
          command: |
            docker build -t us-south1-docker.pkg.dev/${PROJECT_ID}/pd-docker-images/genia-core:${CIRCLE_TAG} .
            docker build -t us-south1-docker.pkg.dev/${PROJECT_ID}/pd-docker-images/genia-core:latest .
      - run:
          name: push image
          command: |
            docker push us-south1-docker.pkg.dev/${PROJECT_ID}/pd-docker-images/genia-core:${CIRCLE_TAG}
            docker push us-south1-docker.pkg.dev/${PROJECT_ID}/pd-docker-images/genia-core:latest
      - send-slack-message:
          title: ":rocket: Build and push successful"
          event: pass
          workflow_name: << parameters.workflow_name >>
      - send-slack-message:
          title: ":bangbang: Build and push failed :speak_no_evil:"
          event: fail
          workflow_name: << parameters.workflow_name >>
  notify_start:
    docker:
      - image: cimg/node:20.18.1
    environment:
      REPO_URL: << pipeline.project.git_url >>
    steps:
      - send-slack-message:
          title: ":warning: Build image and push, approval required :warning: "
          event: always
          workflow_name: "Build and Publish"
  deploy_tag:
    docker:
      - image: cimg/gcp:2024.08
    steps:
      - send-slack-message:
          title: ":hourglass_flowing_sand: Deploy in progress"
          event: always
          workflow_name: "Deploy"
      - checkout
      - run:
          name: Checkout to tag
          command: |
            git fetch --all --tags
            git checkout tags/<< pipeline.parameters.deploy_tag >>
      - run:
          name: Authenticate with Google Cloud
          command: |
            echo $DEPLOY_CREDENTIALS > /tmp/service-account.json
            gcloud auth activate-service-account --key-file=/tmp/service-account.json
            gcloud config set project genia-core-production
      - run:
          name: cloudrun deploy
          command: >
            gcloud run deploy genia-core-production
            --image=us-south1-docker.pkg.dev/${PROJECT_ID}/pd-docker-images/genia-core:<< pipeline.parameters.deploy_tag >>
            --platform=managed
            --region=us-south1
            --allow-unauthenticated
      -  send-slack-message:
          title: ":rocket: Deploy successful"
          event: pass
          workflow_name: "Deploy"
      -  send-slack-message:
          title: ":bangbang: Deploy failed :speak_no_evil:"
          event: fail
          workflow_name: "Deploy"
        
workflows:
  build-image:
    jobs:
      - notify_start:
          context:
            - SLACK
          filters:
            tags:
              only: /[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              ignore: /.*/
      - wait-for-approval:
          type: approval
          requires:
            - notify_start
          filters:
            tags:
              only: /[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              ignore: /.*/
      - build-and-push:
          context:
            - SLACK
            - CREDENTIALS
            - GCP_VARS
          workflow_name: "Build and Publish"
          filters:
            tags:
              only: /[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              ignore: /.*/
          requires:
            - wait-for-approval
  deploy:
    when: << pipeline.parameters.deploy_tag >>
    jobs:
      - deploy_tag:
          context:
            - SLACK
            - CREDENTIALS
            - GCP_VARS
