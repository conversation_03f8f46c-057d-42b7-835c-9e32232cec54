table:
  name: inventory_history
  schema: public
configuration:
  column_config:
    created_at:
      custom_name: createdAt
    id:
      custom_name: id
    inventory_id:
      custom_name: inventoryId
    measurement_unit:
      custom_name: measurementUnit
    movement_type:
      custom_name: movementType
    quantity:
      custom_name: quantity
    reason:
      custom_name: reason
    user_id:
      custom_name: userId
  custom_column_names:
    created_at: createdAt
    id: id
    inventory_id: inventoryId
    measurement_unit: measurementUnit
    movement_type: movementType
    quantity: quantity
    reason: reason
    user_id: userId
  custom_name: inventoryHistory
  custom_root_fields: {}
object_relationships:
  - name: inventory
    using:
      foreign_key_constraint_on: inventory_id
  - name: user
    using:
      foreign_key_constraint_on: user_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - reason
        - quantity
        - created_at
        - movement_type
        - id
        - inventory_id
        - user_id
        - measurement_unit
      filter:
        inventory:
          company_id:
            _eq: X-Hasura-Company-Id
      limit: 100
      allow_aggregations: true
    comment: ""
