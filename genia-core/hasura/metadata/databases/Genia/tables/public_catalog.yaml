table:
  name: catalog
  schema: public
configuration:
  column_config:
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    disabled_at:
      custom_name: disabledAt
    read_id:
      custom_name: readId
    requires_stock:
      custom_name: requiresStock
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    company_id: companyId
    created_at: createdAt
    disabled_at: disabledAt
    read_id: readId
    requires_stock: requiresStock
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: company
    using:
      foreign_key_constraint_on: company_id
array_relationships:
  - name: catalogTax
    using:
      foreign_key_constraint_on:
        column: catalog_id
        table:
          name: catalog_tax
          schema: public
  - name: catalog_discounts
    using:
      foreign_key_constraint_on:
        column: catalog_id
        table:
          name: catalog_discount_catalog
          schema: public
  - name: catalog_media
    using:
      foreign_key_constraint_on:
        column: catalog_id
        table:
          name: catalog_media
          schema: public
  - name: inventoryCatalogs
    using:
      foreign_key_constraint_on:
        column: catalog_id
        table:
          name: inventory_catalog
          schema: public
select_permissions:
  - role: read:hasura
    permission:
      columns:
        - attributes
        - company_id
        - created_at
        - description
        - disabled_at
        - id
        - name
        - price
        - read_id
        - requires_stock
        - type
        - updated_at
      filter: {}
      limit: 20
    comment: ""
  - role: read_user
    permission:
      columns:
        - requires_stock
        - description
        - name
        - read_id
        - attributes
        - price
        - created_at
        - disabled_at
        - updated_at
        - type
        - company_id
        - id
      filter:
        _or:
          - company_id:
              _eq: X-Hasura-Company-Id
          - company:
              clients:
                client_company_id:
                  _eq: X-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
