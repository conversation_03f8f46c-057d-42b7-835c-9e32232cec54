table:
  name: quote_provider_fields
  schema: public
configuration:
  column_config:
    created_at:
      custom_name: createdAt
    quote_id:
      custom_name: quoteId
    related_sale_order_id:
      custom_name: relatedSaleOrderId
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    created_at: createdAt
    quote_id: quoteId
    related_sale_order_id: relatedSaleOrderId
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: quote
    using:
      foreign_key_constraint_on: quote_id
  - name: sale_order
    using:
      foreign_key_constraint_on: related_sale_order_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - notes
        - created_at
        - updated_at
        - id
        - quote_id
        - related_sale_order_id
      filter:
        quote:
          company_id:
            _eq: X-Hasura-Company-Id
    comment: ""
