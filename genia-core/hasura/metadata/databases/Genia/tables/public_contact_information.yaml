table:
  name: contact_information
  schema: public
configuration:
  column_config:
    billing_address:
      custom_name: billingAddress
    billing_email:
      custom_name: billingEmail
    billing_phone_number:
      custom_name: billingPhoneNumber
    billing_whatsapp:
      custom_name: billingWhatsapp
    created_at:
      custom_name: createdAt
    entity_id:
      custom_name: entityId
    main_address:
      custom_name: mainAddress
    main_email:
      custom_name: mainEmail
    main_phone_number:
      custom_name: mainPhoneNumber
    main_whatsapp:
      custom_name: mainWhatsapp
    purchases_email:
      custom_name: purchasesEmail
    purchases_phone_number:
      custom_name: purchasesPhoneNumber
    purchases_whatsapp:
      custom_name: purchasesWhatsapp
    representative_name:
      custom_name: representativeName
    sales_email:
      custom_name: salesEmail
    sales_phone_number:
      custom_name: salesPhoneNumber
    sales_whatsapp:
      custom_name: salesWhatsapp
    shipping_address:
      custom_name: shippingAddress
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    billing_address: billingAddress
    billing_email: billingEmail
    billing_phone_number: billingPhoneNumber
    billing_whatsapp: billingWhatsapp
    created_at: createdAt
    entity_id: entityId
    main_address: mainAddress
    main_email: mainEmail
    main_phone_number: mainPhoneNumber
    main_whatsapp: mainWhatsapp
    purchases_email: purchasesEmail
    purchases_phone_number: purchasesPhoneNumber
    purchases_whatsapp: purchasesWhatsapp
    representative_name: representativeName
    sales_email: salesEmail
    sales_phone_number: salesPhoneNumber
    sales_whatsapp: salesWhatsapp
    shipping_address: shippingAddress
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: client
    using:
      manual_configuration:
        column_mapping:
          entity_id: id
        insertion_order: null
        remote_table:
          name: client
          schema: public
  - name: company
    using:
      manual_configuration:
        column_mapping:
          entity_id: id
        insertion_order: null
        remote_table:
          name: company
          schema: public
  - name: provider
    using:
      manual_configuration:
        column_mapping:
          entity_id: id
        insertion_order: null
        remote_table:
          name: provider
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - billing_address
        - billing_email
        - billing_phone_number
        - billing_whatsapp
        - created_at
        - main_address
        - main_email
        - main_phone_number
        - main_whatsapp
        - purchases_email
        - purchases_phone_number
        - purchases_whatsapp
        - representative_name
        - sales_email
        - sales_phone_number
        - sales_whatsapp
        - shipping_address
        - updated_at
      filter:
        _or:
          - client:
              company_id:
                _eq: X-Hasura-Company-Id
          - company:
              id:
                _eq: X-Hasura-Company-Id
          - provider:
              company_id:
                _eq: X-Hasura-Company-Id
    comment: ""
