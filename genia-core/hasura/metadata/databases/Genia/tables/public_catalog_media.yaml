table:
  name: catalog_media
  schema: public
configuration:
  column_config:
    catalog_id:
      custom_name: catalogId
    created_at:
      custom_name: createdAt
    media_type:
      custom_name: mediaType
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    catalog_id: catalogId
    created_at: createdAt
    media_type: mediaType
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: catalog
    using:
      manual_configuration:
        column_mapping:
          catalog_id: id
        insertion_order: null
        remote_table:
          name: catalog
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - catalog_id
        - created_at
        - id
        - media_type
        - processing
        - updated_at
        - url
      filter:
        _or:
          - catalog:
              company_id:
                _eq: x-Hasura-Company-Id
          - catalog:
              company:
                clients:
                  client_company_id:
                    _eq: x-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
