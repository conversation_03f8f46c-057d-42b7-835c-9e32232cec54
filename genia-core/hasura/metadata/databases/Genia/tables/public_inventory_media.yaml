table:
  name: inventory_media
  schema: public
configuration:
  column_config:
    created_at:
      custom_name: createdAt
    id:
      custom_name: id
    inventory_id:
      custom_name: inventoryId
    media_type:
      custom_name: mediaType
    size:
      custom_name: size
    updated_at:
      custom_name: updatedAt
    url:
      custom_name: url
  custom_column_names:
    created_at: createdAt
    id: id
    inventory_id: inventoryId
    media_type: mediaType
    size: size
    updated_at: updatedAt
    url: url
  custom_name: inventoryMedia
  custom_root_fields: {}
object_relationships:
  - name: inventory
    using:
      foreign_key_constraint_on: inventory_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - created_at
        - id
        - inventory_id
        - media_type
        - processing
        - size
        - updated_at
        - url
      filter:
        inventory:
          company_id:
            _eq: X-Hasura-Company-Id
      limit: 100
    comment: ""
