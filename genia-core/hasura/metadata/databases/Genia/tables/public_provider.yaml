table:
  name: provider
  schema: public
configuration:
  column_config:
    created_at:
      custom_name: createdAt
    provider_company_id:
      custom_name: providerCompanyId
    tributary_id:
      custom_name: tributaryId
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    created_at: createdAt
    provider_company_id: providerCompanyId
    tributary_id: tributaryId
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: company
    using:
      foreign_key_constraint_on: company_id
  - name: contact_information
    using:
      manual_configuration:
        column_mapping:
          id: entity_id
        insertion_order: null
        remote_table:
          name: contact_information
          schema: public
  - name: providerCompany
    using:
      foreign_key_constraint_on: provider_company_id
array_relationships:
  - name: providerInventories
    using:
      foreign_key_constraint_on:
        column: provider_id
        table:
          name: provider_inventory
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - name
        - tributary_id
        - created_at
        - updated_at
        - company_id
        - id
        - provider_company_id
      filter:
        company_id:
          _eq: X-Hasura-Company-Id
      limit: 100
      allow_aggregations: true
    comment: ""
