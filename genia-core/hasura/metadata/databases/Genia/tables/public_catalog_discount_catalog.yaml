table:
  name: catalog_discount_catalog
  schema: public
configuration:
  column_config:
    catalog_discount_id:
      custom_name: catalogDiscountId
    catalog_id:
      custom_name: catalogId
  custom_column_names:
    catalog_discount_id: catalogDiscountId
    catalog_id: catalogId
  custom_root_fields: {}
object_relationships:
  - name: catalog
    using:
      manual_configuration:
        column_mapping:
          catalog_id: id
        insertion_order: null
        remote_table:
          name: catalog
          schema: public
  - name: catalog_discount
    using:
      foreign_key_constraint_on: catalog_discount_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - catalog_discount_id
        - catalog_id
      filter:
        catalog:
          company_id:
            _eq: X-Hasura-Company-Id
    comment: ""
