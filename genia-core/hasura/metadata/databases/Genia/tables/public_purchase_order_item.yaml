table:
  name: purchase_order_item
  schema: public
configuration:
  column_config:
    created_at:
      custom_name: createdAt
    discount_type:
      custom_name: discountType
    discount_value:
      custom_name: discountValue
    inventory_id:
      custom_name: inventoryId
    name:
      custom_name: name
    product_id:
      custom_name: productId
    purchase_order_id:
      custom_name: purchaseOrderId
    quantity:
      custom_name: quantity
    reference_id:
      custom_name: referenceId
    subtotal:
      custom_name: subtotal
    taxes:
      custom_name: taxes
    total:
      custom_name: total
    unit_price:
      custom_name: unitPrice
    unit_price_after_discount:
      custom_name: unitPriceAfterDiscount
    unit_price_after_discount_and_taxes:
      custom_name: unitPriceAfterDiscountAndTaxes
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    created_at: createdAt
    discount_type: discountType
    discount_value: discountValue
    inventory_id: inventoryId
    name: name
    product_id: productId
    purchase_order_id: purchaseOrderId
    quantity: quantity
    reference_id: referenceId
    subtotal: subtotal
    taxes: taxes
    total: total
    unit_price: unitPrice
    unit_price_after_discount: unitPriceAfterDiscount
    unit_price_after_discount_and_taxes: unitPriceAfterDiscountAndTaxes
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: purchase_order
    using:
      foreign_key_constraint_on: purchase_order_id
array_relationships:
  - name: purchase_order_item_inventories
    using:
      foreign_key_constraint_on:
        column: purchase_order_item_id
        table:
          name: purchase_order_item_inventory
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - purchase_order_id
        - reference_id
        - name
        - product_id
        - quantity
        - unit_price
        - unit_price_after_discount
        - unit_price_after_discount_and_taxes
        - subtotal
        - total
        - discount_type
        - discount_value
        - inventory_id
        - taxes
        - created_at
        - updated_at
      filter:
        purchase_order:
          company_id:
            _eq: X-Hasura-Company-Id
      limit: 20
      allow_aggregations: true
    comment: ""
