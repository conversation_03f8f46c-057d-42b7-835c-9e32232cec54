table:
  name: purchase_order
  schema: public
configuration:
  column_config:
    assigned_user_id:
      custom_name: assignedUserId
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    delivery_date:
      custom_name: deliveryDate
    provider_id:
      custom_name: providerId
    read_id:
      custom_name: readId
    related_sale_order_id:
      custom_name: relatedSaleOrderId
    review_started_at:
      custom_name: reviewStartedAt
    shipped_at:
      custom_name: shippedAt
    shipping_address:
      custom_name: shippingAddress
    shipping_price:
      custom_name: shippingPrice
    status:
      custom_name: status
    subtotal:
      custom_name: subtotal
    subtotal_before_discount:
      custom_name: subtotalBeforeDiscount
    taxes:
      custom_name: taxes
    total:
      custom_name: total
    total_discount:
      custom_name: totalDiscount
    total_taxes:
      custom_name: totalTaxes
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    assigned_user_id: assignedUserId
    company_id: companyId
    created_at: createdAt
    delivery_date: deliveryDate
    provider_id: providerId
    read_id: readId
    related_sale_order_id: relatedSaleOrderId
    review_started_at: reviewStartedAt
    shipped_at: shippedAt
    shipping_address: shippingAddress
    shipping_price: shippingPrice
    status: status
    subtotal: subtotal
    subtotal_before_discount: subtotalBeforeDiscount
    taxes: taxes
    total: total
    total_discount: totalDiscount
    total_taxes: totalTaxes
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: assignedUser
    using:
      foreign_key_constraint_on: assigned_user_id
  - name: company
    using:
      foreign_key_constraint_on: company_id
  - name: provider
    using:
      foreign_key_constraint_on: provider_id
  - name: user
    using:
      manual_configuration:
        column_mapping:
          assigned_user_id: id
        insertion_order: null
        remote_table:
          name: user
          schema: public
array_relationships:
  - name: purchase_order_items
    using:
      foreign_key_constraint_on:
        column: purchase_order_id
        table:
          name: purchase_order_item
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - assigned_user_id
        - company_id
        - created_at
        - delivery_date
        - id
        - notes
        - provider_id
        - read_id
        - related_sale_order_id
        - review_started_at
        - shipped_at
        - shipping_address
        - shipping_price
        - status
        - subtotal
        - subtotal_before_discount
        - taxes
        - total
        - total_discount
        - total_taxes
        - updated_at
      filter:
        company_id:
          _eq: x-Hasura-Company-Id
      limit: 20
      allow_aggregations: true
    comment: ""
