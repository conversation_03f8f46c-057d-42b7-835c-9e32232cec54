table:
  name: sale_order
  schema: public
configuration:
  column_config:
    assigned_user_id:
      custom_name: assignedUserId
    client_id:
      custom_name: clientId
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    delivery_date:
      custom_name: deliveryDate
    id:
      custom_name: id
    notes:
      custom_name: notes
    read_id:
      custom_name: readId
    review_started_at:
      custom_name: reviewStartedAt
    shipped_at:
      custom_name: shippedAt
    shipping_address:
      custom_name: shippingAddress
    shipping_price:
      custom_name: shippingPrice
    status:
      custom_name: status
    subtotal:
      custom_name: subtotal
    subtotal_before_discount:
      custom_name: subtotalBeforeDiscount
    taxes:
      custom_name: taxes
    total:
      custom_name: total
    total_discount:
      custom_name: totalDiscount
    total_taxes:
      custom_name: totalTaxes
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    assigned_user_id: assignedUserId
    client_id: clientId
    company_id: companyId
    created_at: createdAt
    delivery_date: deliveryDate
    id: id
    notes: notes
    read_id: readId
    review_started_at: reviewStartedAt
    shipped_at: shippedAt
    shipping_address: shippingAddress
    shipping_price: shippingPrice
    status: status
    subtotal: subtotal
    subtotal_before_discount: subtotalBeforeDiscount
    taxes: taxes
    total: total
    total_discount: totalDiscount
    total_taxes: totalTaxes
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: assignedUser
    using:
      foreign_key_constraint_on: assigned_user_id
  - name: client
    using:
      foreign_key_constraint_on: client_id
  - name: company
    using:
      foreign_key_constraint_on: company_id
array_relationships:
  - name: sale_order_items
    using:
      foreign_key_constraint_on:
        column: sale_order_id
        table:
          name: sale_order_item
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - id
        - read_id
        - client_id
        - company_id
        - assigned_user_id
        - status
        - delivery_date
        - shipped_at
        - review_started_at
        - subtotal_before_discount
        - subtotal
        - total_discount
        - total_taxes
        - total
        - shipping_price
        - shipping_address
        - notes
        - taxes
        - created_at
        - updated_at
      filter:
        company_id:
          _eq: x-Hasura-Company-Id
      limit: 20
      allow_aggregations: true
    comment: ""
