table:
  name: quote
  schema: public
configuration:
  column_config:
    assigned_user_id:
      custom_name: assignedUserId
    client_id:
      custom_name: clientId
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    promised_delivery_date:
      custom_name: promisedDeliveryDate
    read_id:
      custom_name: readId
    shipping_address:
      custom_name: shippingAddress
    shipping_price:
      custom_name: shippingPrice
    subtotal_before_discount:
      custom_name: subtotalBeforeDiscount
    total_discount:
      custom_name: totalDiscount
    total_taxes:
      custom_name: totalTaxes
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    assigned_user_id: assignedUserId
    client_id: clientId
    company_id: companyId
    created_at: createdAt
    promised_delivery_date: promisedDeliveryDate
    read_id: readId
    shipping_address: shippingAddress
    shipping_price: shippingPrice
    subtotal_before_discount: subtotalBeforeDiscount
    total_discount: totalDiscount
    total_taxes: totalTaxes
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: client
    using:
      foreign_key_constraint_on: client_id
  - name: company
    using:
      foreign_key_constraint_on: company_id
  - name: quoteClientFields
    using:
      manual_configuration:
        column_mapping:
          id: quote_id
        insertion_order: null
        remote_table:
          name: quote_client_fields
          schema: public
  - name: quoteItem
    using:
      manual_configuration:
        column_mapping:
          id: quote_id
        insertion_order: null
        remote_table:
          name: quote_item
          schema: public
  - name: quoteProviderFields
    using:
      manual_configuration:
        column_mapping:
          id: quote_id
        insertion_order: null
        remote_table:
          name: quote_provider_fields
          schema: public
  - name: user
    using:
      foreign_key_constraint_on: assigned_user_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - read_id
        - shipping_address
        - taxes
        - shipping_price
        - subtotal
        - subtotal_before_discount
        - total
        - total_discount
        - total_taxes
        - created_at
        - promised_delivery_date
        - updated_at
        - status
        - assigned_user_id
        - client_id
        - company_id
        - id
      filter:
        _or:
          - company_id:
              _eq: X-Hasura-Company-Id
          - client:
              company_id:
                _eq: X-Hasura-Company-Id
    comment: ""
