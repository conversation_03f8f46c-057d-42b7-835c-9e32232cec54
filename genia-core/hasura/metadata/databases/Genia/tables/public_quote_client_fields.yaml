table:
  name: quote_client_fields
  schema: public
configuration:
  column_config:
    created_at:
      custom_name: createdAt
    quote_id:
      custom_name: quoteId
    related_purchase_order_id:
      custom_name: relatedPurchaseOrderId
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    created_at: createdAt
    quote_id: quoteId
    related_purchase_order_id: relatedPurchaseOrderId
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: purchaseOrder
    using:
      manual_configuration:
        column_mapping:
          related_purchase_order_id: id
        insertion_order: null
        remote_table:
          name: purchase_order
          schema: public
  - name: quote
    using:
      foreign_key_constraint_on: quote_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - notes
        - created_at
        - updated_at
        - id
        - quote_id
        - related_purchase_order_id
      filter:
        quote:
          client:
            company:
              id:
                _eq: X-Hasura-Company-Id
    comment: ""
