table:
  name: catalog_discount_client
  schema: public
configuration:
  column_config:
    catalog_discount_id:
      custom_name: catalogDiscountId
    client_id:
      custom_name: clientId
  custom_column_names:
    catalog_discount_id: catalogDiscountId
    client_id: clientId
  custom_root_fields: {}
object_relationships:
  - name: catalog_discount
    using:
      foreign_key_constraint_on: catalog_discount_id
  - name: client
    using:
      foreign_key_constraint_on: client_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - catalog_discount_id
        - client_id
      filter:
        client:
          company_id:
            _eq: X-Hasura-Company-Id
    comment: ""
