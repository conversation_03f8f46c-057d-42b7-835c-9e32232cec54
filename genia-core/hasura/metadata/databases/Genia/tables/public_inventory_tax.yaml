table:
  name: inventory_tax
  schema: public
configuration:
  column_config:
    created_at:
      custom_name: createdAt
    inventory_id:
      custom_name: inventoryId
    tax_id:
      custom_name: taxId
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    created_at: createdAt
    inventory_id: inventoryId
    tax_id: taxId
    updated_at: updatedAt
  custom_name: inventoryTax
  custom_root_fields: {}
object_relationships:
  - name: inventory
    using:
      foreign_key_constraint_on: inventory_id
  - name: tax
    using:
      foreign_key_constraint_on: tax_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - created_at
        - updated_at
        - inventory_id
        - tax_id
      filter:
        inventory:
          company_id:
            _eq: X-Hasura-Company-Id
      limit: 100
    comment: ""
