table:
  name: inventory_catalog
  schema: public
configuration:
  column_config:
    catalog_id:
      custom_name: catalogId
    inventory_id:
      custom_name: inventoryId
  custom_column_names:
    catalog_id: catalogId
    inventory_id: inventoryId
  custom_root_fields: {}
object_relationships:
  - name: catalog
    using:
      foreign_key_constraint_on: catalog_id
  - name: inventory
    using:
      manual_configuration:
        column_mapping:
          inventory_id: id
        insertion_order: null
        remote_table:
          name: inventory
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - catalog_id
        - id
        - inventory_id
        - quantity
      filter:
        catalog:
          company_id:
            _eq: X-Hasura-Company-Id
    comment: ""
