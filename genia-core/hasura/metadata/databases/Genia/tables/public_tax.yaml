table:
  name: tax
  schema: public
configuration:
  column_config:
    country_code:
      custom_name: countryCode
    created_at:
      custom_name: createdAt
    disabled_at:
      custom_name: disabledAt
    id:
      custom_name: id
    name:
      custom_name: name
    type:
      custom_name: type
    updated_at:
      custom_name: updatedAt
    value:
      custom_name: value
  custom_column_names:
    country_code: countryCode
    created_at: createdAt
    disabled_at: disabledAt
    id: id
    name: name
    type: type
    updated_at: updatedAt
    value: value
  custom_root_fields: {}
array_relationships:
  - name: inventoryTaxes
    using:
      foreign_key_constraint_on:
        column: tax_id
        table:
          name: inventory_tax
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - country_code
        - name
        - value
        - created_at
        - disabled_at
        - updated_at
        - type
        - id
      filter: {}
      limit: 10
      allow_aggregations: true
    comment: ""
