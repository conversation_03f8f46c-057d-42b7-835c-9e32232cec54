table:
  name: connection_invitation
  schema: public
configuration:
  column_config:
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    invited_company_id:
      custom_name: invitedCompanyId
    phone_number:
      custom_name: phoneNumber
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    company_id: companyId
    created_at: createdAt
    invited_company_id: invitedCompanyId
    phone_number: phoneNumber
    updated_at: updatedAt
  custom_root_fields: {}
select_permissions:
  - role: read_user
    permission:
      columns:
        - email
        - phone_number
        - created_at
        - updated_at
        - state
        - type
        - company_id
        - id
        - invited_company_id
      filter:
        _or:
          - company_id:
              _eq: X-Hasura-Company-Id
          - invited_company_id:
              _eq: X-Hasura-Company-Id
    comment: ""
