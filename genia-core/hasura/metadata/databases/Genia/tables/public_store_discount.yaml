table:
  name: store_discount
  schema: public
configuration:
  column_config:
    company_id:
      custom_name: companyId
    disabled_at:
      custom_name: disabledAt
    discount_type:
      custom_name: discountType
    discount_value:
      custom_name: discountValue
    end_date:
      custom_name: endDate
    required_amount:
      custom_name: requiredAmount
    start_date:
      custom_name: startDate
  custom_column_names:
    company_id: companyId
    disabled_at: disabledAt
    discount_type: discountType
    discount_value: discountValue
    end_date: endDate
    required_amount: requiredAmount
    start_date: startDate
  custom_root_fields: {}
object_relationships:
  - name: company
    using:
      manual_configuration:
        column_mapping:
          company_id: id
        insertion_order: null
        remote_table:
          name: company
          schema: public
array_relationships:
  - name: storeDiscountClients
    using:
      foreign_key_constraint_on:
        column: store_discount_id
        table:
          name: store_discount_client
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - name
        - discount_value
        - required_amount
        - disabled_at
        - end_date
        - start_date
        - discount_type
        - company_id
        - id
      filter:
        company_id:
          _eq: X-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
