table:
  name: quote_item
  schema: public
configuration:
  column_config:
    catalog_id:
      custom_name: catalogId
    created_at:
      custom_name: createdAt
    discount_type:
      custom_name: discountType
    discount_value:
      custom_name: discountValue
    product_id:
      custom_name: productId
    quote_id:
      custom_name: quoteId
    unit_price:
      custom_name: unitPrice
    unit_price_after_discount:
      custom_name: unitPriceAfterDiscount
    unit_price_after_discount_and_taxes:
      custom_name: unitPriceAfterDiscountAndTaxes
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    catalog_id: catalogId
    created_at: createdAt
    discount_type: discountType
    discount_value: discountValue
    product_id: productId
    quote_id: quoteId
    unit_price: unitPrice
    unit_price_after_discount: unitPriceAfterDiscount
    unit_price_after_discount_and_taxes: unitPriceAfterDiscountAndTaxes
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: quote
    using:
      foreign_key_constraint_on: quote_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - name
        - product_id
        - quantity
        - taxes
        - discount_value
        - subtotal
        - total
        - unit_price
        - unit_price_after_discount
        - unit_price_after_discount_and_taxes
        - created_at
        - updated_at
        - discount_type
        - catalog_id
        - quote_id
      filter:
        quote:
          company_id:
            _eq: X-Hasura-Company-Id
    comment: ""
