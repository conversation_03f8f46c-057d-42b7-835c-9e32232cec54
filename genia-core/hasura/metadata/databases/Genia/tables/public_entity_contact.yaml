table:
  name: entity_contact
  schema: public
configuration:
  column_config:
    address_line_1:
      custom_name: addressLine1
    address_line_2:
      custom_name: addressLine2
    contact_name:
      custom_name: contactName
    created_at:
      custom_name: createdAt
    entity:
      custom_name: entity
    entity_id:
      custom_name: entityId
    phone_1:
      custom_name: phone1
    phone_2:
      custom_name: phone2
    phone_code_1:
      custom_name: phoneCode1
    phone_code_2:
      custom_name: phoneCode2
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    address_line_1: addressLine1
    address_line_2: addressLine2
    contact_name: contactName
    created_at: createdAt
    entity: entity
    entity_id: entityId
    phone_1: phone1
    phone_2: phone2
    phone_code_1: phoneCode1
    phone_code_2: phoneCode2
    updated_at: updatedAt
  custom_name: entityContact
  custom_root_fields: {}
select_permissions:
  - role: read_user
    permission:
      columns:
        - address_line_1
        - address_line_2
        - contact_name
        - created_at
        - entity
        - phone_1
        - phone_2
        - phone_code_1
        - phone_code_2
        - updated_at
      filter:
        _or:
          - _and:
              - entity:
                  _eq: company
              - entity_id:
                  _eq: X-Hasura-Company-Id
          - _and:
              - entity:
                  _eq: user
              - entity_id:
                  _eq: X-Hasura-User-Id
    comment: ""
