table:
  name: attribute
  schema: public
configuration:
  column_config:
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    id:
      custom_name: id
    name:
      custom_name: name
  custom_column_names:
    company_id: companyId
    created_at: createdAt
    id: id
    name: name
  custom_root_fields: {}
object_relationships:
  - name: company
    using:
      foreign_key_constraint_on: company_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - id
        - name
        - company_id
        - created_at
      filter:
        company_id:
          _eq: X-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
