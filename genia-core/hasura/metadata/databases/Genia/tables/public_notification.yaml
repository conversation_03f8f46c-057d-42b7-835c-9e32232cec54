table:
  name: notification
  schema: public
configuration:
  column_config:
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    owner_user_id:
      custom_name: ownerUserId
    required_roles:
      custom_name: requiredRoles
  custom_column_names:
    company_id: companyId
    created_at: createdAt
    owner_user_id: ownerUserId
    required_roles: requiredRoles
  custom_root_fields: {}
object_relationships:
  - name: company
    using:
      foreign_key_constraint_on: company_id
  - name: user
    using:
      foreign_key_constraint_on: owner_user_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - required_roles
        - payload
        - created_at
        - type
        - company_id
        - id
        - owner_user_id
      filter:
        company_id:
          _eq: X-Hasura-Company-Id
    comment: ""
