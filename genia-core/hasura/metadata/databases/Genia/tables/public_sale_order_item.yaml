table:
  name: sale_order_item
  schema: public
configuration:
  column_config:
    catalog_id:
      custom_name: catalogId
    created_at:
      custom_name: createdAt
    discount_type:
      custom_name: discountType
    discount_value:
      custom_name: discountValue
    name:
      custom_name: name
    product_id:
      custom_name: productId
    quantity:
      custom_name: quantity
    sale_order_id:
      custom_name: saleOrderId
    subtotal:
      custom_name: subtotal
    taxes:
      custom_name: taxes
    total:
      custom_name: total
    unit_price:
      custom_name: unitPrice
    unit_price_after_discount:
      custom_name: unitPriceAfterDiscount
    unit_price_after_discount_and_taxes:
      custom_name: unitPriceAfterDiscountAndTaxes
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    catalog_id: catalogId
    created_at: createdAt
    discount_type: discountType
    discount_value: discountValue
    name: name
    product_id: productId
    quantity: quantity
    sale_order_id: saleOrderId
    subtotal: subtotal
    taxes: taxes
    total: total
    unit_price: unitPrice
    unit_price_after_discount: unitPriceAfterDiscount
    unit_price_after_discount_and_taxes: unitPriceAfterDiscountAndTaxes
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: catalog_media
    using:
      manual_configuration:
        column_mapping:
          catalog_id: catalog_id
        insertion_order: null
        remote_table:
          name: catalog_media
          schema: public
  - name: sale_order
    using:
      foreign_key_constraint_on: sale_order_id
array_relationships:
  - name: inventoryCatalogs
    using:
      manual_configuration:
        column_mapping:
          catalog_id: catalog_id
        insertion_order: null
        remote_table:
          name: inventory_catalog
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - catalog_id
        - created_at
        - discount_type
        - discount_value
        - name
        - product_id
        - quantity
        - sale_order_id
        - subtotal
        - taxes
        - total
        - unit_price
        - unit_price_after_discount
        - unit_price_after_discount_and_taxes
        - updated_at
      filter:
        sale_order:
          company_id:
            _eq: X-Hasura-Company-Id
      limit: 20
      allow_aggregations: true
    comment: ""
