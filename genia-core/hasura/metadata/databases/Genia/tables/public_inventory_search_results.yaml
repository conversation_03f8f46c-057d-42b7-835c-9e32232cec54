table:
  name: inventory_search_results
  schema: public
configuration:
  column_config:
    attributes:
      custom_name: attributes
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    description:
      custom_name: description
    disabled_at:
      custom_name: disabledAt
    has_stock_validation:
      custom_name: hasStockValidation
    id:
      custom_name: id
    measurement_unit:
      custom_name: measurementUnit
    name:
      custom_name: name
    restricted_stock:
      custom_name: restrictedStock
    sku:
      custom_name: sku
    standard_identifier:
      custom_name: standardIdentifier
    stock:
      custom_name: stock
    type:
      custom_name: type
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    attributes: attributes
    company_id: companyId
    created_at: createdAt
    description: description
    disabled_at: disabledAt
    has_stock_validation: hasStockValidation
    id: id
    measurement_unit: measurementUnit
    name: name
    restricted_stock: restrictedStock
    sku: sku
    standard_identifier: standardIdentifier
    stock: stock
    type: type
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: company
    using:
      manual_configuration:
        column_mapping:
          company_id: id
        insertion_order: null
        remote_table:
          name: company
          schema: public
array_relationships:
  - name: inventoryHistories
    using:
      manual_configuration:
        column_mapping:
          id: inventory_id
        insertion_order: null
        remote_table:
          name: inventory_history
          schema: public
  - name: inventoryMedia
    using:
      manual_configuration:
        column_mapping:
          id: inventory_id
        insertion_order: null
        remote_table:
          name: inventory_media
          schema: public
  - name: inventoryTaxes
    using:
      manual_configuration:
        column_mapping:
          id: inventory_id
        insertion_order: null
        remote_table:
          name: inventory_tax
          schema: public
select_permissions:
  - role: read:hasura
    permission:
      columns:
        - attributes
        - company_id
        - created_at
        - description
        - disabled_at
        - id
        - measurement_unit
        - name
        - sku
        - standard_identifier
        - stock
        - type
        - updated_at
        - rank
        - restricted_stock
      filter: {}
      limit: 20
    comment: ""
  - role: read_user
    permission:
      columns:
        - attributes
        - company_id
        - created_at
        - description
        - disabled_at
        - has_stock_validation
        - id
        - measurement_unit
        - name
        - rank
        - sku
        - standard_identifier
        - stock
        - type
        - updated_at
        - restricted_stock
      filter:
        company_id:
          _eq: X-Hasura-Company-Id
      limit: 100
      allow_aggregations: true
    comment: ""
