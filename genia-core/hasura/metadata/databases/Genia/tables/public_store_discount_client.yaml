table:
  name: store_discount_client
  schema: public
configuration:
  column_config:
    client_id:
      custom_name: clientId
    store_discount_id:
      custom_name: storeDiscountId
  custom_column_names:
    client_id: clientId
    store_discount_id: storeDiscountId
  custom_root_fields: {}
object_relationships:
  - name: client
    using:
      foreign_key_constraint_on: client_id
  - name: store_discount
    using:
      manual_configuration:
        column_mapping:
          store_discount_id: id
        insertion_order: null
        remote_table:
          name: store_discount
          schema: public
select_permissions:
  - role: read_user
    permission:
      columns:
        - client_id
        - store_discount_id
      filter:
        client:
          company_id:
            _eq: X-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
