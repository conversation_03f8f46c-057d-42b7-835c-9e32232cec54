table:
  name: user_company
  schema: public
configuration:
  column_config:
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    role:
      custom_name: role
    updated_at:
      custom_name: updatedAt
    user_id:
      custom_name: userId
  custom_column_names:
    company_id: companyId
    created_at: createdAt
    role: role
    updated_at: updatedAt
    user_id: userId
  custom_name: userCompany
  custom_root_fields: {}
object_relationships:
  - name: company
    using:
      foreign_key_constraint_on: company_id
  - name: user
    using:
      foreign_key_constraint_on: user_id
select_permissions:
  - role: read:hasura
    permission:
      columns:
        - company_id
        - role
        - user_id
      filter: {}
    comment: ""
  - role: read_user
    permission:
      columns:
        - company_id
        - created_at
        - role
        - updated_at
        - user_id
      filter:
        _and:
          - company_id:
              _eq: X-Hasura-Company-Id
          - user_id:
              _eq: X-Hasura-User-Id
      limit: 100
      allow_aggregations: true
    comment: ""
