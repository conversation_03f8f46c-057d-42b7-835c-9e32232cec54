table:
  name: purchase_order_item_inventory
  schema: public
object_relationships:
  - name: inventory
    using:
      foreign_key_constraint_on: inventory_id
  - name: purchase_order_item
    using:
      foreign_key_constraint_on: purchase_order_item_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - inventory_id
        - purchase_order_item_id
      filter:
        inventory:
          company_id:
            _eq: X-Hasura-Company-Id
      limit: 100
      allow_aggregations: true
    comment: ""
