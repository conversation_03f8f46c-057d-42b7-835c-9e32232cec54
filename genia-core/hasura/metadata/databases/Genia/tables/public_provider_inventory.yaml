table:
  name: provider_inventory
  schema: public
configuration:
  column_config:
    current_discount:
      custom_name: currentDiscount
    current_purchase_price:
      custom_name: currentPurchasePrice
    inventory_id:
      custom_name: inventoryId
    provider_id:
      custom_name: providerId
    provider_product_sku:
      custom_name: providerProductSku
  custom_column_names:
    current_discount: currentDiscount
    current_purchase_price: currentPurchasePrice
    inventory_id: inventoryId
    provider_id: providerId
    provider_product_sku: providerProductSku
  custom_root_fields: {}
object_relationships:
  - name: inventory
    using:
      foreign_key_constraint_on: inventory_id
  - name: provider
    using:
      foreign_key_constraint_on: provider_id
select_permissions:
  - role: read_user
    permission:
      columns:
        - provider_product_sku
        - current_discount
        - current_purchase_price
        - inventory_id
        - provider_id
      filter:
        inventory:
          company_id:
            _eq: X-Hasura-Company-Id
      limit: 100
      allow_aggregations: true
    comment: ""
