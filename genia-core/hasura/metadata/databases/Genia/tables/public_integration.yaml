table:
  name: integration
  schema: public
configuration:
  column_config:
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    id:
      custom_name: id
    params:
      custom_name: params
    token:
      custom_name: token
    token_type:
      custom_name: tokenType
    type:
      custom_name: type
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    company_id: companyId
    created_at: createdAt
    id: id
    params: params
    token: token
    token_type: tokenType
    type: type
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: company
    using:
      foreign_key_constraint_on: company_id
select_permissions:
  - role: read:hasura
    permission:
      columns:
        - company_id
        - id
        - params
        - token
        - type
      filter: {}
    comment: ""
  - role: read_user
    permission:
      columns:
        - company_id
        - created_at
        - id
        - params
        - type
        - updated_at
      filter:
        company_id:
          _eq: X-Hasura-Company-Id
      limit: 20
    comment: ""
