- name: Genia
  kind: postgres
  configuration:
    connection_info:
      database_url:
        from_env: PG_DATABASE_URL
      isolation_level: read-committed
      use_prepared_statements: false
  logical_models:
    - fields:
        - name: company_id
          type:
            nullable: true
            scalar: uuid
        - name: data_points
          type:
            nullable: true
            scalar: jsonb
      name: sale_orders_total_by_client
      select_permissions:
        - permission:
            columns:
              - company_id
              - data_points
            filter:
              company_id:
                _eq: X-Hasura-Company-Id
          role: read_user
    - fields:
        - name: period_total
          type:
            nullable: true
            scalar: numeric
        - name: data_points
          type:
            nullable: true
            scalar: jsonb
        - name: company_id
          type:
            nullable: false
            scalar: uuid
      name: sale_orders_total_by_periodicity
      select_permissions:
        - permission:
            columns:
              - company_id
              - data_points
              - period_total
            filter:
              company_id:
                _eq: X-Hasura-Company-Id
          role: read_user
  native_queries:
    - arguments:
        company_id:
          description: ""
          nullable: false
          type: uuid
      code: "WITH client_totals AS (\r\n  SELECT\r\n    c.id,\r\n    c.name,\r\n    COALESCE(SUM(so.total), 0) AS total\r\n  FROM public.client c\r\n  LEFT JOIN public.sale_order so\r\n    ON so.client_id = c.id\r\n    AND so.status = 'completed'\r\n    AND so.company_id = {{company_id}}::uuid\r\n  GROUP BY c.id, c.name\r\n)\r\n\r\nSELECT\r\n  {{company_id}}::uuid AS company_id,\r\n  (\r\n    SELECT jsonb_agg(\r\n      json_build_object(\r\n        'id', ct.id,\r\n        'name', ct.name,\r\n        'total', ct.total\r\n      )\r\n      ORDER BY ct.total DESC\r\n    )\r\n    FROM client_totals ct\r\n  ) AS data_points;"
      returns: sale_orders_total_by_client
      root_field_name: get_sale_orders_totals_by_clients
    - arguments:
        company_id:
          description: ""
          nullable: false
          type: uuid
      code: |-
        WITH range_dates AS (
          SELECT
            generate_series(
              (now() - interval '29 days')::date,
              now()::date,
              interval '1 day'
            ) AS period_point
        ),

        data AS (
          SELECT
            date_trunc('day', created_at)::date AS period_point,
            SUM(total) AS total
          FROM public.sale_order
          WHERE
            company_id = {{company_id}}
            AND status IS DISTINCT FROM 'cancelled'
            AND created_at >= (now() - interval '29 days')
            AND created_at < now()
          GROUP BY period_point
        )

        SELECT
          {{company_id}}::uuid AS company_id,
          (
            SELECT COALESCE(SUM(total), 0)
            FROM data
          ) AS period_total,

          (
            SELECT jsonb_agg(json_build_object(
              'period', TO_CHAR(r.period_point, 'YYYY-MM-DD'),
              'total', COALESCE(d.total, 0)
            ) ORDER BY r.period_point)
            FROM range_dates r
            LEFT JOIN data d ON r.period_point = d.period_point
          ) AS data_points;
      returns: sale_orders_total_by_periodicity
      root_field_name: get_sale_orders_totals_by_last_30_days
    - arguments:
        company_id:
          description: ""
          nullable: false
          type: uuid
      code: "WITH range_dates AS (\r\n  SELECT generate_series(\r\n    date_trunc('month', now() - interval '1 month')::date,\r\n    (date_trunc('month', now()) - interval '1 day')::date,\r\n    interval '1 day'\r\n  ) AS period_point\r\n),\r\ndata AS (\r\n  SELECT\r\n    date_trunc('day', created_at)::date AS period_point,\r\n    SUM(total) AS total\r\n  FROM public.sale_order\r\n  WHERE\r\n    company_id = {{company_id}}\r\n    AND status IS DISTINCT FROM 'cancelled'\r\n    AND created_at >= date_trunc('month', now() - interval '1 month')\r\n    AND created_at < date_trunc('month', now())\r\n  GROUP BY period_point\r\n)\r\n\r\nSELECT\r\n  {{company_id}}::uuid AS company_id,\r\n  (\r\n    SELECT COALESCE(SUM(total), 0)\r\n    FROM data\r\n  ) AS period_total,\r\n  (\r\n    SELECT jsonb_agg(json_build_object(\r\n      'period', TO_CHAR(r.period_point, 'YYYY-MM-DD'),\r\n      'total', COALESCE(d.total, 0)\r\n    ) ORDER BY r.period_point)\r\n    FROM range_dates r\r\n    LEFT JOIN data d ON r.period_point = d.period_point\r\n  ) AS data_points;"
      returns: sale_orders_total_by_periodicity
      root_field_name: get_sale_orders_totals_by_last_month
    - arguments:
        company_id:
          description: ""
          nullable: false
          type: uuid
      code: |-
        WITH range_dates AS (
          SELECT
            generate_series(
              date_trunc('month', now() - interval '12 months'),
              now(),
              interval '1 month'
            ) AS period_point
        ),

        data AS (
          SELECT
            date_trunc('month', created_at) AS period_point,
            SUM(total) AS total
          FROM public.sale_order
          WHERE
            company_id = {{company_id}}
            AND status IS DISTINCT FROM 'cancelled'
            AND created_at >= (now() - interval '12 months')
            AND created_at < now()
          GROUP BY period_point
        )

        SELECT
          {{company_id}}::uuid AS company_id,
          (
            SELECT COALESCE(SUM(total), 0)
            FROM data
          ) AS period_total,

          (
            SELECT jsonb_agg(json_build_object(
              'period', TO_CHAR(r.period_point, 'YYYY-MM'),
              'total', COALESCE(d.total, 0)
            ) ORDER BY r.period_point)
            FROM range_dates r
            LEFT JOIN data d ON r.period_point = d.period_point
          ) AS data_points;
      returns: sale_orders_total_by_periodicity
      root_field_name: get_sale_orders_totals_by_last_year
    - arguments:
        company_id:
          description: ""
          nullable: false
          type: uuid
      code: |-
        WITH range_dates AS (
          SELECT
            generate_series(
              date_trunc('quarter', now() - interval '24 months'),
              date_trunc('quarter', now()),
              interval '3 months'
            ) AS period_point
        ),

        data AS (
          SELECT
            date_trunc('quarter', created_at) AS quarter_point,
            TO_CHAR(created_at, 'YYYY') AS year,
            EXTRACT(QUARTER FROM created_at)::int AS quarter,
            SUM(total) AS total
          FROM public.sale_order
          WHERE
            company_id = {{company_id}}
            AND status IS DISTINCT FROM 'cancelled'
            AND created_at >= (now() - interval '24 months')
            AND created_at < now()
          GROUP BY quarter_point, year, quarter
        ),

        quarters AS (
          SELECT
            TO_CHAR(r.period_point, 'YYYY') || '-Q' || EXTRACT(QUARTER FROM r.period_point)::int AS period,
            r.period_point
          FROM range_dates r
        ),

        data_agg AS (
          SELECT
            TO_CHAR(d.quarter_point, 'YYYY') || '-Q' || d.quarter AS period,
            SUM(d.total) AS total
          FROM data d
          GROUP BY period
        )

        SELECT
          {{company_id}}::uuid AS company_id,
          (
            SELECT COALESCE(SUM(total), 0)
            FROM data_agg
          ) AS period_total,

          (
            SELECT jsonb_agg(json_build_object(
              'period', q.period,
              'total', COALESCE(a.total, 0)
            ) ORDER BY q.period_point)
            FROM quarters q
            LEFT JOIN data_agg a ON q.period = a.period
          ) AS data_points;
      returns: sale_orders_total_by_periodicity
      root_field_name: get_sale_orders_totals_by_quarter
    - arguments:
        company_id:
          description: ""
          nullable: false
          type: uuid
      code: |-
        WITH range_dates AS (
          SELECT
            generate_series(
              date_trunc('month', now() - interval '24 months'),
              date_trunc('month', now()),
              interval '6 months'
            ) AS period_point
        ),

        data AS (
          SELECT
            date_trunc('month', created_at) AS month_point,
            EXTRACT(YEAR FROM created_at) AS year,
            CEIL(EXTRACT(MONTH FROM created_at)::int / 6.0)::int AS semester,
            SUM(total) AS total
          FROM public.sale_order
          WHERE
            company_id = {{company_id}}
            AND status IS DISTINCT FROM 'cancelled'
            AND created_at >= (now() - interval '24 months')
            AND created_at < now()
          GROUP BY month_point, year, semester
        ),

        semesters AS (
          SELECT
            TO_CHAR(date_trunc('month', r.period_point), 'YYYY') || '-S' || CEIL(EXTRACT(MONTH FROM r.period_point)::int / 6.0)::int AS period,
            r.period_point
          FROM range_dates r
        ),

        data_agg AS (
          SELECT
            TO_CHAR(date_trunc('month', d.month_point), 'YYYY') || '-S' || d.semester AS period,
            SUM(d.total) AS total
          FROM data d
          GROUP BY period
        )

        SELECT
          {{company_id}}::uuid AS company_id,
          (
            SELECT COALESCE(SUM(total), 0)
            FROM data_agg
          ) AS period_total,

          (
            SELECT jsonb_agg(json_build_object(
              'period', s.period,
              'total', COALESCE(a.total, 0)
            ) ORDER BY s.period_point)
            FROM semesters s
            LEFT JOIN data_agg a ON s.period = a.period
          ) AS data_points;
      returns: sale_orders_total_by_periodicity
      root_field_name: get_sale_orders_totals_by_semester
    - arguments:
        company_id:
          description: ""
          nullable: false
          type: uuid
      code: |-
        WITH range_dates AS (
          SELECT
            generate_series(
              date_trunc('month', now())::date,
              now()::date,
              interval '1 day'
            ) AS period_point
        ),

        data AS (
          SELECT
            date_trunc('day', created_at)::date AS period_point,
            SUM(total) AS total
          FROM public.sale_order
          WHERE
            company_id = {{company_id}}
            AND status IS DISTINCT FROM 'cancelled'
            AND created_at >= date_trunc('month', now())
            AND created_at < now()
          GROUP BY period_point
        )

        SELECT
          {{company_id}}::uuid AS company_id,
          (
            SELECT COALESCE(SUM(total), 0)
            FROM data
          ) AS period_total,

          (
            SELECT jsonb_agg(json_build_object(
              'period', TO_CHAR(r.period_point, 'YYYY-MM-DD'),
              'total', COALESCE(d.total, 0)
            ) ORDER BY r.period_point)
            FROM range_dates r
            LEFT JOIN data d ON r.period_point = d.period_point
          ) AS data_points;
      returns: sale_orders_total_by_periodicity
      root_field_name: get_sale_orders_totals_by_this_month
  tables: "!include Genia/tables/tables.yaml"
  functions: "!include Genia/functions/functions.yaml"
