{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Launch server", "runtimeExecutable": "pnpm", "runtimeArgs": ["run", "start:dev"], "cwd": "${workspaceFolder}", "outputCapture": "std", "restart": true, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"type": "node", "request": "launch", "name": "test - current file", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/jest", "runtimeArgs": ["${file}", "--runInBand", "--<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "--forceExit", "--test<PERSON><PERSON>", "**/*.test.ts"], "cwd": "${workspaceFolder}", "outputCapture": "std", "restart": true, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}