export interface CreateQuoteEntriesOrderItem {
  catalogId: string;
  quantity: number;
}

export interface CreateQuoteEntriesProviderFields {
  notes: string;
  relatedSaleOrderId: string | null;
}

interface CreateQuoteEntriesCommand {
  companyId: string;
  entries : {
    assignedUserId: string;
    clientId: string;
    shippingAddress: string | null;
    shippingPrice: number | null;
    promisedDeliveryDate: Date | null;
    orderItems: CreateQuoteEntriesOrderItem[];
    providerFields: CreateQuoteEntriesProviderFields | null;
  }[];
}

export default CreateQuoteEntriesCommand;
