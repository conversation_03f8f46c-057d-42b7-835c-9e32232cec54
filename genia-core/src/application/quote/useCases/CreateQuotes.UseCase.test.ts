import CreateQuoteEntriesCommand from '#application/quote/commands/CreateQuoteEntriesCommand';
import Registry from '#composition/ImplementationRegistry';
import { QuoteStatus } from '#domain/aggregates/quote/Quote.Entity';
import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';

import CreateQuotesUseCase from './CreateQuotes.UseCase';

jest.unmock('./CreateQuotes.UseCase');

const mockClient = {
  id: 'client1',
  companyId: 'comp1',
  clientCompanyId: null,
};

const mockCatalog = {
  id: 'cat1',
  readId: 'prod1',
  name: 'Product 1',
  companyId: 'comp1',
};

const now = new Date();

const mockConditions = {
  subtotalBeforeDiscount: 100,
  subtotal: 100,
  totalDiscount: 0,
  totalTaxes: 16,
  total: 116,
  taxes: [
    {
      name: 'VAT', value: 0.16, type: TaxType.PERCENTAGE, amount: 16,
    },
  ],
  orderItems: [
    {
      catalogId: 'cat1',
      quantity: 2,
      unitPrice: 50,
      unitPriceAfterDiscount: 50,
      unitPriceAfterDiscountAndTaxes: 58,
      subtotal: 100,
      total: 116,
      taxes: [{
        name: 'VAT', value: 0.16, type: TaxType.PERCENTAGE, amount: 16,
      }],
      discounts: {
        appliedDiscount: {
          catalogDiscount: {
            discountValue: 0,
            discountType: DiscountType.AMOUNT,
          },
        },
      },
    },
  ],
};

jest.mock('#application/common/order/Order.UseCase', () => ({
  getCatalogConditions: jest.fn(() => mockConditions.orderItems),
}));

jest.mock('#domain/domainServices/OrderConditions.DomainService', () => ({
  calculateConditions: jest.fn(({ orderItems }) => ({
    subtotalBeforeDiscount: mockConditions.subtotalBeforeDiscount,
    subtotal: mockConditions.subtotal,
    totalDiscount: mockConditions.totalDiscount,
    totalTaxes: mockConditions.totalTaxes,
    total: mockConditions.total,
    taxes: mockConditions.taxes,
    orderItems,
  })),
}));

describe('CreateQuotes.UseCase.apply', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create a quote successfully', async () => {
    (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue(mockClient);
    (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalog]);
    (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValue('quote-id-1');
    (Registry.IdentificationService.generateObjectSequenceForCompany as jest.Mock).mockResolvedValue('Q-001');
    (Registry.QuoteRepository.save as jest.Mock).mockResolvedValue([
      {
        id: 'quote-id-1',
        readId: 'Q-001',
        companyId: 'comp1',
        clientId: 'client1',
        assignedUserId: 'user1',
        status: QuoteStatus.DRAFT,
        subtotalBeforeDiscount: 100,
        subtotal: 100,
        totalDiscount: 0,
        totalTaxes: 16,
        total: 116,
        shippingPrice: 0,
        shippingAddress: '123 Main St',
        notes: null,
        taxes: [
          {
            name: 'VAT', value: 0.16, type: TaxType.PERCENTAGE, amount: 16,
          },
        ],
        createdAt: now,
        updatedAt: now,
        promisedDeliveryDate: now,
        orderItems: [
          {
            catalogId: 'cat1',
            productId: 'prod1',
            name: 'Product 1',
            quantity: 2,
            unitPrice: 50,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 58,
            subtotal: 100,
            total: 116,
            discount: { value: 0, type: DiscountType.AMOUNT },
            taxes: [{
              name: 'VAT', value: 0.16, type: TaxType.PERCENTAGE, amount: 16,
            }],
            createdAt: now,
            updatedAt: now,
          },
        ],
        providerFields: null,
        clientFields: null,
      },
    ]);

    const command: CreateQuoteEntriesCommand = {
      entries: [
        {
          orderItems: [
            {
              catalogId: 'cat1',
              quantity: 2,
            },
          ],
          clientId: 'client1',
          promisedDeliveryDate: now,
          shippingAddress: '123 Main St',
          shippingPrice: 0,
          providerFields: null,
          assignedUserId: 'user1',
        },
      ],
      companyId: 'comp1',
    };

    const result = await CreateQuotesUseCase.apply(command);

    expect(Registry.ClientRepository.findOneById).toHaveBeenCalledWith('client1');
    expect(Registry.CatalogRepository.findManyByIds).toHaveBeenCalledWith(['cat1']);
    expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
    expect(Registry.IdentificationService.generateObjectSequenceForCompany).toHaveBeenCalledWith('comp1', 'quote');
    expect(Registry.QuoteRepository.save).toHaveBeenCalled();
    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      id: 'quote-id-1',
      readId: 'Q-001',
      companyId: 'comp1',
      clientId: 'client1',
      assignedUserId: 'user1',
      status: QuoteStatus.DRAFT,
      subtotalBeforeDiscount: 100,
      subtotal: 100,
      totalDiscount: 0,
      totalTaxes: 16,
      total: 116,
      shippingPrice: 0,
      shippingAddress: '123 Main St',
      promisedDeliveryDate: now,
    });
    expect(result[0].orderItems.length).toBe(1);
    expect(result[0].orderItems[0].catalogId).toBe('cat1');
  });

  it('should throw if client is not found', async () => {
    (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue(null);

    const command: CreateQuoteEntriesCommand = {
      entries: [
        {
          orderItems: [
            {
              catalogId: 'cat1',
              quantity: 2,
            },
          ],
          clientId: 'client1',
          promisedDeliveryDate: now,
          shippingAddress: '123 Main St',
          shippingPrice: 0,
          assignedUserId: 'user1',
          providerFields: null,
        },
      ],
      companyId: 'comp1',
    };

    await expect(CreateQuotesUseCase.apply(command)).rejects.toThrow('Client not found');
  });

  it('should throw if client belongs to other company', async () => {
    (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue({ ...mockClient, companyId: 'other-company' });

    const command: CreateQuoteEntriesCommand = {
      entries: [
        {
          orderItems: [
            {
              catalogId: 'cat1',
              quantity: 2,
            },
          ],
          clientId: 'client1',
          promisedDeliveryDate: now,
          shippingAddress: '123 Main St',
          shippingPrice: 0,
          assignedUserId: 'user1',
          providerFields: null,
        },
      ],
      companyId: 'comp1',
    };

    await expect(CreateQuotesUseCase.apply(command)).rejects.toThrow('Client does not belong to company');
  });

  it('should throw if the company is not provider for the client', async () => {
    (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue({ ...mockClient, clientCompanyId: 'comp2' });
    (Registry.ProviderRepository.findProviderForCompanyByProviderCompanyId as jest.Mock).mockResolvedValue(undefined);

    const command: CreateQuoteEntriesCommand = {
      entries: [
        {
          orderItems: [
            {
              catalogId: 'cat1',
              quantity: 2,
            },
          ],
          clientId: 'client1',
          promisedDeliveryDate: now,
          shippingAddress: '123 Main St',
          shippingPrice: 0,
          assignedUserId: 'user1',
          providerFields: null,
        },
      ],
      companyId: 'comp1',
    };

    await expect(CreateQuotesUseCase.apply(command)).rejects.toThrow('Company comp1 is not a provider for client comp2');
  });

  it('should throw if catalog item does not exist', async () => {
    (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue(mockClient);
    (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

    const command: CreateQuoteEntriesCommand = {
      entries: [
        {
          orderItems: [
            {
              catalogId: 'cat1',
              quantity: 2,
            },
          ],
          clientId: 'client1',
          promisedDeliveryDate: now,
          shippingAddress: '123 Main St',
          shippingPrice: 0,
          assignedUserId: 'user1',
          providerFields: null,
        },
      ],
      companyId: 'comp1',
    };

    await expect(CreateQuotesUseCase.apply(command)).rejects.toThrow('Catalog item cat1 not found');
  });

  it('should throw if catalog item does not belong to company', async () => {
    (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue(mockClient);
    (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([
      { ...mockCatalog, companyId: 'other-company' },
    ]);

    const command: CreateQuoteEntriesCommand = {
      entries: [
        {
          orderItems: [
            {
              catalogId: 'cat1',
              quantity: 2,
            },
          ],
          clientId: 'client1',
          promisedDeliveryDate: now,
          shippingAddress: '123 Main St',
          shippingPrice: 0,
          assignedUserId: 'user1',
          providerFields: null,
        },
      ],
      companyId: 'comp1',
    };

    await expect(CreateQuotesUseCase.apply(command)).rejects.toThrow('Catalog item does not belong to company');
  });
});
