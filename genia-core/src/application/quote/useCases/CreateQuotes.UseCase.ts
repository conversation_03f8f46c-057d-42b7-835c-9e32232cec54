import OrderUseCase from '#application/common/order/Order.UseCase';
import CreateQuoteEntriesCommand from '#application/quote/commands/CreateQuoteEntriesCommand';
import { CalculateSaleOrderConditionsOrderItem } from '#application/saleOrder/commands/CalculateSaleOrderConditions.Command';
import CalculateSaleOrderConditionsResult from '#application/saleOrder/results/CalculateSaleOrderConditions.Result';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import CatalogEntity from '#domain/aggregates/catalog/Catalog.Entity';
import ClientEntity from '#domain/aggregates/client/Client.Entity';
import QuoteEntity, { QuoteStatus } from '#domain/aggregates/quote/Quote.Entity';
import QuoteOperator, { QuoteBuildParams, QuoteItemBuildParams } from '#domain/aggregates/quote/Quote.Operator';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import OrderConditionsDomainService from '#domain/domainServices/OrderConditions.DomainService';

async function validateAndGetClient(clientId: string, companyId: string): Promise<ClientEntity> {
  const client = await Registry.ClientRepository.findOneById(clientId);

  if (!client) throw new Error('Client not found', { cause: Cause.BAD_REQUEST });

  if (client.companyId !== companyId) {
    throw new Error('Client does not belong to company', { cause: Cause.BAD_REQUEST });
  }

  return client;
}

async function validateProvider(clientCompanyId: string, providerCompanyId: string): Promise<void> {
  const provider = await Registry.ProviderRepository.findProviderForCompanyByProviderCompanyId(clientCompanyId, providerCompanyId);

  if (!provider) throw new Error(`Company ${providerCompanyId} is not a provider for client ${clientCompanyId}`, { cause: Cause.NOT_FOUND });
}

async function getConditionsForClient(
  client: ClientEntity,
  companyId: string,
  orderItems: CalculateSaleOrderConditionsOrderItem[],
  shippingPrice: number,
): Promise<CalculateSaleOrderConditionsResult> {
  const catalogConditions = await OrderUseCase.getCatalogConditions(orderItems, companyId, client);

  const orderConditions = OrderConditionsDomainService.calculateConditions({
    orderItems: catalogConditions,
    shippingPrice,
  });

  return {
    ...orderConditions,
    orderItems: catalogConditions.map((item, i) => ({
      ...item,
      ...orderConditions.orderItems[i],
    })),
  };
}

async function apply(command: CreateQuoteEntriesCommand): Promise<QuoteEntity[]> {
  const { entries, companyId } = command;

  const entities = await Promise.all(entries.map(async (entry) => {
    const {
      orderItems, clientId, promisedDeliveryDate, providerFields, shippingAddress, shippingPrice, assignedUserId,
    } = entry;

    const client = await validateAndGetClient(clientId, companyId);

    if (client.clientCompanyId) await validateProvider(client.clientCompanyId, companyId);

    const conditions = await getConditionsForClient(
      client,
      companyId,
      orderItems,
      shippingPrice || 0,
    );

    const catalog = await Registry.CatalogRepository.findManyByIds(orderItems.map((item) => item.catalogId));
    const catalogMap = new Map(catalog.map((item) => [item.id, item]));

    orderItems.forEach((item) => {
      const catalogItem = catalogMap.get(item.catalogId);

      if (!catalogItem) {
        throw new Error(`Catalog item ${item.catalogId} not found`, { cause: Cause.NOT_FOUND });
      }

      if (catalogItem.companyId !== companyId) {
        throw new Error('Catalog item does not belong to company', { cause: Cause.BAD_REQUEST });
      }
    });

    return Registry.TransactionService.transactional(async () => {
      const id = await Registry.IdentificationService.generateId();
      const readId = await Registry.IdentificationService.generateObjectSequenceForCompany(companyId, 'quote');

      const items: QuoteItemBuildParams[] = conditions.orderItems.map((item) => {
        const appliedDiscount = item.discounts?.appliedDiscount.catalogDiscount || item.discounts?.appliedDiscount.storeDiscount;
        const discount = appliedDiscount ? {
          value: appliedDiscount.discountValue,
          type: appliedDiscount.discountType === DiscountType.PERCENTAGE ? DiscountType.PERCENTAGE : DiscountType.AMOUNT,
        } : null;

        return {
          catalogId: item.catalogId,
          productId: (catalogMap.get(item.catalogId) as CatalogEntity).readId,
          discount,
          name: (catalogMap.get(item.catalogId) as CatalogEntity).name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          unitPriceAfterDiscount: item.unitPriceAfterDiscount,
          unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscountAndTaxes,
          subtotal: item.subtotal,
          total: item.total,
          taxes: item.taxes,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      });

      const buildParams: QuoteBuildParams = {
        id,
        readId,
        companyId,
        clientId,
        assignedUserId,
        shippingAddress,
        status: QuoteStatus.DRAFT,
        subtotalBeforeDiscount: conditions.subtotalBeforeDiscount,
        totalDiscount: conditions.totalDiscount,
        subtotal: conditions.subtotal,
        totalTaxes: conditions.totalTaxes,
        shippingPrice: shippingPrice || undefined,
        total: conditions.total,
        taxes: conditions.taxes,
        orderItems: items,
        promisedDeliveryDate,
        providerFields,
        clientFields: null,
        createdAt: new Date(),
      };

      const newQuote = QuoteOperator.build(buildParams);

      const [result] = await Registry.QuoteRepository.save([newQuote]);

      return result;
    }).catch((error) => {
      throw error;
    });
  }));

  return entities as unknown as QuoteEntity[];
}

export default {
  apply,
};
