import UserEntity from '#domain/aggregates/user/User.Entity';

export type CreateUser = Omit<UserEntity, 'id'>;

interface UserRepository {
  findOneByEmail(email: string): Promise<UserEntity | undefined>;
  findOneByPhoneNumber(phoneNumber: string): Promise<UserEntity | undefined>;
  createOne(user: CreateUser): Promise<UserEntity>;
  saveInvitedBy(invitedUsers: UserEntity[], userId: string): Promise<UserEntity[]>;
  save(users: UserEntity[]): Promise<UserEntity[]>;
  findOneById(id: string): Promise<UserEntity | undefined>;
  update(user: UserEntity): Promise<void>;
  findEmails(emails: string[]): Promise<string[]>
}

export default UserRepository;
