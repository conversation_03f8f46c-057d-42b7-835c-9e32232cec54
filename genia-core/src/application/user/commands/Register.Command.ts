import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';

interface RegisterCommand {
  user: {
    email: string;
    name: string;
    lastName: string;
    phoneNumber: string;
  }
  company: {
    name: string;
    description?: string;
    tributaryId: string;
    country: string;
    contactInformation: ContactInformationValueObject | null;
  };
}

export default RegisterCommand;
