import { ApplicationError } from '#application/Common.Type';
import InviteUsersToCompanyCommand from '#application/user/commands/InviteUserToCompany.Command';
import Registry from '#composition/ImplementationRegistry';
import { UserRole } from '#domain/aggregates/user/User.Entity';
import UserOperator from '#domain/aggregates/user/User.Operator';

import InviteUsersToCompanyUseCase from './InviteUsersToCompany.UseCase';

jest.unmock('./InviteUsersToCompany.UseCase');
jest.unmock('#application/Common.Type');
jest.unmock('#application/Transaction.Service');

describe('InviteUsersToCompanyUseCase', () => {
  const command: InviteUsersToCompanyCommand = {
    companyId: 'company-1',
    userId: 'admin-user-id',
    invitedUsers: [
      {
        email: '<EMAIL>', name: 'User1', lastName: 'One', role: UserRole.USER, phoneNumber: '1234567890',
      },
      {
        email: '<EMAIL>', name: 'User2', lastName: 'Two', role: UserRole.ADMIN, phoneNumber: '0987654321',
      },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (Registry.UserRepository.findEmails as jest.Mock).mockResolvedValue([]);
    (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValue({
      id: 'admin-user-id',
      role: UserRole.ADMIN,
      companies: ['company-1'],
    });
    (Registry.IdentificationService.generateId as jest.Mock)
      .mockResolvedValueOnce('new-user-id-1')
      .mockResolvedValueOnce('new-user-id-2');
    (UserOperator.build as jest.Mock).mockImplementation((params) => ({ ...params }));
    (Registry.UserRepository.saveInvitedBy as jest.Mock).mockResolvedValue([{ name: 'John', lastName: 'Doe' }]);
    (Registry.CompanyRepository.findOneById as jest.Mock).mockResolvedValue({ name: 'suplif.ai' });
    (Registry.TransactionService.transactional as jest.Mock).mockImplementation(async (fn) => fn());
  });

  describe('apply', () => {
    it('should invite users correctly', async () => {
      const result = await InviteUsersToCompanyUseCase.apply(command);

      expect(result).toEqual([expect.objectContaining({ name: 'John', lastName: 'Doe' })]);

      expect(Registry.UserRepository.findEmails).toHaveBeenCalledWith(['<EMAIL>', '<EMAIL>']);
      expect(Registry.IdentificationService.generateId).toHaveBeenCalledTimes(2);
      expect(UserOperator.build).toHaveBeenCalledTimes(2);

      expect(Registry.TransactionService.transactional).toHaveBeenCalled();
      expect(Registry.UserRepository.saveInvitedBy).toHaveBeenCalledWith([
        expect.objectContaining({ email: '<EMAIL>', companies: ['company-1'] }),
        expect.objectContaining({ email: '<EMAIL>', companies: ['company-1'] }),
      ], 'admin-user-id');
    });

    it('should throw ApplicationError if any email already exists', async () => {
      (Registry.UserRepository.findEmails as jest.Mock).mockResolvedValue(['<EMAIL>']);

      await expect(InviteUsersToCompanyUseCase.apply(command)).rejects.toThrow(ApplicationError);

      expect(Registry.UserRepository.saveInvitedBy).not.toHaveBeenCalled();
    });

    it('should throw ApplicationError if user does not have permission to invite', async () => {
      (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValue({
        id: 'admin-user-id',
        role: UserRole.USER,
        companies: ['company-1'],
      });

      await expect(InviteUsersToCompanyUseCase.apply(command)).rejects.toThrow(
        'User with ID admin-user-id does not have permission to invite users to company company-1.',
      );

      expect(Registry.UserRepository.saveInvitedBy).not.toHaveBeenCalled();
    });
  });
});
