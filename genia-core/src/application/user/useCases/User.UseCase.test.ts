import UserUseCase from '#application/user/useCases/User.UseCase';
import Registry from '#composition/ImplementationRegistry';
import Logger from '#composition/Logger';
import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import CompanyOperator from '#domain/aggregates/company/Company.Operator';
import UserEntity, { UserRole, UserState } from '#domain/aggregates/user/User.Entity';
import UserOperator from '#domain/aggregates/user/User.Operator';

import RegisterCommand from '../commands/Register.Command';

jest.unmock('./User.UseCase');

describe('UserUseCase', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const email = '<EMAIL>';
    const name = 'John';
    const lastName = 'Doe';
    const phoneNumber = '1234567890';
    const companyName = 'Test Company';
    const description = 'Test Description';
    const tributaryId = '12345678';
    const country = 'US';

    const registerCommand: RegisterCommand = {
      user: {
        email,
        name,
        lastName,
        phoneNumber,
      },
      company: {
        name: companyName,
        description,
        tributaryId,
        country,
        contactInformation: null,
      },
    };

    const mockCompany: CompanyEntity = {
      id: 'company-id',
      name: companyName,
      description,
      tributaryId,
      country,
      contactInformation: null,
    };

    const mockUser: UserEntity = {
      id: 'user-id',
      email,
      name,
      lastName,
      state: UserState.ACTIVE,
      phoneNumber,
      companies: ['company-id'],
      role: UserRole.ADMIN,
    };

    it('should register a new user and create a new company', async () => {
      // Mock repository methods
      jest.spyOn(Registry.UserRepository, 'findOneByEmail').mockResolvedValue(undefined);
      jest.spyOn(Registry.UserRepository, 'findOneByPhoneNumber').mockResolvedValue(undefined);
      jest.spyOn(Registry.CompanyRepository, 'findOneByTributaryIdAndCountry').mockResolvedValue(undefined);
      jest.spyOn(Registry.IdentificationService, 'generateId')
        .mockResolvedValueOnce('company-id')
        .mockResolvedValueOnce('user-id');
      jest.spyOn(Registry.CompanyRepository, 'save').mockResolvedValue(mockCompany);
      jest.spyOn(Registry.UserRepository, 'createOne').mockResolvedValue(mockUser);
      jest.spyOn(CompanyOperator, 'build').mockReturnValue(mockCompany);
      jest.spyOn(UserOperator, 'build').mockReturnValue(mockUser);
      jest.spyOn(Registry.TransactionService, 'transactional').mockImplementation(async (callback) => callback());

      // Execute
      const result = await UserUseCase.register(registerCommand);

      // Assert
      expect(Registry.UserRepository.findOneByEmail).toHaveBeenCalledWith(email);
      expect(Registry.UserRepository.findOneByPhoneNumber).toHaveBeenCalledWith(phoneNumber);
      expect(Registry.CompanyRepository.findOneByTributaryIdAndCountry).toHaveBeenCalledWith(tributaryId, country);
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();
      expect(CompanyOperator.build).toHaveBeenCalledWith({
        id: 'company-id',
        name: companyName,
        description,
        tributaryId,
        country,
        contactInformation: null,
      });
      expect(UserOperator.build).toHaveBeenCalledWith({
        id: 'user-id',
        email,
        companies: ['company-id'],
        lastName,
        name,
        role: UserRole.ADMIN,
        phoneNumber,
      });
      expect(Registry.CompanyRepository.save).toHaveBeenCalledWith(mockCompany);
      expect(Registry.UserRepository.createOne).toHaveBeenCalledWith(mockUser);
      expect(result).toEqual({ user: mockUser, company: mockCompany });
    });

    it('should throw an error if the email already exists', async () => {
      // Mock repository methods
      jest.spyOn(Registry.UserRepository, 'findOneByEmail').mockResolvedValue(mockUser);

      // Execute and assert
      await expect(UserUseCase.register(registerCommand))
        .rejects.toThrow('Email already exists');
    });

    it('should throw an error if the phone number already exists', async () => {
      // Mock repository methods
      jest.spyOn(Registry.UserRepository, 'findOneByEmail').mockResolvedValue(undefined);
      jest.spyOn(Registry.UserRepository, 'findOneByPhoneNumber').mockResolvedValue(mockUser);

      // Execute and assert
      await expect(UserUseCase.register(registerCommand))
        .rejects.toThrow('Phone number already exists');
    });

    it('should throw an error if the company already exists', async () => {
      // Mock repository methods
      jest.spyOn(Registry.UserRepository, 'findOneByEmail').mockResolvedValue(undefined);
      jest.spyOn(Registry.UserRepository, 'findOneByPhoneNumber').mockResolvedValue(undefined);
      jest.spyOn(Registry.CompanyRepository, 'findOneByTributaryIdAndCountry').mockResolvedValue(mockCompany);

      // Execute and assert
      await expect(UserUseCase.register(registerCommand))
        .rejects.toThrow(`Company with ${tributaryId} already exists`);
    });

    it('should handle transaction errors and log them', async () => {
      // Mock repository methods
      jest.spyOn(Registry.UserRepository, 'findOneByEmail').mockResolvedValue(undefined);
      jest.spyOn(Registry.UserRepository, 'findOneByPhoneNumber').mockResolvedValue(undefined);
      jest.spyOn(Registry.CompanyRepository, 'findOneByTributaryIdAndCountry').mockResolvedValue(undefined);
      jest.spyOn(Registry.TransactionService, 'transactional').mockRejectedValue(new Error('Transaction failed'));

      // Execute and assert
      await expect(UserUseCase.register(registerCommand))
        .rejects.toThrow('Error registering user');

      expect(Logger.getLogger().error).toHaveBeenCalledWith('Error registering user', { registerCommand });
    });
  });

  describe('retrieveUser', () => {
    it('should retrieve a user by ID', async () => {
      const userId = 'test-user-id';
      const mockUser: UserEntity = { id: userId, name: 'Test User' } as UserEntity;

      // Mock repository methods
      jest.spyOn(Registry.UserRepository, 'findOneById').mockResolvedValue(mockUser);

      // Execute
      const result = await UserUseCase.retrieveUser({ userId });

      // Assert
      expect(Registry.UserRepository.findOneById).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockUser);
    });

    it('should throw an error if the user is not found', async () => {
      const userId = 'non-existent-id';

      // Mock repository methods
      jest.spyOn(Registry.UserRepository, 'findOneById').mockResolvedValue(undefined);

      // Execute and assert
      expect(UserUseCase.retrieveUser({ userId })).rejects.toThrow(`User with ID ${userId} not found`);

      expect(Registry.UserRepository.findOneById).toHaveBeenCalledWith(userId);
    });
  });
});
