import { ApplicationError } from '#application/Common.Type';
import InviteUsersToCompanyCommand from '#application/user/commands/InviteUserToCompany.Command';
import UserErrorCodes from '#application/user/User.ErrorCodes';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import UserEntity, { UserRole } from '#domain/aggregates/user/User.Entity';
import UserOperator from '#domain/aggregates/user/User.Operator';

async function apply(command: InviteUsersToCompanyCommand): Promise<UserEntity[]> {
  const emails = command.invitedUsers.map((user) => user.email);
  const existingEmails = await Registry.UserRepository.findEmails(emails);

  if (existingEmails.length) {
    throw new ApplicationError(`Some users where already invited with emails: ${existingEmails.join(', ')}`, Cause.CONFLICT, UserErrorCodes.EMAIL_CONFLICT);
  }

  const foundUser = (await Registry.UserRepository.findOneById(command.userId))!;

  if (foundUser.role !== UserRole.ADMIN) {
    throw new ApplicationError(
      `User with ID ${command.userId} does not have permission to invite users to company ${command.companyId}.`,
      Cause.FORBIDDEN,
      UserErrorCodes.COMPANY_USER_FORBIDDEN,
    );
  }

  const company = (await Registry.CompanyRepository.findOneById(command.companyId))!;

  return Registry.TransactionService.transactional(async () => {
    const usersToSave: UserEntity[] = await Promise.all(command.invitedUsers.map(async (user) => {
      const id = await Registry.IdentificationService.generateId();

      return UserOperator.build({ ...user, id, companies: [command.companyId] });
    }));

    const users = await Registry.UserRepository.saveInvitedBy(usersToSave, command.userId);

    await Promise.all(users.map(async (user) => Registry.EmailService.sendInvitation(`${foundUser!.name} ${foundUser!.lastName}`, company.name, user.email)));

    return users;
  });
}

export default {
  apply,
};
