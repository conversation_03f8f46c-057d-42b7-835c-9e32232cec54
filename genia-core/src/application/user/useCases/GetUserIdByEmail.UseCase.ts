import { ApplicationError } from '#application/Common.Type';
import GetUserIdByEmailCommand from '#application/user/commands/GetUserIdByEmail.Command';
import UserErrorCodes from '#application/user/User.ErrorCodes';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';

async function apply(command: GetUserIdByEmailCommand): Promise<string> {
  const { email } = command;
  const user = await Registry.UserRepository.findOneByEmail(email);

  if (!user) {
    throw new ApplicationError(`User with email ${email} not found`, Cause.NOT_FOUND, UserErrorCodes.USER_NOT_FOUND);
  }

  return user.id;
}

export default {
  apply,
};
