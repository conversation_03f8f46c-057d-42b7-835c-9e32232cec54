import { ApplicationError } from '#application/Common.Type';
import ErrorsMapper from '#application/Errors.Mapper';
import Registry from '#composition/ImplementationRegistry';
import UserOperator from '#domain/aggregates/user/User.Operator';

import UpdateUserUseCase from './UpdateUser.UseCase';

jest.unmock('./UpdateUser.UseCase');
jest.unmock('#application/Common.Type');

describe('UpdateUserUseCase.apply', () => {
  const user = {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Test',
    lastName: 'User',
    phoneNumber: '1234567890',
    companies: ['company-1'],
    role: 'admin',
  };

  const updaterUser = {
    ...user,
    id: 'user-2',
    email: '<EMAIL>',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (Registry.UserRepository.findOneById as jest.Mock).mockImplementation((id: string) => {
      if (id === user.id) return Promise.resolve(user);
      if (id === updaterUser.id) return Promise.resolve(updaterUser);
      return Promise.resolve(undefined);
    });
    (Registry.UserRepository.save as jest.Mock).mockImplementation(async (users) => users);
    (UserOperator.updateByUser as jest.Mock).mockImplementation((u, updates) => ({
      ...u,
      ...updates,
    }));
  });

  it('should update user when updater is the same user', async () => {
    const command = {
      userId: user.id,
      updaterUserId: user.id,
      updates: { name: 'Updated Name' },
    };

    const result = await UpdateUserUseCase.apply(command);

    expect(Registry.UserRepository.findOneById).toHaveBeenCalledWith(user.id);
    expect(UserOperator.updateByUser).toHaveBeenCalledWith(user, command.updates, user);
    expect(Registry.UserRepository.save).toHaveBeenCalledWith([expect.objectContaining({ name: 'Updated Name' })]);
    expect(result.name).toBe('Updated Name');
  });

  it('should update user when updater is different user', async () => {
    const command = {
      userId: user.id,
      updaterUserId: updaterUser.id,
      updates: { lastName: 'Updated LastName' },
    };

    const result = await UpdateUserUseCase.apply(command);

    expect(Registry.UserRepository.findOneById).toHaveBeenCalledWith(user.id);
    expect(Registry.UserRepository.findOneById).toHaveBeenCalledWith(updaterUser.id);
    expect(UserOperator.updateByUser).toHaveBeenCalledWith(user, command.updates, updaterUser);
    expect(Registry.UserRepository.save).toHaveBeenCalledWith([expect.objectContaining({ lastName: 'Updated LastName' })]);
    expect(result.lastName).toBe('Updated LastName');
  });

  it('should throw ApplicationError if user is not found', async () => {
    (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValueOnce(undefined);

    const command = {
      userId: 'non-existent',
      updaterUserId: 'non-existent',
      updates: { name: 'Name' },
    };

    await expect(UpdateUserUseCase.apply(command)).rejects.toThrow(ApplicationError);
    await expect(UpdateUserUseCase.apply(command)).rejects.toThrow('User with ID non-existent not found.');
  });

  it('should throw ApplicationError if updater user is not found', async () => {
    (Registry.UserRepository.findOneById as jest.Mock)
      .mockResolvedValueOnce(user) // user found
      .mockResolvedValueOnce(undefined); // updater not found

    const command = {
      userId: user.id,
      updaterUserId: 'non-existent',
      updates: { name: 'Name' },
    };

    await expect(UpdateUserUseCase.apply(command)).rejects.toThrow(ApplicationError);
    await expect(UpdateUserUseCase.apply(command)).rejects.toThrow('User with ID user-1 not found.');
  });

  it('should map and throw error from UserOperator.updateByUser', async () => {
    (UserOperator.updateByUser as jest.Mock).mockImplementation(() => { throw new Error('Update not allowed'); });
    (ErrorsMapper.map as jest.Mock).mockReturnValueOnce(new Error('mapper error'));

    const command = {
      userId: user.id,
      updaterUserId: user.id,
      updates: { name: 'Name' },
    };

    await expect(UpdateUserUseCase.apply(command)).rejects.toThrow('mapper error');
  });
});
