import Registry from '#composition/ImplementationRegistry';

import GetUserIdByEmailUseCase from './GetUserIdByEmail.UseCase';

jest.unmock('./GetUserIdByEmail.UseCase');
jest.unmock('#application/Common.Type');

describe('GetUserIdByEmailUseCase.apply', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return user id when user exists', async () => {
    const mockUser = { id: 'user-123', email: '<EMAIL>' };
    (Registry.UserRepository.findOneByEmail as jest.Mock).mockResolvedValue(mockUser);

    const result = await GetUserIdByEmailUseCase.apply({ email: '<EMAIL>' });
    expect(result).toBe('user-123');
    expect(Registry.UserRepository.findOneByEmail).toHaveBeenCalledWith('<EMAIL>');
  });

  it('should throw ApplicationError when user does not exist', async () => {
    (Registry.UserRepository.findOneByEmail as jest.Mock).mockResolvedValue(undefined);

    await expect(GetUserIdByEmailUseCase.apply({ email: '<EMAIL>' })).rejects.toThrow('User <NAME_EMAIL> not found');
    expect(Registry.UserRepository.findOneByEmail).toHaveBeenCalledWith('<EMAIL>');
  });
});
