import { ApplicationError } from '#application/Common.Type';
import ErrorsMapper from '#application/Errors.Mapper';
import UpdateUserCommand from '#application/user/commands/UpdateUser.Command';
import UserErrorCodes from '#application/user/User.ErrorCodes';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import UserEntity from '#domain/aggregates/user/User.Entity';
import UserOperator from '#domain/aggregates/user/User.Operator';

async function apply(command: UpdateUserCommand): Promise<UserEntity> {
  const { userId, updates, updaterUserId } = command;

  const user = await Registry.UserRepository.findOneById(userId);
  if (!user) {
    throw new ApplicationError(`User with ID ${userId} not found.`, Cause.NOT_FOUND, UserErrorCodes.USER_NOT_FOUND);
  }

  const updaterUser = userId !== updaterUserId
    ? await Registry.UserRepository.findOneById(updaterUserId)
    : user;

  if (!updaterUser) {
    throw new ApplicationError(`User with ID ${userId} not found.`, Cause.NOT_FOUND, UserErrorCodes.USER_NOT_FOUND);
  }

  try {
    const updatedUser = UserOperator.updateByUser(user, updates, updaterUser);

    const [savedUser] = await Registry.UserRepository.save([updatedUser]);

    return savedUser;
  } catch (error) {
    const mappedError = ErrorsMapper.map(error);

    throw mappedError;
  }
}

export default {
  apply,
};
