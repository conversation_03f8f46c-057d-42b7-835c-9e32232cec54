import { ApplicationError } from '#application/Common.Type';
import UpdateCompanyCommand from '#application/company/commands/UpdateCompanyEntry.Command';
import CompanyErrorCodes from '#application/company/Company.ErrorCodes';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import CompanyOperator from '#domain/aggregates/company/Company.Operator';
import { UserRole } from '#domain/aggregates/user/User.Entity';

async function apply(command: UpdateCompanyCommand): Promise<CompanyEntity> {
  const { id, userId, ...updates } = command;

  const user = (await Registry.UserRepository.findOneById(userId))!;

  const { companies: [userCompanyId], role } = user;

  if (id !== userCompanyId) {
    throw new ApplicationError('User does not belong to the company being updated', Cause.FORBIDDEN, CompanyErrorCodes.COMPANY_FORBIDDEN);
  }

  if (role !== UserRole.ADMIN) {
    throw new ApplicationError('User does not have permission to update the company', Cause.FORBIDDEN, CompanyErrorCodes.COMPANY_FORBIDDEN);
  }

  const company = await Registry.CompanyRepository.findOneById(id);

  if (!company) {
    throw new ApplicationError('Company not found', Cause.NOT_FOUND, CompanyErrorCodes.COMPANY_NOT_FOUND);
  }

  const { tributaryId: oldTributaryId, country } = company;
  const { tributaryId: newTributaryId } = updates;

  if (newTributaryId && newTributaryId !== oldTributaryId) {
    const existingCompanies = await Registry.CompanyRepository.findManyByTributaryIdAndCountry(newTributaryId, country);

    const companiesWithSameTributaryId = existingCompanies.filter((c) => c.id !== id);

    if (companiesWithSameTributaryId.length) {
      throw new ApplicationError('Another company with the same tributary ID already exists in the country', Cause.CONFLICT, CompanyErrorCodes.COMPANY_CONFLICT);
    }
  }

  const updated = CompanyOperator.update(company, updates);

  return Registry.CompanyRepository.save(updated);
}

const UpdateCompanyUseCase = { apply };

export default UpdateCompanyUseCase;
