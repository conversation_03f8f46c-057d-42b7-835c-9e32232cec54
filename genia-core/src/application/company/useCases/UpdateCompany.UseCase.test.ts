import CompanyErrorCodes from '#application/company/Company.ErrorCodes';
import Registry from '#composition/ImplementationRegistry';
import CompanyOperator from '#domain/aggregates/company/Company.Operator';
import { UserRole } from '#domain/aggregates/user/User.Entity';

import UpdateCompanyUseCase from './UpdateCompany.UseCase';

jest.unmock('#application/Common.Type');

jest.unmock('./UpdateCompany.UseCase');

describe('UpdateCompanyUseCase.apply', () => {
  const mockUser = {
    id: 'user-1',
    companies: ['company-1'],
    role: UserRole.ADMIN,
  };

  const mockCompany = {
    id: 'company-1',
    name: 'Test Company',
    tributaryId: 'TAX123',
    country: 'US',
    description: 'Test Description',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValue(mockUser);
    (Registry.CompanyRepository.findOneById as jest.Mock).mockResolvedValue(mockCompany);
    (Registry.CompanyRepository.findManyByTributaryIdAndCountry as jest.Mock).mockResolvedValue([mockCompany]);
    (Registry.CompanyRepository.save as jest.Mock).mockImplementation((company) => Promise.resolve(company));
    (CompanyOperator.update as jest.Mock).mockImplementation((c, updates) => ({ ...c, ...updates }));
  });

  it('should update company successfully', async () => {
    const command = {
      id: 'company-1',
      userId: 'user-1',
      name: 'Updated Company',
      description: 'Updated Description',
    };

    const result = await UpdateCompanyUseCase.apply(command);

    expect(result.name).toBe('Updated Company');
    expect(result.description).toBe('Updated Description');
    expect(Registry.CompanyRepository.save).toHaveBeenCalled();
  });

  it('should throw error when user is not admin', async () => {
    (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValue({
      ...mockUser,
      role: UserRole.USER,
    });

    await expect(UpdateCompanyUseCase.apply({
      id: 'company-1',
      userId: 'user-1',
      name: 'Updated Company',
    })).rejects.toMatchObject({
      message: 'User does not have permission to update the company',
      code: CompanyErrorCodes.COMPANY_FORBIDDEN,
    });
  });

  it('should throw error when user does not belong to company', async () => {
    (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValue({
      ...mockUser,
      companies: ['different-company'],
    });

    await expect(UpdateCompanyUseCase.apply({
      id: 'company-1',
      userId: 'user-1',
      name: 'Updated Company',
    })).rejects.toMatchObject({
      message: 'User does not belong to the company being updated',
      code: CompanyErrorCodes.COMPANY_FORBIDDEN,
    });
  });

  it('should throw error when company not found', async () => {
    (Registry.CompanyRepository.findOneById as jest.Mock).mockResolvedValue(null);

    await expect(UpdateCompanyUseCase.apply({
      id: 'company-1',
      userId: 'user-1',
      name: 'Updated Company',
    })).rejects.toMatchObject({
      message: 'Company not found',
      code: CompanyErrorCodes.COMPANY_NOT_FOUND,
    });
  });

  it('should throw error when new tributaryId already exists for another company', async () => {
    const existingCompany = { ...mockCompany, tributaryId: 'TAX124', id: 'company-2' };
    (Registry.CompanyRepository.findManyByTributaryIdAndCountry as jest.Mock)
      .mockResolvedValue([existingCompany]);

    await expect(UpdateCompanyUseCase.apply({
      id: 'company-1',
      userId: 'user-1',
      tributaryId: 'TAX124',
    })).rejects.toMatchObject({
      message: 'Another company with the same tributary ID already exists in the country',
      code: CompanyErrorCodes.COMPANY_CONFLICT,
    });
  });

  it('should allow updating tributaryId when it belongs to same company', async () => {
    const command = {
      id: 'company-1',
      userId: 'user-1',
      tributaryId: 'NEW-TAX123',
    };

    (Registry.CompanyRepository.findManyByTributaryIdAndCountry as jest.Mock)
      .mockResolvedValue([mockCompany]);

    const result = await UpdateCompanyUseCase.apply(command);

    expect(result.tributaryId).toBe('NEW-TAX123');
    expect(Registry.CompanyRepository.save).toHaveBeenCalled();
  });
});
