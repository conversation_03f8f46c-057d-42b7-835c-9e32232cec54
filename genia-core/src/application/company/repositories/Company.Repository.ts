import CompanyEntity from '#domain/aggregates/company/Company.Entity';

interface CompanyRepository {
  save(company: CompanyEntity): Promise<CompanyEntity>;
  findOneByTributaryIdAndCountry(tributaryId: string, country: string): Promise<CompanyEntity | undefined>;
  findManyByTributaryIdAndCountry(tributaryId: string, country: string): Promise<CompanyEntity[]>;
  findManyByIds(ids: string[]): Promise<CompanyEntity[]>;
  findOneById(id: string): Promise<CompanyEntity | undefined>;
}

export default CompanyRepository;
