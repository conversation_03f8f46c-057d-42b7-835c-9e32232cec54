import { v4 } from 'uuid';

import OrderUseCase from '#application/common/order/Order.UseCase';
import CalculatePurchaseOrderConditionsCommand, {
  CalculatePurchaseOrderConditionsOrderItem,
} from '#application/purchaseOrders/commands/CalculatePurchaseOrderConditions.Command';
import CreatePurchaseOrderEntriesCommand, { CreatePurchaseOrderEntriesOrderItem } from '#application/purchaseOrders/commands/CreatePurchaseOrderEntriesCommand';
import { FindPurchaseOrderAvailableStatuses } from '#application/purchaseOrders/commands/FindPurchaseOrderAvailableStatuses.Command';
import ObtainInventorySuggestionsCommand from '#application/purchaseOrders/commands/ObtainInventorySuggestions.Command';
import UpdatePurchaseOrderEntryCommand from '#application/purchaseOrders/commands/UpdatePurchaseOrderEntry.Command';
import CalculatePurchaseOrderConditionsResult, { PurchaseOrderItemCalculated } from '#application/purchaseOrders/results/CalculatePurchaseOrderConditions.Result';
import { ObtainInventorySuggestionsResult } from '#application/purchaseOrders/results/ObtainInventorySuggestions.Result';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import CatalogEntity from '#domain/aggregates/catalog/Catalog.Entity';
import CatalogMediaEntity from '#domain/aggregates/catalogMedia/CatalogMedia.Entity';
import ClientEntity from '#domain/aggregates/client/Client.Entity';
import InventoryEntity from '#domain/aggregates/inventory/Inventory.Entity';
import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import PurchaseOrderEntity, {
  PurchaseOrderStatus,
} from '#domain/aggregates/purchaseOrder/PurchaseOrder.Entity';
import PurchaseOrderOperator, { PurchaseOrderBuildParams, PurchaseOrderItemBuildParams } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Operator';
import { SaleOrderStatus } from '#domain/aggregates/saleOrder/SaleOrder.Entity';
import SaleOrderOperator from '#domain/aggregates/saleOrder/SaleOrder.Operator';
import TaxOperator from '#domain/aggregates/tax/Tax.Operator';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import OrderDomainService from '#domain/domainServices/Order.DomainService';
import OrderConditionsDomainService from '#domain/domainServices/OrderConditions.DomainService';

async function validateAndGetClient(clientCompanyId: string, providerCompanyId: string): Promise<ClientEntity> {
  const clients = await Registry.ClientRepository.findManyByClientCompanyIds([clientCompanyId]);

  if (!clients.length) throw new Error('Client not found', { cause: Cause.NOT_FOUND });

  const client = clients.find((c) => c.companyId === providerCompanyId);

  if (!client) {
    throw new Error(`Company ${clientCompanyId} is not a client provider`, { cause: Cause.BAD_REQUEST });
  }

  return client;
}

async function validateAndGetProvider(providerId: string, companyId: string): Promise<ProviderEntity> {
  const provider = await Registry.ProviderRepository.findOneById(providerId);

  if (!provider) throw new Error(`Provider ${providerId} not found`, { cause: Cause.NOT_FOUND });

  if (provider.companyId !== companyId) {
    throw new Error('Provider does not belong to company', { cause: Cause.BAD_REQUEST });
  }

  return provider;
}

async function handleManualOrderItemConditions(orderItems: CalculatePurchaseOrderConditionsOrderItem[]): Promise<PurchaseOrderItemCalculated[]> {
  const uniqueTaxIds = new Set(orderItems.filter((orderItem) => orderItem.taxIds && orderItem.taxIds.length > 0).map((orderItem) => orderItem.taxIds as string[]).flat());
  const foundTaxes = uniqueTaxIds.size > 0 ? (await Registry.TaxRepository.findManyByIds(Array.from(uniqueTaxIds))).filter((tax) => !tax.disabledAt) : [];
  const taxMap = new Map(foundTaxes.map((tax) => [tax.id, tax]));

  const calculatedItems = orderItems.map((orderItem) => {
    const {
      referenceId, quantity, unitPrice, unitPriceAfterDiscount, taxIds,
    } = orderItem;

    if (!unitPrice || unitPrice <= 0) {
      throw new Error('Manual order item should have unitPrice greater than 0', { cause: Cause.BAD_REQUEST });
    }

    if (!unitPriceAfterDiscount || unitPriceAfterDiscount <= 0) {
      throw new Error('Manual order item should have unitPriceAfterDiscount greater than 0', { cause: Cause.BAD_REQUEST });
    }

    if (unitPriceAfterDiscount > unitPrice) {
      throw new Error('Manual order item should have unitPriceAfterDiscount less than or equal to unitPrice', { cause: Cause.BAD_REQUEST });
    }

    const taxes = (taxIds || []).map((taxId) => {
      const tax = taxMap.get(taxId);
      if (!tax) throw new Error(`Tax ${taxId} not found`, { cause: Cause.NOT_FOUND });

      return {
        id: tax.id,
        name: tax.name,
        type: tax.type,
        value: tax.value,
        amount: TaxOperator.calculateTaxAmount(tax, unitPriceAfterDiscount),
      };
    });

    const priceAfterTaxes = unitPriceAfterDiscount + taxes.reduce((acc, tax) => acc + tax.amount, 0);

    return {
      referenceId,
      quantity,
      unitPrice,
      unitPriceAfterDiscount,
      unitPriceAfterDiscountAndTaxes: priceAfterTaxes,
      discounts: null,
      taxes,
    };
  });

  return calculatedItems;
}

async function getConditionsForProvider(
  provider: ProviderEntity,
  companyId: string,
  orderItems: CalculatePurchaseOrderConditionsOrderItem[],
  shippingPrice: number,
): Promise<CalculatePurchaseOrderConditionsResult> {
  let resultOrderItems: PurchaseOrderItemCalculated[] = [];

  if (provider.providerCompanyId) {
    const client = await validateAndGetClient(companyId, provider.providerCompanyId);

    const catalogOrderItems = orderItems.map(({ referenceId, ...orderItem }) => ({
      ...orderItem,
      catalogId: referenceId,
    }));

    const catalogConditions = await OrderUseCase.getCatalogConditions(catalogOrderItems, provider.providerCompanyId, client);
    resultOrderItems = catalogConditions.map(({ catalogId, ...orderItem }) => ({
      referenceId: catalogId,
      ...orderItem,
    }));
  }

  if (!provider.providerCompanyId) {
    const manualConditions = await handleManualOrderItemConditions(orderItems);
    resultOrderItems = manualConditions;
  }

  const orderItemsParam = resultOrderItems.map(({ referenceId, ...item }) => ({
    ...item,
    catalogId: referenceId,
  }));

  const orderConditions = OrderConditionsDomainService.calculateConditions({ orderItems: orderItemsParam, shippingPrice });

  return {
    ...orderConditions,
    orderItems: orderConditions.orderItems.map(({ catalogId, ...conditionOrderItem }, i) => ({
      ...resultOrderItems[i],
      ...conditionOrderItem,
    })),
  };
}

async function calculatePurchaseOrderConditions(command: CalculatePurchaseOrderConditionsCommand): Promise<CalculatePurchaseOrderConditionsResult> {
  const {
    companyId, providerId, orderItems, shippingPrice = 0,
  } = command;

  if (orderItems.length === 0) {
    throw new Error('Order items not found', { cause: Cause.BAD_REQUEST });
  }

  const provider = await validateAndGetProvider(providerId, companyId);

  return getConditionsForProvider(
    provider,
    companyId,
    orderItems,
    shippingPrice,
  );
}

async function obtainInventorySuggestions(command: ObtainInventorySuggestionsCommand): Promise<ObtainInventorySuggestionsResult[]> {
  const { referenceIds, companyId, providerId } = command;

  const provider = await Registry.ProviderRepository.findOneById(providerId);

  if (!provider) throw new Error('Provider not found', { cause: Cause.NOT_FOUND });

  if (provider.companyId !== companyId) {
    throw new Error('Provider does not belong to company', { cause: Cause.FORBIDDEN });
  }

  if (!provider.providerCompanyId) {
    throw new Error('Inventory suggestions are only available for order items related to a provider with company', { cause: Cause.BAD_REQUEST });
  }

  const catalogItems = await Registry.CatalogRepository.findManyForCompanyByIds(provider.providerCompanyId, referenceIds);
  const catalogItemsMap = new Map(catalogItems.map((catalogItem) => [catalogItem.id, catalogItem]));

  referenceIds.forEach((referenceId) => {
    if (!catalogItemsMap.has(referenceId)) {
      throw new Error(`Provider catalog item ${referenceId} not found`, { cause: Cause.NOT_FOUND });
    }
  });

  const alreadyPurchasedInventoryFromProvider = await Registry.InventoryRepository.findManyForCompanyByProviderSkus(
    companyId,
    providerId,
    catalogItems.map((catalogItem) => catalogItem.readId),
  );

  const referenceResult: ObtainInventorySuggestionsResult[] = [];
  const catalogNotBoughtToProvider: CatalogEntity[] = [];

  catalogItems.forEach((catalogItem) => {
    const boughtInventories = alreadyPurchasedInventoryFromProvider.filter((inv) => inv.providers.some((p) => p.providerProductSku === catalogItem.readId));
    if (boughtInventories.length) {
      referenceResult.push({ inventoryIds: boughtInventories.map(({ id }) => id), referenceId: catalogItem.id });
    } else {
      catalogNotBoughtToProvider.push(catalogItem);
    }
  });

  const neverBoughtInventories = await Registry.InventoryRepository.findManyForCompanyBySkus(
    companyId,
    catalogNotBoughtToProvider.map((catalogItem) => catalogItem.readId),
  );

  catalogNotBoughtToProvider.forEach((catalogItem) => {
    const inventories = neverBoughtInventories.filter((inv) => inv.sku === catalogItem.readId);

    referenceResult.push({ inventoryIds: inventories.map(({ id }) => id), referenceId: catalogItem.id });
  });

  return referenceResult;
}

async function createPurchaseOrderEntries(command: CreatePurchaseOrderEntriesCommand): Promise<PurchaseOrderEntity[]> {
  const {
    entries, companyId, assignedUserId,
  } = command;

  const inventoryIds = entries.flatMap((entry) => entry.orderItems.flatMap((item) => item.inventoryIds));

  const inventory = await Registry.InventoryRepository.findManyByIds(inventoryIds);
  const inventorySet = new Set(inventory.map((inv) => inv.id));
  inventory.forEach((inv) => {
    if (inv.companyId !== companyId) {
      throw new Error(`Inventory ${inv.id} forbidden`, { cause: Cause.FORBIDDEN });
    }
  });

  inventoryIds.forEach((inventoryId) => {
    if (!inventorySet.has(inventoryId)) {
      throw new Error(`Inventory ${inventoryId} not available`, { cause: Cause.BAD_REQUEST });
    }
  });

  const entities = await Promise.all(entries.map(async (entry) => {
    const {
      orderItems, providerId, deliveryDate, notes, shippingAddress, shippingPrice, createInventoryFromOrderItems,
    } = entry;

    const provider = await validateAndGetProvider(providerId, companyId);

    let buildableOrderItem: (CreatePurchaseOrderEntriesOrderItem & {productId: string })[] = orderItems.map((item) => ({ ...item, productId: item.referenceId }));

    let client:ClientEntity | null = null;

    let catalogMap: Map<string, CatalogEntity & {media?: CatalogMediaEntity[]}> = new Map();
    if (provider.providerCompanyId) {
      const catalogItems = await Registry.CatalogRepository.findManyByIds(orderItems.map((item) => item.referenceId));
      const itemsMap = new Map(catalogItems.map((catalogItem) => [catalogItem.id, catalogItem]));
      catalogMap = new Map(catalogItems.map((catalogItem) => [catalogItem.readId, catalogItem]));

      const catalogToMedia = catalogItems.flatMap((catalogItem) => catalogItem.mediaIds);
      const catalogMedia = await Registry.CatalogMediaRepository.findManyByIds(catalogToMedia);
      const mediaMap = new Map(catalogMedia.map((media) => [media.catalogId, media]));

      catalogItems.forEach((catalogItem) => {
        const media = mediaMap.get(catalogItem.id);
        if (media) {
          catalogMap.set(catalogItem.readId, { ...catalogItem, media: [media] });
        }
      });

      buildableOrderItem = orderItems.map((item) => ({
        ...item,
        name: (itemsMap.get(item.referenceId) as CatalogEntity).name,
        productId: (itemsMap.get(item.referenceId) as CatalogEntity).readId,
      }));
    }

    if (!provider.providerCompanyId) {
      buildableOrderItem.forEach((item) => {
        if (!item.name) {
          throw new Error(`Order item ${item.referenceId} name is required for providers without company`, { cause: Cause.BAD_REQUEST });
        }
      });
    }

    const conditions = await getConditionsForProvider(
      provider,
      companyId,
      buildableOrderItem,
      shippingPrice || 0,
    );

    const buildableOrderItemMap = new Map(buildableOrderItem.map((item) => [item.referenceId, item]));

    return Registry.TransactionService.transactional(async () => {
      const id = await Registry.IdentificationService.generateId();
      const readId = await Registry.IdentificationService.generateObjectSequenceForCompany(companyId, 'purchase_order');

      const items: PurchaseOrderItemBuildParams[] = conditions.orderItems.map((item) => {
        const appliedDiscount = item.discounts?.appliedDiscount.catalogDiscount || item.discounts?.appliedDiscount.storeDiscount;
        const discount = appliedDiscount ? {
          value: appliedDiscount.discountValue,
          type: appliedDiscount.discountType === DiscountType.PERCENTAGE ? DiscountType.PERCENTAGE : DiscountType.AMOUNT,
        } : null;

        return {
          id: v4(),
          referenceId: item.referenceId,
          discount,
          productId: (buildableOrderItemMap.get(item.referenceId) as CreatePurchaseOrderEntriesOrderItem & {productId: string }).productId,
          name: (buildableOrderItemMap.get(item.referenceId) as CreatePurchaseOrderEntriesOrderItem).name as string,
          inventoryIds: (buildableOrderItemMap.get(item.referenceId) as CreatePurchaseOrderEntriesOrderItem).inventoryIds,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          unitPriceAfterDiscount: item.unitPriceAfterDiscount,
          unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscountAndTaxes,
          subtotal: item.subtotal,
          total: item.total,
          taxes: item.taxes,
        };
      });

      const buildParams: PurchaseOrderBuildParams = {
        id,
        readId,
        companyId,
        assignedUserId,
        shippingPrice,
        shippingAddress,
        status: PurchaseOrderStatus.PENDING,
        subtotalBeforeDiscount: conditions.subtotalBeforeDiscount,
        totalDiscount: conditions.totalDiscount,
        subtotal: conditions.subtotal,
        totalTaxes: conditions.totalTaxes,
        total: conditions.total,
        taxes: conditions.taxes,
        orderItems: items,
        providerId,
        deliveryDate,
        notes,
      };

      const {
        purchaseOrder: newPurchaseOrder,
        inventory: inventoryToCreate,
      } = await OrderDomainService.createPurchaseOrder({
        orderBuildParams: buildParams,
        createInventoryFromOrderItems,
        catalogReference: catalogMap,
      });

      if (inventoryToCreate.length) {
        const existingInventory = await Registry.InventoryRepository.findManyForCompanyBySkus(companyId, inventoryToCreate.map((i) => i.sku));

        if (existingInventory.length) {
          throw new Error(`Inventory ${existingInventory.map((i) => i.sku).join(', ')} already exists`, { cause: Cause.BAD_REQUEST });
        }

        await Registry.InventoryRepository.save(inventoryToCreate);
      }

      let [result] = await Registry.PurchaseOrderRepository.save([newPurchaseOrder]);

      if (provider.providerCompanyId) {
        const saleOrderId = await Registry.IdentificationService.generateId();
        const saleOrderReadId = await Registry.IdentificationService.generateObjectSequenceForCompany(provider.providerCompanyId, 'sale_order');
        const saleOrderCatalog = items.map((item) => item.referenceId);
        const saleOrderCatalogItems = await Registry.CatalogRepository.findManyByIds(saleOrderCatalog);
        const saleOrderRelatedInventory = saleOrderCatalogItems.flatMap((catalogItem) => catalogItem.inventoryRelations).map((relation) => relation.inventoryId);
        const saleOrderInventory = await Registry.InventoryRepository.findManyByIds(saleOrderRelatedInventory);

        client = await validateAndGetClient(companyId, provider.providerCompanyId);

        const { inventory: saleOrderUpdatedInventory, saleOrder } = OrderDomainService.createSaleOrder({
          catalog: saleOrderCatalogItems,
          inventory: saleOrderInventory,
          orderBuildParams: {
            ...newPurchaseOrder,
            id: saleOrderId,
            readId: saleOrderReadId,
            companyId: provider.providerCompanyId,
            assignedUserId: null,
            status: SaleOrderStatus.PENDING,
            clientId: client.id,
            notes: null,
            relatedPurchaseOrderId: newPurchaseOrder.id,
            orderItems: items.map((item) => ({
              ...item,
              catalogId: item.referenceId,
            })),
          },
        });

        await Registry.SaleOrderRepository.save([saleOrder]);

        if (saleOrderUpdatedInventory.length) await Registry.InventoryRepository.save(saleOrderUpdatedInventory);

        [result] = await Registry.PurchaseOrderRepository.save([{
          ...result,
          status: PurchaseOrderStatus.PENDING,
          relatedSaleOrderId: saleOrder.id,
        }]);
      }

      return result;
    }).catch((error) => {
      throw error;
    });
  }));

  return entities;
}

async function updatePurchaseOrderEntry(command: UpdatePurchaseOrderEntryCommand): Promise<PurchaseOrderEntity> {
  const {
    companyId, purchaseOrder: {
      id, deliveryDate, notes, shippingAddress, status, assignedUserId,
    },
  } = command;

  const purchaseOrderEntity = await Registry.PurchaseOrderRepository.findOneById(id) as PurchaseOrderEntity;

  await OrderUseCase.validateOrderPatch({
    companyId,
    entity: purchaseOrderEntity,
    params: {
      id, deliveryDate, notes, shippingAddress, status, assignedUserId,
    },
  });

  return Registry.TransactionService.transactional(async () => {
    let updatedPurchaseOrder = PurchaseOrderOperator.update(purchaseOrderEntity, {
      deliveryDate,
      shippingAddress,
      notes,
      assignedUserId: assignedUserId || purchaseOrderEntity.assignedUserId,
    }, companyId);

    if (status) {
      const inventoryIds = purchaseOrderEntity.orderItems.flatMap((item) => item.inventoryIds);
      const inventory: InventoryEntity[] = inventoryIds.length ? await Registry.InventoryRepository.findManyByIds(inventoryIds) : [];

      const { purchaseOrder: purchaseOrderWithStatus, inventory: updatedInventory } = OrderDomainService.updatePurchaseOrderStatus({
        order: updatedPurchaseOrder,
        status,
        modifierCompanyId: companyId,
        inventory,
      });

      updatedPurchaseOrder = purchaseOrderWithStatus;

      if (updatedInventory.length) await Registry.InventoryRepository.save(updatedInventory);
    }

    if (purchaseOrderEntity.relatedSaleOrderId) {
      const saleOrderEntity = await Registry.SaleOrderRepository.findOneById(purchaseOrderEntity.relatedSaleOrderId);

      if (!saleOrderEntity) throw new Error('Related sale order not found', { cause: Cause.NOT_FOUND });

      let updatedSaleOrder = SaleOrderOperator.update(saleOrderEntity, {
        shippingAddress,
      });

      if (status) {
        const { saleOrder: saleOrderWithStatus } = OrderDomainService.updateSaleOrderStatus({
          order: updatedSaleOrder,
          status,
          modifierCompanyId: companyId,
        });

        updatedSaleOrder = saleOrderWithStatus;
      }

      await Registry.SaleOrderRepository.save([updatedSaleOrder]);
    }

    const [updatedPurchaseOrderEntity] = await Registry.PurchaseOrderRepository.save([updatedPurchaseOrder]);

    return updatedPurchaseOrderEntity;
  })
    .catch((error) => {
      throw error;
    });
}

async function getAvailableStatuses(command: FindPurchaseOrderAvailableStatuses): Promise<PurchaseOrderStatus[]> {
  const { companyId, purchaseOrderId } = command;

  const purchaseOrder = await Registry.PurchaseOrderRepository.findOneById(purchaseOrderId);

  if (!purchaseOrder) throw new Error('Purchase order not found', { cause: Cause.NOT_FOUND });

  if (purchaseOrder.companyId !== companyId) throw new Error('Purchase order does not belong to company', { cause: Cause.FORBIDDEN });

  return PurchaseOrderOperator.availableStatuses(purchaseOrder);
}

export default {
  calculatePurchaseOrderConditions,
  obtainInventorySuggestions,
  createPurchaseOrderEntries,
  getAvailableStatuses,
  updatePurchaseOrderEntry,
};
