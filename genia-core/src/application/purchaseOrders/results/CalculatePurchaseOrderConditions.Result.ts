import { OrderItemCalculated } from '#application/common/order/results/CalculateOrderConditions.Result';
import { TaxType } from '#domain/aggregates/tax/Tax.Entity';

export interface PurchaseOrderItemCalculated extends Omit<OrderItemCalculated, 'catalogId'> {
  referenceId: string;
}

export interface OrderItemCalculatedWithTotals extends PurchaseOrderItemCalculated {
  total: number;
  subtotal: number;
}

export default interface CalculatePurchaseOrderConditionsResult {
  total: number,
  subtotal: number,
  subtotalBeforeDiscount: number,
  totalDiscount: number,
  totalTaxes: number,
  shippingPrice: number,
  hasTaxOnShipping: boolean,
  taxes: {
    id: string;
    name: string;
    amount: number;
    type: TaxType;
    value: number;
  }[];
  orderItems: OrderItemCalculatedWithTotals[],
}
