export interface CreatePurchaseOrderEntriesOrderItem {
  referenceId: string;
  quantity: number;
  name?: string;
  unitPrice?: number;
  unitPriceAfterDiscount?: number;
  taxIds?: string[];
  inventoryIds: string[];
}

interface CreatePurchaseOrderEntriesCommand {
  companyId: string;
  assignedUserId: string;
  entries : {
    createInventoryFromOrderItems?: boolean;
    providerId: string;
    shippingPrice?: number;
    shippingAddress?: string | null;
    notes?: string;
    deliveryDate?: Date;
    orderItems: CreatePurchaseOrderEntriesOrderItem[];
  }[];
}

export default CreatePurchaseOrderEntriesCommand;
