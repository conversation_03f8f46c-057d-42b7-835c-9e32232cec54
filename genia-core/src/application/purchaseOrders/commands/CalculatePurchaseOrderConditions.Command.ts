export interface CalculatePurchaseOrderConditionsOrderItem {
  referenceId: string;
  quantity: number;
  unitPrice?: number;
  unitPriceAfterDiscount?: number;
  taxIds?: string[];
}

export default interface CalculatePurchaseOrderConditionsCommand {
  providerId: string;
  companyId: string;
  orderItems: CalculatePurchaseOrderConditionsOrderItem[];
  shippingPrice?: number;
  hasTaxOnShipping?: boolean;
}
