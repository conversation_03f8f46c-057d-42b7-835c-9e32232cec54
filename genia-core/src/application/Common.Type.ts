import Cause from '#composition/Cause.type';

export interface PaginatedQueryFilter {
  [key: string]: unknown;
}

export interface PaginatedQueryWithSearchAndFilters<T extends PaginatedQueryFilter = PaginatedQueryFilter > {
  page: number;
  pageSize: number;
  searchTerm?: string;
  filters?: T;
  orderBy?: {[key: string]: 'ASC' | 'DESC'};
}

export class ApplicationError extends Error {
  code: string;

  cause: Cause;

  constructor(message: string, cause: Cause, code: string) {
    super(message);
    this.code = code;
    this.cause = cause;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}
