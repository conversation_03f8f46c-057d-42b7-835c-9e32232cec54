import { ApplicationError } from '#application/Common.Type';
import UserErrorCodes from '#application/user/User.ErrorCodes';
import Cause from '#composition/Cause.type';
import UserErrors from '#domain/aggregates/user/User.Errors';

function isObjectWithMessage(error: unknown): error is { message: string } {
  return typeof error === 'object' && error !== null && 'message' in error && typeof (error as { message?: unknown }).message === 'string';
}

function map(error: unknown): Error {
  switch (error) {
    case UserErrors.FORBIDDEN_TO_REGULAR_USER:
      return new ApplicationError('User must be an admin to perform this action.', Cause.FORBIDDEN, UserErrorCodes.COMPANY_USER_FORBIDDEN);
    case UserErrors.FORBIDDEN_TO_DIFFERENT_USER:
      return new ApplicationError('Users has only permissions to their own information.', Cause.FORBIDDEN, UserErrorCodes.NOT_SELF_FORBIDDEN);
    default:
      if (error instanceof Error) {
        return error;
      }

      if (typeof error === 'string') {
        return new Error(error);
      }

      if (isObjectWithMessage(error)) {
        return new Error(error.message);
      }

      return new Error(`An unexpected error occurred: ${JSON.stringify(error)}`);
  }
}

export default {
  map,
};
