import CatalogMediaEntity from '#domain/aggregates/catalogMedia/CatalogMedia.Entity';

interface CatalogMediaRepository {
  findManyByIds(ids: string[]): Promise<CatalogMediaEntity[]>;
  findManyByCatalogId(id: string): Promise<CatalogMediaEntity[]>;
  save(medias: CatalogMediaEntity[]): Promise<CatalogMediaEntity[]>;
  deleteByIds(ids: string[]): Promise<undefined>;
}

export default CatalogMediaRepository;
