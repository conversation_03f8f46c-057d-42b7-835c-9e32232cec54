import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';

export default interface StoreDiscountRepository {
  saveMany(storeDiscounts: StoreDiscountEntity[]): Promise<StoreDiscountEntity[]>
  findManyByIds(ids: string[]): Promise<StoreDiscountEntity[]>
  findManyForCompaniesByClientIds(companyIds: string[], clientIds: string[] | null): Promise<StoreDiscountEntity[]>
  findManyByClients(clientIds: string[]): Promise<StoreDiscountEntity[]>
  deleteByIds(discountIds: string[]): Promise<void>
}
