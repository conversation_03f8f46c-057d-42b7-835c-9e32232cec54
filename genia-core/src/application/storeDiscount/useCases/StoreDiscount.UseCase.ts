import { difference } from 'lodash';

import CreateStoreDiscountEntriesCommand from '#application/storeDiscount/commands/CreateStoreDiscountEntriesCommand';
import UpdateStoreDiscountEntryCommand from '#application/storeDiscount/commands/UpdateStoreDiscountEntry.Command';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import StoreDiscountOperator from '#domain/aggregates/storeDiscount/StoreDiscount.Operator';
import StoreDiscountDomainService from '#domain/domainServices/StoreDiscount.DomainService';

async function createStoreDiscountEntries(createStoreDiscountEntriesCommand: CreateStoreDiscountEntriesCommand): Promise<StoreDiscountEntity[]> {
  const { companyId, entries } = createStoreDiscountEntriesCommand;

  return Registry.TransactionService.transactional(async () => {
    const storeDiscountEntries = await Promise.all(entries.map(async (entry) => {
      const id = await Registry.IdentificationService.generateId();

      return StoreDiscountOperator.build({
        ...entry, id, companyId,
      });
    }));

    return Registry.StoreDiscountRepository.saveMany(storeDiscountEntries);
  });
}

async function getStoreDiscountWithAccessCheck(companyId: string, storeDiscountId: string): Promise<StoreDiscountEntity> {
  const [storeDiscount] = await Registry.StoreDiscountRepository.findManyByIds([storeDiscountId]);

  if (!storeDiscount) {
    throw new Error('Store discount not found', { cause: Cause.NOT_FOUND });
  }

  if (companyId !== storeDiscount.companyId) {
    throw new Error('Store discount does not belong to the company', { cause: Cause.FORBIDDEN });
  }

  return storeDiscount;
}

async function assignStoreDiscountClients(companyId: string, storeDiscountId: string, clientIds: string[]): Promise<void> {
  const storeDiscount = await getStoreDiscountWithAccessCheck(companyId, storeDiscountId);

  const foundClients = await Registry.ClientRepository.findManyByIds(clientIds);

  if (foundClients.length !== clientIds.length) {
    const foundIds = foundClients.map(({ id }) => id);
    const notFoundIds = difference(clientIds, foundIds);

    throw new Error(`Some clients were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
  }

  const notCompanyClients = foundClients.filter(({ companyId: clientCompanyId }) => companyId !== clientCompanyId);

  if (notCompanyClients.length) {
    const notCompanyClientIds = notCompanyClients.map(({ id }) => id);

    throw new Error(`Some clients do not belong to the company: ${notCompanyClientIds.join(',')}`, { cause: Cause.FORBIDDEN });
  }

  const updatedStoreDiscount = StoreDiscountOperator.assignClients(storeDiscount, clientIds);

  const clientsStoreDiscounts = (await Registry.StoreDiscountRepository.findManyByClients(clientIds))
    .filter(({ id }) => id !== storeDiscountId);

  StoreDiscountDomainService.validateDiscounts([...clientsStoreDiscounts, updatedStoreDiscount]);

  await Registry.StoreDiscountRepository.saveMany([updatedStoreDiscount]);
}

async function unassignStoreDiscountClients(companyId: string, storeDiscountId: string, clientIds: string[]): Promise<void> {
  const storeDiscount = await getStoreDiscountWithAccessCheck(companyId, storeDiscountId);

  const updatedStoreDiscount = StoreDiscountOperator.unassignClients(storeDiscount, clientIds);

  await Registry.StoreDiscountRepository.saveMany([updatedStoreDiscount]);
}

async function updateStoreDiscount(updateStoreDiscountEntryCommand: UpdateStoreDiscountEntryCommand): Promise<StoreDiscountEntity> {
  const { companyId, id: storeDiscountId, ...params } = updateStoreDiscountEntryCommand;

  const storeDiscount = await getStoreDiscountWithAccessCheck(companyId, storeDiscountId);

  const updatedStoreDiscount = StoreDiscountOperator.update(storeDiscount, params);

  const [result] = await Registry.StoreDiscountRepository.saveMany([updatedStoreDiscount]);

  return result;
}

async function deleteStoreDiscounts(companyId: string, storeDiscountIds: string[]): Promise<void> {
  const storeDiscounts = await Registry.StoreDiscountRepository.findManyByIds(storeDiscountIds);

  if (storeDiscountIds.length !== storeDiscounts.length) {
    const foundIds = storeDiscounts.map(({ id }) => id);
    const notFoundIds = difference(storeDiscountIds, foundIds);

    throw new Error(`Some discounts were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
  }

  const notCompanyDiscounts = storeDiscounts.filter(({ companyId: discountCompanyId }) => discountCompanyId !== companyId);

  if (notCompanyDiscounts.length) {
    const notCompanyDiscountIds = notCompanyDiscounts.map(({ id }) => id);

    throw new Error(`Some discounts do not belong to the company: ${notCompanyDiscountIds.join(',')}`, { cause: Cause.FORBIDDEN });
  }

  return Registry.StoreDiscountRepository.deleteByIds(storeDiscountIds);
}

export default {
  createStoreDiscountEntries,
  assignStoreDiscountClients,
  unassignStoreDiscountClients,
  updateStoreDiscount,
  deleteStoreDiscounts,
};
