import CreateStoreDiscountEntriesCommand from '#application/storeDiscount/commands/CreateStoreDiscountEntriesCommand';
import StoreDiscountUseCase from '#application/storeDiscount/useCases/StoreDiscount.UseCase';
import Registry from '#composition/ImplementationRegistry';
import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import StoreDiscountOperator from '#domain/aggregates/storeDiscount/StoreDiscount.Operator';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';

jest.unmock('lodash');

jest.unmock('./StoreDiscount.UseCase');

describe('StoreDiscountUseCase', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createStoreDiscountEntries', () => {
    const command: CreateStoreDiscountEntriesCommand = {
      entries: [
        {
          name: 'Discount 1',
          discountValue: 10,
          discountType: DiscountType.AMOUNT,
          requiredAmount: 100,
          startDate: new Date('2023-01-01T00:00:00.000Z'),
          endDate: new Date('2023-12-31T23:59:59.999Z'),
        },
      ],
      companyId: 'comp_123',
    };

    const mockStoreDiscount: StoreDiscountEntity = {
      id: 'sd_001',
      ...command.entries[0],
      companyId: 'comp_123',
      endDate: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      disabledAt: null,
      clientIds: [],
    };

    it('should create store discounts successfully', async () => {
      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValue('sd_001');
      (StoreDiscountOperator.build as jest.Mock).mockReturnValue(mockStoreDiscount);
      (Registry.StoreDiscountRepository.saveMany as jest.Mock).mockResolvedValue([mockStoreDiscount]);

      const result = await StoreDiscountUseCase.createStoreDiscountEntries(command);

      expect(result).toEqual([mockStoreDiscount]);
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(StoreDiscountOperator.build).toHaveBeenCalledWith({
        ...command.entries[0],
        id: 'sd_001',
        companyId: 'comp_123',
      });
      expect(Registry.StoreDiscountRepository.saveMany).toHaveBeenCalledWith([mockStoreDiscount]);
    });

    it('should handle creating store discounts with no clients', async () => {
      const commandWithoutClients: CreateStoreDiscountEntriesCommand = {
        entries: [
          {
            name: 'Discount 2',
            discountValue: 20,
            discountType: DiscountType.PERCENTAGE,
            requiredAmount: 200,
            startDate: new Date(),
            endDate: null,
          },
        ],
        companyId: 'comp_123',
      };

      const mockStoreDiscountWithoutClients: StoreDiscountEntity = {
        id: 'sd_002',
        ...commandWithoutClients.entries[0],
        companyId: 'comp_123',
        createdAt: new Date(),
        updatedAt: new Date(),
        disabledAt: null,
        startDate: new Date(),
        clientIds: [],
        endDate: null,
      };

      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValue('sd_002');
      (StoreDiscountOperator.build as jest.Mock).mockReturnValue(mockStoreDiscountWithoutClients);
      (Registry.StoreDiscountRepository.saveMany as jest.Mock).mockResolvedValue([mockStoreDiscountWithoutClients]);

      const result = await StoreDiscountUseCase.createStoreDiscountEntries(commandWithoutClients);

      expect(result).toEqual([mockStoreDiscountWithoutClients]);
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(StoreDiscountOperator.build).toHaveBeenCalledWith({
        ...commandWithoutClients.entries[0],
        id: 'sd_002',
        companyId: 'comp_123',
      });
      expect(Registry.StoreDiscountRepository.saveMany).toHaveBeenCalledWith([mockStoreDiscountWithoutClients]);
    });
  });

  describe('assignStoreDiscountClients', () => {
    const companyId = 'comp_123';
    const storeDiscountId = 'sd_123';
    const clientIds = ['client_001', 'client_002'];

    const mockStoreDiscount: StoreDiscountEntity = {
      id: storeDiscountId,
      name: 'Discount 1',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date(),
      endDate: null,
      disabledAt: null,
      companyId,
      clientIds: [],
      requiredAmount: 100,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should assign clients to a store discount successfully', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockStoreDiscount]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'client_001', companyId },
        { id: 'client_002', companyId },
      ]);
      (StoreDiscountOperator.assignClients as jest.Mock).mockReturnValue({
        ...mockStoreDiscount,
        clientIds: ['client_001', 'client_002'],
      });
      (Registry.StoreDiscountRepository.findManyByClients as jest.Mock).mockResolvedValue([]);
      (Registry.StoreDiscountRepository.saveMany as jest.Mock).mockResolvedValue([]);

      await StoreDiscountUseCase.assignStoreDiscountClients(companyId, storeDiscountId, clientIds);

      expect(Registry.StoreDiscountRepository.findManyByIds).toHaveBeenCalledWith([storeDiscountId]);
      expect(Registry.ClientRepository.findManyByIds).toHaveBeenCalledWith(clientIds);
      expect(StoreDiscountOperator.assignClients).toHaveBeenCalledWith(mockStoreDiscount, clientIds);
      expect(Registry.StoreDiscountRepository.saveMany).toHaveBeenCalledWith([
        { ...mockStoreDiscount, clientIds: ['client_001', 'client_002'] },
      ]);
    });

    it('should throw an error if the store discount is not found', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      await expect(
        StoreDiscountUseCase.assignStoreDiscountClients(companyId, storeDiscountId, clientIds),
      ).rejects.toThrow('Store discount not found');
    });

    it('should throw an error if the store discount does not belong to the company', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { ...mockStoreDiscount, companyId: 'other_company' },
      ]);

      await expect(
        StoreDiscountUseCase.assignStoreDiscountClients(companyId, storeDiscountId, clientIds),
      ).rejects.toThrow('Store discount does not belong to the company');
    });

    it('should throw an error if some clients are not found', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockStoreDiscount]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([{ id: 'client_001', companyId }]);

      await expect(
        StoreDiscountUseCase.assignStoreDiscountClients(companyId, storeDiscountId, clientIds),
      ).rejects.toThrow('Some clients were not found: client_002');
    });

    it('should throw an error if some clients do not belong to the company', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockStoreDiscount]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'client_001', companyId },
        { id: 'client_002', companyId: 'other_company' },
      ]);

      await expect(
        StoreDiscountUseCase.assignStoreDiscountClients(companyId, storeDiscountId, clientIds),
      ).rejects.toThrow('Some clients do not belong to the company: client_002');
    });

    it('should validate discounts before assigning clients', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockStoreDiscount]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'client_001', companyId },
        { id: 'client_002', companyId },
      ]);
      (Registry.StoreDiscountRepository.findManyByClients as jest.Mock).mockResolvedValue([mockStoreDiscount]);

      await StoreDiscountUseCase.assignStoreDiscountClients(companyId, storeDiscountId, clientIds);

      expect(StoreDiscountOperator.assignClients).toHaveBeenCalledWith(mockStoreDiscount, clientIds);
    });
  });

  describe('unassignStoreDiscountClients', () => {
    const companyId = 'comp_123';
    const storeDiscountId = 'sd_123';
    const clientIds = ['client_001', 'client_002'];

    const mockStoreDiscount: StoreDiscountEntity = {
      id: storeDiscountId,
      name: 'Discount 1',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date(),
      endDate: null,
      disabledAt: null,
      companyId,
      clientIds: ['client_001', 'client_002', 'client_003'],
      requiredAmount: 100,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should unassign clients from a store discount successfully', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockStoreDiscount]);
      (StoreDiscountOperator.unassignClients as jest.Mock).mockReturnValue({
        ...mockStoreDiscount,
        clientIds: ['client_003'],
      });

      await StoreDiscountUseCase.unassignStoreDiscountClients(companyId, storeDiscountId, clientIds);

      expect(Registry.StoreDiscountRepository.findManyByIds).toHaveBeenCalledWith([storeDiscountId]);
      expect(StoreDiscountOperator.unassignClients).toHaveBeenCalledWith(mockStoreDiscount, clientIds);
      expect(Registry.StoreDiscountRepository.saveMany).toHaveBeenCalledWith([
        { ...mockStoreDiscount, clientIds: ['client_003'] },
      ]);
    });

    it('should throw an error if the store discount is not found', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      await expect(
        StoreDiscountUseCase.unassignStoreDiscountClients(companyId, storeDiscountId, clientIds),
      ).rejects.toThrow('Store discount not found');
    });

    it('should throw an error if the store discount does not belong to the company', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { ...mockStoreDiscount, companyId: 'other_company' },
      ]);

      await expect(
        StoreDiscountUseCase.unassignStoreDiscountClients(companyId, storeDiscountId, clientIds),
      ).rejects.toThrow('Store discount does not belong to the company');
    });

    it('should throw an error if some clients do not have access to the store discount', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockStoreDiscount]);
      (StoreDiscountOperator.unassignClients as jest.Mock).mockImplementation(() => {
        throw new Error('Some clients do not have access to the store discount: client_004');
      });

      await expect(
        StoreDiscountUseCase.unassignStoreDiscountClients(companyId, storeDiscountId, ['client_004']),
      ).rejects.toThrow('Some clients do not have access to the store discount: client_004');
    });
  });

  describe('updateStoreDiscount', () => {
    const companyId = 'comp_123';
    const storeDiscountId = 'sd_123';

    const mockStoreDiscount: StoreDiscountEntity = {
      id: storeDiscountId,
      name: 'Original Discount',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date('2023-01-01T00:00:00.000Z'),
      endDate: new Date('2023-12-31T23:59:59.999Z'),
      disabledAt: null,
      companyId,
      clientIds: ['client1', 'client2'],
      requiredAmount: 100,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const updateCommand = {
      id: storeDiscountId,
      companyId,
      name: 'Updated Discount',
      discountValue: 20,
      requiredAmount: 200,
    };

    it('should update the store discount successfully', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockStoreDiscount]);
      (StoreDiscountOperator.update as jest.Mock).mockReturnValue({
        ...mockStoreDiscount,
        ...updateCommand,
        updatedAt: new Date(),
      });
      (Registry.StoreDiscountRepository.saveMany as jest.Mock).mockResolvedValue([
        { ...mockStoreDiscount, ...updateCommand, updatedAt: new Date() },
      ]);

      const result = await StoreDiscountUseCase.updateStoreDiscount(updateCommand);

      const { id, companyId: _, ...params } = updateCommand;

      expect(Registry.StoreDiscountRepository.findManyByIds).toHaveBeenCalledWith([storeDiscountId]);
      expect(StoreDiscountOperator.update).toHaveBeenCalledWith(mockStoreDiscount, params);
      expect(Registry.StoreDiscountRepository.saveMany).toHaveBeenCalledWith([
        { ...mockStoreDiscount, ...updateCommand, updatedAt: expect.any(Date) },
      ]);
      expect(result).toEqual({ ...mockStoreDiscount, ...updateCommand, updatedAt: expect.any(Date) });
    });

    it('should throw an error if the store discount is not found', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      await expect(StoreDiscountUseCase.updateStoreDiscount(updateCommand)).rejects.toThrow(
        'Store discount not found',
      );
    });

    it('should throw an error if the store discount does not belong to the company', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { ...mockStoreDiscount, companyId: 'other_company' },
      ]);

      await expect(StoreDiscountUseCase.updateStoreDiscount(updateCommand)).rejects.toThrow(
        'Store discount does not belong to the company',
      );
    });
  });
});
