import { ApplicationError } from '#application/Common.Type';
import UserErrorCodes from '#application/user/User.ErrorCodes';
import Cause from '#composition/Cause.type';
import UserErrors from '#domain/aggregates/user/User.Errors';

import ErrorsMapper from './Errors.Mapper';

jest.unmock('./Errors.Mapper');
jest.unmock('#application/Common.Type');
jest.unmock('#application/user/User.ErrorCodes');
jest.unmock('#composition/Cause.type');
jest.unmock('#domain/aggregates/user/User.Errors');

describe('Errors.Mapper.map', () => {
  it('should map UserErrors.FORBIDDEN_TO_REGULAR_USER to ApplicationError', () => {
    const error = ErrorsMapper.map(UserErrors.FORBIDDEN_TO_REGULAR_USER);
    expect(error).toBeInstanceOf(ApplicationError);
    expect(error.message).toBe('User must be an admin to perform this action.');
    expect((error as ApplicationError).code).toBe(UserErrorCodes.COMPANY_USER_FORBIDDEN);
    expect((error as ApplicationError).cause).toBe(Cause.FORBIDDEN);
  });

  it('should map UserErrors.FORBIDDEN_TO_DIFFERENT_USER to ApplicationError', () => {
    const error = ErrorsMapper.map(UserErrors.FORBIDDEN_TO_DIFFERENT_USER);
    expect(error).toBeInstanceOf(ApplicationError);
    expect(error.message).toBe('Users has only permissions to their own information.');
    expect((error as ApplicationError).code).toBe(UserErrorCodes.NOT_SELF_FORBIDDEN);
    expect((error as ApplicationError).cause).toBe(Cause.FORBIDDEN);
  });

  it('should return the same Error instance if error is already an Error', () => {
    const err = new Error('Some error');
    const result = ErrorsMapper.map(err);
    expect(result).toBe(err);
  });

  it('should wrap a string error in an Error', () => {
    const result = ErrorsMapper.map('string error');
    expect(result).toBeInstanceOf(Error);
    expect(result.message).toBe('string error');
  });

  it('should wrap an object with message in an Error', () => {
    const result = ErrorsMapper.map({ message: 'object error' });
    expect(result).toBeInstanceOf(Error);
    expect(result.message).toBe('object error');
  });

  it('should wrap unknown objects in a generic Error', () => {
    const obj = { foo: 'bar' };
    const result = ErrorsMapper.map(obj);
    expect(result).toBeInstanceOf(Error);
    expect(result.message).toContain('An unexpected error occurred');
    expect(result.message).toContain('"foo":"bar"');
  });
});
