import validateOrderPatchCommand from '#application/common/commands/ValidateOrderPatch.Command';
import Registry from '#composition/ImplementationRegistry';
import CatalogEntity, { CatalogType } from '#domain/aggregates/catalog/Catalog.Entity';
import CatalogDiscountEntity from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';
import ClientObject from '#domain/aggregates/client/Client.Entity';
import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import TaxEntity, { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import OrderEntity, { OrderStatus } from '#domain/common/aggregates/order/Order.Entity';
import CatalogDomainService from '#domain/domainServices/Catalog.DomainService';

import OrderUseCase from './Order.UseCase';

jest.unmock('./Order.UseCase');

describe('OrderUseCase', () => {
  describe('getCatalogConditions', () => {
    const mockClient: ClientObject = {
      id: 'client-id',
      companyId: 'client-company-id',
      name: 'Test Client',
      createdAt: new Date(),
      updatedAt: new Date(),
    } as ClientObject;

    const mockCatalogItem1: CatalogEntity = {
      id: 'catalog-item-1',
      price: 100,
      taxIds: ['tax-1'],
      companyId: 'provider-company-id',
      readId: 'SKU-1',
      requiresStock: true,
      name: 'Test Item 1',
      attributes: [],
      createdAt: new Date(),
      description: 'Test Description',
      disabledAt: null,
      inventoryRelations: [],
      mediaIds: [],
      type: CatalogType.PRODUCT,
      updatedAt: new Date(),
      discountIds: [],
    };

    const mockCatalogItem2: CatalogEntity = {
      id: 'catalog-item-2',
      price: 200,
      taxIds: ['tax-2'],
      companyId: 'provider-company-id',
      readId: 'SKU-2',
      requiresStock: true,
      name: 'Test Item 2',
      attributes: [],
      createdAt: new Date(),
      description: 'Test Description 2',
      disabledAt: null,
      inventoryRelations: [],
      mediaIds: [],
      type: CatalogType.PRODUCT,
      updatedAt: new Date(),
      discountIds: [],
    };

    const mockTaxes: TaxEntity[] = [
      {
        id: 'tax-1',
        name: 'VAT 16%',
        type: TaxType.PERCENTAGE,
        value: 16,
        createdAt: new Date(),
        updatedAt: new Date(),
        countryCode: 'ES',
        disabledAt: null,
      },
      {
        id: 'tax-2',
        name: 'Special Tax',
        type: TaxType.AMOUNT,
        value: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
        countryCode: 'ES',
        disabledAt: null,
      },
      {
        id: 'tax-3',
        name: 'Disabled Tax',
        type: TaxType.PERCENTAGE,
        value: 5,
        createdAt: new Date(),
        updatedAt: new Date(),
        countryCode: 'ES',
        disabledAt: new Date(), // This tax is disabled
      },
    ];

    const mockCatalogDiscounts: CatalogDiscountEntity[] = [
      {
        id: 'catalog-discount-1',
        discountValue: 10,
        discountType: DiscountType.PERCENTAGE,
        name: 'Product Discount',
        catalogIds: ['catalog-item-1'],
        clientIds: ['client-id'],
        companyId: 'provider-company-id',
        startDate: new Date(),
        endDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        disabledAt: null,
      },
    ] as CatalogDiscountEntity[];

    const mockStoreDiscounts: StoreDiscountEntity[] = [
      {
        id: 'store-discount-1',
        discountValue: 5,
        discountType: DiscountType.PERCENTAGE,
        name: 'Store Discount',
        requiredAmount: 150,
        clientIds: ['client-id'],
        companyId: 'provider-company-id',
        startDate: new Date(),
        endDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        disabledAt: null,
      },
    ] as StoreDiscountEntity[];

    const mockCatalogCalculationResult = [
      {
        id: 'catalog-item-1',
        quantity: 2,
        price: 100,
        priceAfterDiscount: 90, // 10% discount applied
        priceAfterTaxes: 104.4, // 16% tax on discounted price
        appliedDiscount: {
          catalogDiscount: {
            ...mockCatalogDiscounts[0],
            discountValue: 10,
            discountType: DiscountType.PERCENTAGE,
            priceAfterDiscount: 90,
          },
          storeDiscount: null,
        },
        applicableDiscounts: {
          catalogDiscounts: [mockCatalogDiscounts[0]],
          storeDiscounts: [mockStoreDiscounts[0]],
        },
        taxes: [
          {
            id: 'tax-1',
            name: 'VAT 16%',
            type: TaxType.PERCENTAGE,
            value: 16,
            amount: 14.4, // 16% of 90
          },
        ],
      },
      {
        id: 'catalog-item-2',
        quantity: 1,
        price: 200,
        priceAfterDiscount: 190, // 5% store discount applied
        priceAfterTaxes: 200, // Fixed tax of 10 on discounted price
        appliedDiscount: {
          catalogDiscount: null,
          storeDiscount: {
            ...mockStoreDiscounts[0],
            discountValue: 5,
            discountType: DiscountType.PERCENTAGE,
            priceAfterDiscount: 190,
          },
        },
        applicableDiscounts: {
          catalogDiscounts: [],
          storeDiscounts: [mockStoreDiscounts[0]],
        },
        taxes: [
          {
            id: 'tax-2',
            name: 'Special Tax',
            type: TaxType.AMOUNT,
            value: 10,
            amount: 10,
          },
        ],
      },
    ];

    const orderItems = [
      { catalogId: 'catalog-item-1', quantity: 2 },
      { catalogId: 'catalog-item-2', quantity: 1 },
    ];

    beforeEach(() => {
      jest.clearAllMocks();

      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([
        mockCatalogItem1,
        mockCatalogItem2,
      ]);

      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValue(mockTaxes);

      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock)
        .mockImplementation((companyIds, catalogIds, clientIds) => {
          if (clientIds === null) {
            return Promise.resolve([]); // No global catalog discounts in this test
          }
          return Promise.resolve(mockCatalogDiscounts);
        });

      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock)
        .mockImplementation((companyIds, clientIds) => {
          if (clientIds === null) {
            return Promise.resolve([]); // No global store discounts in this test
          }
          return Promise.resolve(mockStoreDiscounts);
        });

      (CatalogDomainService.calculateCatalogDiscountAndTaxes as jest.Mock)
        .mockReturnValue(mockCatalogCalculationResult);
    });

    it('should calculate catalog conditions successfully', async () => {
      const result = await OrderUseCase.getCatalogConditions(
        orderItems,
        'provider-company-id',
        mockClient,
      );

      expect(Registry.CatalogRepository.findManyByIds).toHaveBeenCalledWith(['catalog-item-1', 'catalog-item-2']);
      expect(Registry.TaxRepository.findManyByIds).toHaveBeenCalledWith(['tax-1', 'tax-2']);

      // Verify client-specific discounts are fetched
      expect(Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds)
        .toHaveBeenCalledWith(
          ['provider-company-id'],
          ['catalog-item-1', 'catalog-item-2'],
          ['client-id'],
        );

      // Verify global discounts are fetched
      expect(Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds)
        .toHaveBeenCalledWith(
          ['provider-company-id'],
          ['catalog-item-1', 'catalog-item-2'],
          null,
        );

      // Verify client-specific store discounts are fetched
      expect(Registry.StoreDiscountRepository.findManyForCompaniesByClientIds)
        .toHaveBeenCalledWith(['provider-company-id'], ['client-id']);

      // Verify global store discounts are fetched
      expect(Registry.StoreDiscountRepository.findManyForCompaniesByClientIds)
        .toHaveBeenCalledWith(['provider-company-id'], null);

      expect(CatalogDomainService.calculateCatalogDiscountAndTaxes).toHaveBeenCalledWith({
        catalog: [
          { ...mockCatalogItem1, quantity: 2 },
          { ...mockCatalogItem2, quantity: 1 },
        ],
        catalogDiscounts: mockCatalogDiscounts,
        storeDiscounts: mockStoreDiscounts,
        client: mockClient,
        taxes: [mockTaxes[0], mockTaxes[1]], // Only non-disabled taxes
      });

      // Verify result format
      expect(result).toEqual([
        {
          catalogId: 'catalog-item-1',
          quantity: 2,
          unitPrice: 100,
          unitPriceAfterDiscount: 90,
          unitPriceAfterDiscountAndTaxes: 104.4,
          discounts: {
            appliedDiscount: {
              catalogDiscount: expect.objectContaining({
                id: 'catalog-discount-1',
                discountValue: 10,
                discountType: DiscountType.PERCENTAGE,
              }),
              storeDiscount: null,
            },
            applicableDiscounts: {
              catalogDiscounts: [expect.objectContaining({ id: 'catalog-discount-1' })],
              storeDiscounts: [expect.objectContaining({ id: 'store-discount-1' })],
            },
          },
          taxes: [
            {
              id: 'tax-1',
              name: 'VAT 16%',
              type: TaxType.PERCENTAGE,
              value: 16,
              amount: 14.4,
            },
          ],
        },
        {
          catalogId: 'catalog-item-2',
          quantity: 1,
          unitPrice: 200,
          unitPriceAfterDiscount: 190,
          unitPriceAfterDiscountAndTaxes: 200,
          discounts: {
            appliedDiscount: {
              catalogDiscount: null,
              storeDiscount: expect.objectContaining({
                id: 'store-discount-1',
                discountValue: 5,
                discountType: DiscountType.PERCENTAGE,
              }),
            },
            applicableDiscounts: {
              catalogDiscounts: [],
              storeDiscounts: [expect.objectContaining({ id: 'store-discount-1' })],
            },
          },
          taxes: [
            {
              id: 'tax-2',
              name: 'Special Tax',
              type: TaxType.AMOUNT,
              value: 10,
              amount: 10,
            },
          ],
        },
      ]);
    });

    it('should throw error when catalog item not found', async () => {
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogItem1]);

      await expect(
        OrderUseCase.getCatalogConditions(orderItems, 'provider-company-id', mockClient),
      ).rejects.toThrow('Provider catalog item catalog-item-2 not found');
    });

    it('should throw error when catalog item does not belong to provider company', async () => {
      const wrongCompanyCatalog = {
        ...mockCatalogItem2,
        companyId: 'wrong-company-id',
      };

      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([
        mockCatalogItem1,
        wrongCompanyCatalog,
      ]);

      await expect(
        OrderUseCase.getCatalogConditions(orderItems, 'provider-company-id', mockClient),
      ).rejects.toThrow('Catalog item does not belong to provider');
    });

    it('should handle empty order items', async () => {
      // Override mocks specific to this test
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([]);
      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValue([]);
      (CatalogDomainService.calculateCatalogDiscountAndTaxes as jest.Mock).mockReturnValue([]);

      const result = await OrderUseCase.getCatalogConditions([], 'provider-company-id', mockClient);

      expect(Registry.CatalogRepository.findManyByIds).toHaveBeenCalledWith([]);
      expect(Registry.TaxRepository.findManyByIds).toHaveBeenCalledWith([]);
      expect(result).toEqual([]);
    });

    it('should filter out disabled taxes', async () => {
      await OrderUseCase.getCatalogConditions(
        [{ catalogId: 'catalog-item-1', quantity: 1 }],
        'provider-company-id',
        mockClient,
      );

      // Check that disabled tax was filtered out
      expect(CatalogDomainService.calculateCatalogDiscountAndTaxes).toHaveBeenCalledWith(
        expect.objectContaining({
          taxes: expect.not.arrayContaining([mockTaxes[2]]), // Disabled tax should be filtered out
        }),
      );
    });

    it('should handle case with no discounts', async () => {
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValue([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValue([]);

      (CatalogDomainService.calculateCatalogDiscountAndTaxes as jest.Mock).mockReturnValue([
        {
          ...mockCatalogCalculationResult[0],
          priceAfterDiscount: 100, // No discount
          appliedDiscount: {
            catalogDiscount: null,
            storeDiscount: null,
          },
          applicableDiscounts: {
            catalogDiscounts: [],
            storeDiscounts: [],
          },
        },
      ]);

      const result = await OrderUseCase.getCatalogConditions(
        [{ catalogId: 'catalog-item-1', quantity: 2 }],
        'provider-company-id',
        mockClient,
      );

      expect(CatalogDomainService.calculateCatalogDiscountAndTaxes).toHaveBeenCalledWith({
        catalog: [{ ...mockCatalogItem1, quantity: 2 }],
        catalogDiscounts: [],
        storeDiscounts: [],
        client: mockClient,
        taxes: expect.any(Array),
      });

      expect(result[0].discounts?.appliedDiscount).toEqual({
        catalogDiscount: null,
        storeDiscount: null,
      });
      expect(result[0].unitPriceAfterDiscount).toBe(100);
    });

    it('should combine both client-specific and global discounts', async () => {
      const globalCatalogDiscount = {
        id: 'global-catalog-discount',
        discountValue: 5,
        discountType: DiscountType.PERCENTAGE,
        catalogIds: ['catalog-item-2'],
        clientIds: null,
        companyId: 'provider-company-id',
      };

      const globalStoreDiscount = {
        id: 'global-store-discount',
        discountValue: 2,
        discountType: DiscountType.PERCENTAGE,
        clientIds: null,
        companyId: 'provider-company-id',
      };

      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock)
        .mockImplementation((companyIds, catalogIds, clientIds) => {
          if (clientIds === null) {
            return Promise.resolve([globalCatalogDiscount]);
          }
          return Promise.resolve(mockCatalogDiscounts);
        });

      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock)
        .mockImplementation((companyIds, clientIds) => {
          if (clientIds === null) {
            return Promise.resolve([globalStoreDiscount]);
          }
          return Promise.resolve(mockStoreDiscounts);
        });

      await OrderUseCase.getCatalogConditions(orderItems, 'provider-company-id', mockClient);

      expect(CatalogDomainService.calculateCatalogDiscountAndTaxes).toHaveBeenCalledWith(
        expect.objectContaining({
          catalogDiscounts: [...mockCatalogDiscounts, globalCatalogDiscount],
          storeDiscounts: [...mockStoreDiscounts, globalStoreDiscount],
        }),
      );
    });
  });

  describe('validateOrderPatch', () => {
    const mockCompanyId = 'company-id';
    const mockOrderId = 'order-id';

    const mockOrder: OrderEntity = {
      id: mockOrderId,
      companyId: mockCompanyId,
      assignedUserId: null,
      readId: 'ORD-123',
      status: OrderStatus.PENDING,
      deliveryDate: null,
      shippedAt: null,
      reviewStartedAt: null,
      subtotalBeforeDiscount: 0,
      subtotal: 0,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 0,
      shippingPrice: 0,
      shippingAddress: null,
      notes: null,
      taxes: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      orderItems: [],
    };

    const mockUser = {
      id: 'user-id',
      companies: [mockCompanyId],
      // Other user properties would be here
    };

    beforeEach(() => {
      jest.clearAllMocks();
      (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValue(mockUser);
    });

    it('should validate a valid order patch', async () => {
      const command: validateOrderPatchCommand = {
        companyId: mockCompanyId,
        entity: mockOrder,
        params: {
          id: mockOrderId,
          deliveryDate: new Date(),
          notes: 'Updated notes',
        },
      };

      await expect(OrderUseCase.validateOrderPatch(command)).resolves.not.toThrow();
    });

    it('should throw an error if entity is missing', async () => {
      const command: validateOrderPatchCommand = {
        companyId: mockCompanyId,
        entity: undefined,
        params: {
          id: mockOrderId,
          deliveryDate: new Date(),
        },
      };

      await expect(OrderUseCase.validateOrderPatch(command)).rejects.toThrow(`Order ${mockOrderId} not found`);
    });

    it('should throw an error if entity does not belong to company', async () => {
      const command: validateOrderPatchCommand = {
        companyId: mockCompanyId,
        entity: {
          ...mockOrder,
          companyId: 'different-company-id',
        },
        params: {
          id: mockOrderId,
          notes: 'Updated notes',
        },
      };

      await expect(OrderUseCase.validateOrderPatch(command)).rejects.toThrow(`Order ${mockOrderId} does not belong to company`);
    });

    it('should throw an error if no fields are provided for update', async () => {
      const command: validateOrderPatchCommand = {
        companyId: mockCompanyId,
        entity: mockOrder,
        params: {
          id: mockOrderId,
        },
      };

      await expect(OrderUseCase.validateOrderPatch(command)).rejects.toThrow('At least one field must be updated');
    });

    it('should throw an error if assigned user is not found', async () => {
      (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValue(null);

      const command: validateOrderPatchCommand = {
        companyId: mockCompanyId,
        entity: mockOrder,
        params: {
          id: mockOrderId,
          assignedUserId: 'non-existent-user-id',
        },
      };

      await expect(OrderUseCase.validateOrderPatch(command)).rejects.toThrow('Assigned user non-existent-user-id not found');
    });

    it('should throw an error if assigned user does not belong to company', async () => {
      (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValue({
        ...mockUser,
        companies: ['different-company-id'],
      });

      const command: validateOrderPatchCommand = {
        companyId: mockCompanyId,
        entity: mockOrder,
        params: {
          id: mockOrderId,
          assignedUserId: 'user-id',
        },
      };

      await expect(OrderUseCase.validateOrderPatch(command)).rejects.toThrow('Assigned user user-id does not belong to company');
    });
  });
});
