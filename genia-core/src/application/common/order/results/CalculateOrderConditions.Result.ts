import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import { CatalogDiscountWithPriceAfterDiscount, StoreDiscountWithPriceAfterDiscount } from '#domain/domainServices/Catalog.DomainService';

export interface OrderItemCalculated {
  catalogId: string;
  quantity: number;
  unitPrice: number;
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  discounts: {
    appliedDiscount: {
      catalogDiscount: CatalogDiscountWithPriceAfterDiscount | null;
      storeDiscount: StoreDiscountWithPriceAfterDiscount | null;
    };
    applicableDiscounts: {
      catalogDiscounts: CatalogDiscountWithPriceAfterDiscount[];
      storeDiscounts: StoreDiscountWithPriceAfterDiscount[];
    };
  } | null;
  taxes: {
    id: string;
    name: string;
    amount: number;
    type: TaxType;
    value: number;
  }[];
}
