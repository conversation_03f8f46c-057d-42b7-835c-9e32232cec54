import validateOrderPatchCommand from '#application/common/commands/ValidateOrderPatch.Command';
import { OrderItemCalculated } from '#application/common/order/results/CalculateOrderConditions.Result';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import ClientObject from '#domain/aggregates/client/Client.Entity';
import CatalogDomainService, { CatalogWithQuantity } from '#domain/domainServices/Catalog.DomainService';

async function getCatalogConditions(
  orderItems: { catalogId: string, quantity: number }[],
  providerCompanyId: string,
  client: ClientObject,
): Promise<OrderItemCalculated[]> {
  const catalogIds = orderItems.map((c) => c.catalogId);
  const catalogItems = await Registry.CatalogRepository.findManyByIds(catalogIds);
  const taxIds = catalogItems.map((c) => c.taxIds).flat();
  const taxes = (await Registry.TaxRepository.findManyByIds(taxIds)).filter((tax) => !tax.disabledAt);
  const catalogItemsMap = new Map(catalogItems.map((catalogItem) => [catalogItem.id, catalogItem]));

  const catalogWithQuantity: CatalogWithQuantity[] = orderItems.map((orderItem) => {
    const catalogItemRef = catalogItemsMap.get(orderItem.catalogId);

    if (!catalogItemRef) throw new Error(`Provider catalog item ${orderItem.catalogId} not found`, { cause: Cause.NOT_FOUND });

    if (catalogItemRef.companyId !== providerCompanyId) throw new Error('Catalog item does not belong to provider', { cause: Cause.BAD_REQUEST });

    return {
      ...catalogItemRef,
      quantity: orderItem.quantity,
    };
  });

  const catalogDiscounts = await Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds(
    [providerCompanyId],
    catalogIds,
    [client.id],
  );

  const globalCatalogDiscounts = await Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds(
    [providerCompanyId],
    catalogIds,
    null,
  );

  const storeDiscounts = await Registry.StoreDiscountRepository.findManyForCompaniesByClientIds([providerCompanyId], [client.id]);

  const globalStoreDiscounts = await Registry.StoreDiscountRepository.findManyForCompaniesByClientIds([providerCompanyId], null);

  const catalogWithDiscountsAndTaxes = CatalogDomainService.calculateCatalogDiscountAndTaxes({
    catalog: catalogWithQuantity,
    catalogDiscounts: [...catalogDiscounts, ...globalCatalogDiscounts],
    storeDiscounts: [...storeDiscounts, ...globalStoreDiscounts],
    client,
    taxes,
  });

  return catalogWithDiscountsAndTaxes.map((catalogItem) => ({
    catalogId: catalogItem.id,
    quantity: catalogItem.quantity,
    unitPrice: catalogItem.price,
    unitPriceAfterDiscount: catalogItem.priceAfterDiscount,
    unitPriceAfterDiscountAndTaxes: catalogItem.priceAfterTaxes,
    discounts: {
      appliedDiscount: catalogItem.appliedDiscount,
      applicableDiscounts: catalogItem.applicableDiscounts,
    },
    taxes: catalogItem.taxes.map((tax) => ({
      id: tax.id,
      name: tax.name,
      type: tax.type,
      value: tax.value,
      amount: tax.amount,
    })),
  }));
}

async function validateOrderPatch(command: validateOrderPatchCommand): Promise<void> {
  const {
    companyId, entity, params: {
      id, deliveryDate, notes, shippingAddress, status, assignedUserId,
    },
  } = command;

  if (assignedUserId) {
    const assignedUser = await Registry.UserRepository.findOneById(assignedUserId);

    if (!assignedUser) {
      throw new Error(`Assigned user ${assignedUserId} not found`, { cause: Cause.NOT_FOUND });
    }

    if (!assignedUser.companies.includes(companyId)) {
      throw new Error(`Assigned user ${assignedUserId} does not belong to company`, { cause: Cause.BAD_REQUEST });
    }
  }

  if (!entity) {
    throw new Error(`Order ${id} not found`, { cause: Cause.NOT_FOUND });
  }

  if (entity.companyId !== companyId) {
    throw new Error(`Order ${id} does not belong to company`, { cause: Cause.BAD_REQUEST });
  }

  if (deliveryDate === undefined && shippingAddress === undefined && status === undefined && notes === undefined && assignedUserId === undefined) {
    throw new Error('At least one field must be updated', { cause: Cause.BAD_REQUEST });
  }
}

export default {
  getCatalogConditions,
  validateOrderPatch,
};
