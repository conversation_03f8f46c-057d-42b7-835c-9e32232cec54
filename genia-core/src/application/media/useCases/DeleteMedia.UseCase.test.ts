import Registry from '#composition/ImplementationRegistry';
import { MediaEntityType } from '#domain/common/aggregates/media/MediaEntity';

import DeleteMediaUseCase from './DeleteMedia.UseCase';

jest.unmock('lodash');

jest.unmock('./DeleteMedia.UseCase');

jest.unmock('#application/Common.Type');

describe('DeleteMediaUseCase.apply', () => {
  const companyId = 'company-1';
  const catalogId = 'catalog-1';
  const inventoryId = 'inventory-1';
  const mediaIds = ['media-1', 'media-2'];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when entity is CATALOG', () => {
    it('should delete catalog media if all checks pass', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValue({ id: catalogId, companyId });
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'media-1', catalogId },
        { id: 'media-2', catalogId },
      ]);
      (Registry.CatalogMediaRepository.deleteByIds as jest.Mock).mockResolvedValue(undefined);

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.CATALOG,
        entityId: catalogId,
        companyId,
        mediaIds,
      })).resolves.toBeUndefined();

      expect(Registry.CatalogMediaRepository.deleteByIds).toHaveBeenCalledWith(mediaIds);
      expect(Registry.FileService.deleteFromStorage).toHaveBeenCalledWith(MediaEntityType.CATALOG, catalogId, 'media-1');
      expect(Registry.FileService.deleteFromStorage).toHaveBeenCalledWith(MediaEntityType.CATALOG, catalogId, 'media-2');
    });

    it('should throw if catalog not found', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValue(null);

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.CATALOG,
        entityId: catalogId,
        companyId,
        mediaIds,
      })).rejects.toThrow('Catalog with ID catalog-1 not found.');
    });

    it('should throw if catalog does not belong to company', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValue({ id: catalogId, companyId: 'other-company' });

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.CATALOG,
        entityId: catalogId,
        companyId,
        mediaIds,
      })).rejects.toThrow('Catalog with ID catalog-1 does not belong to company company-1.');
    });

    it('should throw if some medias are not found', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValue({ id: catalogId, companyId });
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'media-1', catalogId },
      ]);

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.CATALOG,
        entityId: catalogId,
        companyId,
        mediaIds,
      })).rejects.toThrow('Some medias where not found: media-2.');
    });

    it('should throw if some medias do not belong to the catalog', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValue({ id: catalogId, companyId });
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'media-1', catalogId },
        { id: 'media-2', catalogId: 'other-catalog' },
      ]);

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.CATALOG,
        entityId: catalogId,
        companyId,
        mediaIds,
      })).rejects.toThrow('Some medias do not belong to catalog catalog-1: media-2.');
    });
  });

  describe('when entity is INVENTORY', () => {
    it('should delete inventory media if all checks pass', async () => {
      (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValue({ id: inventoryId, companyId });
      (Registry.InventoryMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'media-1', inventoryId },
        { id: 'media-2', inventoryId },
      ]);
      (Registry.InventoryMediaRepository.deleteByIds as jest.Mock).mockResolvedValue(undefined);

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.INVENTORY,
        entityId: inventoryId,
        companyId,
        mediaIds,
      })).resolves.toBeUndefined();

      expect(Registry.InventoryMediaRepository.deleteByIds).toHaveBeenCalledWith(mediaIds);
      expect(Registry.FileService.deleteFromStorage).toHaveBeenCalledWith(MediaEntityType.INVENTORY, inventoryId, 'media-1');
      expect(Registry.FileService.deleteFromStorage).toHaveBeenCalledWith(MediaEntityType.INVENTORY, inventoryId, 'media-2');
    });

    it('should throw if inventory not found', async () => {
      (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValue(null);

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.INVENTORY,
        entityId: inventoryId,
        companyId,
        mediaIds,
      })).rejects.toThrow('Inventory with ID inventory-1 not found.');
    });

    it('should throw if inventory does not belong to company', async () => {
      (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValue({ id: inventoryId, companyId: 'other-company' });

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.INVENTORY,
        entityId: inventoryId,
        companyId,
        mediaIds,
      })).rejects.toThrow('Inventory with ID inventory-1 does not belong to company company-1.');
    });

    it('should throw if some medias are not found', async () => {
      (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValue({ id: inventoryId, companyId });
      (Registry.InventoryMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'media-1', inventoryId },
      ]);

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.INVENTORY,
        entityId: inventoryId,
        companyId,
        mediaIds,
      })).rejects.toThrow('Some medias where not found: media-2.');
    });

    it('should throw if some medias do not belong to the inventory', async () => {
      (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValue({ id: inventoryId, companyId });
      (Registry.InventoryMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'media-1', inventoryId },
        { id: 'media-2', inventoryId: 'other-inventory' },
      ]);

      await expect(DeleteMediaUseCase.apply({
        entity: MediaEntityType.INVENTORY,
        entityId: inventoryId,
        companyId,
        mediaIds,
      })).rejects.toThrow('Some medias do not belong to inventory inventory-1: media-2.');
    });
  });
});
