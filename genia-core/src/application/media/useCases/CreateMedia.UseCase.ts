import { ApplicationError } from '#application/Common.Type';
import CatalogErrorCodes from '#application/catalog/Catalog.ErrorCodes';
import MediaErrorCodes from '#application/media/Media.ErrorCodes';
import CreateMediaCommand from '#application/media/commands/CreateMedia.Command';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import CatalogMediaEntity from '#domain/aggregates/catalogMedia/CatalogMedia.Entity';
import CatalogMediaOperator from '#domain/aggregates/catalogMedia/CatalogMedia.Operator';
import InventoryMediaEntity from '#domain/aggregates/inventoryMedia/InventoryMedia.Entity';
import InventoryMediaOperator from '#domain/aggregates/inventoryMedia/InventoryMedia.Operator';
import MediaEntity, { MediaEntityType } from '#domain/common/aggregates/media/MediaEntity';

async function handleCatalogMedia(command: CreateMediaCommand): Promise<MediaEntity[]> {
  const { entityId, companyId, mediasInfo } = command;

  const foundCatalog = await Registry.CatalogRepository.findOneById(entityId);
  if (!foundCatalog) {
    throw new ApplicationError(
      `Catalog with ID ${entityId} not found.`,
      Cause.NOT_FOUND,
      CatalogErrorCodes.CATALOG_PRODUCT_NOT_FOUND,
    );
  }

  if (foundCatalog.companyId !== companyId) {
    throw new ApplicationError(
      `Catalog with ID ${entityId} does not belong to company ${companyId}.`,
      Cause.FORBIDDEN,
      CatalogErrorCodes.CATALOG_PRODUCT_FORBIDDEN,
    );
  }

  const catalogMediaEntities: CatalogMediaEntity[] = mediasInfo.map((mediaInfo) => {
    const { url } = mediaInfo;

    const id = url.split('/').pop()?.split('.')[0];

    if (!id) {
      throw new ApplicationError(`Invalid media URL: ${url}`, Cause.BAD_REQUEST, MediaErrorCodes.MEDIA_INVALID_URL);
    }

    return CatalogMediaOperator.build({
      id,
      url: mediaInfo.url,
      mediaType: mediaInfo.mediaType,
      catalogId: entityId,
    });
  });

  return Registry.CatalogMediaRepository.save(catalogMediaEntities);
}

async function handleInventoryMedia(command: CreateMediaCommand): Promise<MediaEntity[]> {
  const { entityId, companyId, mediasInfo } = command;

  const foundInventory = await Registry.InventoryRepository.findOneById(entityId);
  if (!foundInventory) {
    throw new Error(`Inventory with ID ${entityId} not found.`, { cause: Cause.NOT_FOUND });
  }

  if (foundInventory.companyId !== companyId) {
    throw new Error(`Inventory with ID ${entityId} does not belong to company ${companyId}.`, { cause: Cause.FORBIDDEN });
  }

  const inventoryMediaEntities: InventoryMediaEntity[] = mediasInfo.map((mediaInfo) => {
    const { url } = mediaInfo;

    const id = url.split('/').pop()?.split('.')[0];

    if (!id) {
      throw new ApplicationError(`Invalid media URL: ${url}`, Cause.BAD_REQUEST, MediaErrorCodes.MEDIA_INVALID_URL);
    }

    return InventoryMediaOperator.build({
      id,
      url: mediaInfo.url,
      mediaType: mediaInfo.mediaType,
      inventoryId: entityId,
    });
  });

  return Registry.InventoryMediaRepository.save(inventoryMediaEntities);
}

async function apply(command: CreateMediaCommand): Promise<MediaEntity[]> {
  const { entity } = command;

  if (entity === MediaEntityType.CATALOG) {
    return handleCatalogMedia(command);
  }

  return handleInventoryMedia(command);
}

export default {
  apply,
};
