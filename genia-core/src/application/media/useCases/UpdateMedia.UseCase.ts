import { ApplicationError } from '#application/Common.Type';
import UpdateMediaCommand from '#application/media/commands/UpdateMedia.Command';
import MediaErrorCodes from '#application/media/Media.ErrorCodes';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import CatalogMediaOperator from '#domain/aggregates/catalogMedia/CatalogMedia.Operator';
import InventoryMediaOperator from '#domain/aggregates/inventoryMedia/InventoryMedia.Operator';
import MediaEntity, { MediaEntityType } from '#domain/common/aggregates/media/MediaEntity';

async function handleCatalogMediaUpdate(command: UpdateMediaCommand): Promise<MediaEntity> {
  const { mediaId } = command;

  const [foundMedia] = await Registry.CatalogMediaRepository.findManyByIds([mediaId]);

  if (!foundMedia) {
    throw new ApplicationError(
      `Media with ID ${mediaId} not found in catalog.`,
      Cause.NOT_FOUND,
      MediaErrorCodes.MEDIA_NOT_FOUND,
    );
  }

  const updatedMedia = CatalogMediaOperator.update(foundMedia, {
    processing: command.processing,
  });

  const [record] = await Registry.CatalogMediaRepository.save([updatedMedia]);

  return record;
}

async function handleInventoryMediaUpdate(command: UpdateMediaCommand): Promise<MediaEntity> {
  const { mediaId } = command;

  const [foundMedia] = await Registry.InventoryMediaRepository.findManyByIds([mediaId]);

  if (!foundMedia) {
    throw new ApplicationError(
      `Media with ID ${mediaId} not found in inventory.`,
      Cause.NOT_FOUND,
      MediaErrorCodes.MEDIA_NOT_FOUND,
    );
  }

  const updatedMedia = InventoryMediaOperator.update(foundMedia, {
    processing: command.processing,
  });

  const [record] = await Registry.InventoryMediaRepository.save([updatedMedia]);

  return record;
}

async function apply(command: UpdateMediaCommand): Promise<MediaEntity> {
  const { entity } = command;

  if (entity === MediaEntityType.CATALOG) {
    return handleCatalogMediaUpdate(command);
  }

  return handleInventoryMediaUpdate(command);
}

export default {
  apply,
};
