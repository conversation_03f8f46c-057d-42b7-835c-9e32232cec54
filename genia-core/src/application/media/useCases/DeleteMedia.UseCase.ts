import { difference } from 'lodash';

import CatalogErrorCodes from '#application/catalog/Catalog.ErrorCodes';
import { ApplicationError } from '#application/Common.Type';
import InventoryErrorCodes from '#application/inventory/Inventory.ErrorCodes';
import DeleteMediaCommand from '#application/media/commands/DeleteMedia.Command';
import MediaErrorCodes from '#application/media/Media.ErrorCodes';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import { MediaEntityType } from '#domain/common/aggregates/media/MediaEntity';

async function handleCatalogMedia(command: DeleteMediaCommand): Promise<undefined> {
  const { entityId, companyId, mediaIds } = command;

  const foundCatalog = await Registry.CatalogRepository.findOneById(entityId);
  if (!foundCatalog) {
    throw new ApplicationError(
      `Catalog with ID ${entityId} not found.`,
      Cause.NOT_FOUND,
      CatalogErrorCodes.CATALOG_PRODUCT_NOT_FOUND,
    );
  }

  if (foundCatalog.companyId !== companyId) {
    throw new ApplicationError(
      `Catalog with ID ${entityId} does not belong to company ${companyId}.`,
      Cause.FORBIDDEN,
      CatalogErrorCodes.CATALOG_PRODUCT_FORBIDDEN,
    );
  }

  const foundCatalogMedias = await Registry.CatalogMediaRepository.findManyByIds(mediaIds);
  if (foundCatalogMedias.length !== mediaIds.length) {
    const setMediaIds = new Set(mediaIds);
    const foundIds = foundCatalogMedias.map((media) => media.id);
    const notFoundIds = difference(Array.from(setMediaIds), foundIds);

    throw new ApplicationError(
      `Some medias where not found: ${notFoundIds.join(',')}.`,
      Cause.NOT_FOUND,
      MediaErrorCodes.MEDIA_NOT_FOUND,
    );
  }

  const notCatalogMedias = foundCatalogMedias.filter((media) => media.catalogId !== entityId);
  if (notCatalogMedias.length > 0) {
    const notCatalogIds = notCatalogMedias.map((media) => media.id);

    throw new ApplicationError(
      `Some medias do not belong to catalog ${entityId}: ${notCatalogIds.join(',')}.`,
      Cause.FORBIDDEN,
      MediaErrorCodes.MEDIA_FORBIDDEN,
    );
  }

  await Registry.TransactionService.transactional(async () => {
    await Registry.CatalogMediaRepository.deleteByIds(mediaIds);

    const deletionPromises = mediaIds.map((mediaId) => Registry.FileService.deleteFromStorage(MediaEntityType.CATALOG, entityId, mediaId));

    await Promise.all(deletionPromises);
  });
}

async function handleInventoryMedia(command: DeleteMediaCommand): Promise<undefined> {
  const { entityId, companyId, mediaIds } = command;

  const foundInventory = await Registry.InventoryRepository.findOneById(entityId);
  if (!foundInventory) {
    throw new ApplicationError(
      `Inventory with ID ${entityId} not found.`,
      Cause.NOT_FOUND,
      InventoryErrorCodes.INVENTORY_PRODUCT_NOT_FOUND,
    );
  }

  if (foundInventory.companyId !== companyId) {
    throw new ApplicationError(
      `Inventory with ID ${entityId} does not belong to company ${companyId}.`,
      Cause.FORBIDDEN,
      InventoryErrorCodes.INVENTORY_PRODUCT_FORBIDDEN,
    );
  }

  const foundInventoryMedias = await Registry.InventoryMediaRepository.findManyByIds(mediaIds);
  if (foundInventoryMedias.length !== mediaIds.length) {
    const setMediaIds = new Set(mediaIds);
    const foundIds = foundInventoryMedias.map((media) => media.id);
    const notFoundIds = difference(Array.from(setMediaIds), foundIds);

    throw new ApplicationError(
      `Some medias where not found: ${notFoundIds.join(',')}.`,
      Cause.NOT_FOUND,
      MediaErrorCodes.MEDIA_NOT_FOUND,
    );
  }

  const notInventoryMedias = foundInventoryMedias.filter((media) => media.inventoryId !== entityId);
  if (notInventoryMedias.length > 0) {
    const notInventoryIds = notInventoryMedias.map((media) => media.id);

    throw new ApplicationError(
      `Some medias do not belong to inventory ${entityId}: ${notInventoryIds.join(',')}.`,
      Cause.FORBIDDEN,
      MediaErrorCodes.MEDIA_FORBIDDEN,
    );
  }

  await Registry.TransactionService.transactional(async () => {
    await Registry.InventoryMediaRepository.deleteByIds(mediaIds);

    const deletionPromises = mediaIds.map((mediaId) => Registry.FileService.deleteFromStorage(MediaEntityType.INVENTORY, entityId, mediaId));

    await Promise.all(deletionPromises);
  });
}

async function apply(command: DeleteMediaCommand): Promise<undefined> {
  const { entity } = command;

  if (entity === MediaEntityType.CATALOG) {
    return handleCatalogMedia(command);
  }

  return handleInventoryMedia(command);
}

export default {
  apply,
};
