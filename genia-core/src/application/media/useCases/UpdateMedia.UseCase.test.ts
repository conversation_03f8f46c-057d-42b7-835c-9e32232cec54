import Registry from '#composition/ImplementationRegistry';
import CatalogMediaOperator from '#domain/aggregates/catalogMedia/CatalogMedia.Operator';
import InventoryMediaOperator from '#domain/aggregates/inventoryMedia/InventoryMedia.Operator';
import { MediaEntityType } from '#domain/common/aggregates/media/MediaEntity';

import UpdateMediaUseCase from './UpdateMedia.UseCase';

jest.unmock('./UpdateMedia.UseCase');

jest.unmock('#application/Common.Type');

describe('UpdateMediaUseCase.apply', () => {
  const catalogId = 'catalog-1';
  const inventoryId = 'inventory-1';
  const mediaId = 'media-1';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when entity is CATALOG', () => {
    it('should update catalog media processing and return the updated entity', async () => {
      const foundMedia = { id: mediaId, catalogId, processing: true };
      const updatedMedia = { ...foundMedia, processing: false };

      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([foundMedia]);
      (CatalogMediaOperator.update as jest.Mock).mockReturnValue(updatedMedia);
      (Registry.CatalogMediaRepository.save as jest.Mock).mockResolvedValue([updatedMedia]);

      const command = {
        entity: MediaEntityType.CATALOG,
        mediaId,
        processing: false,
      };

      const result = await UpdateMediaUseCase.apply(command);

      expect(Registry.CatalogMediaRepository.findManyByIds).toHaveBeenCalledWith([mediaId]);
      expect(CatalogMediaOperator.update).toHaveBeenCalledWith(foundMedia, { processing: false });
      expect(Registry.CatalogMediaRepository.save).toHaveBeenCalledWith([updatedMedia]);
      expect(result).toEqual(updatedMedia);
    });

    it('should throw if media not found in catalog', async () => {
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      const command = {
        entity: MediaEntityType.CATALOG,
        mediaId,
        processing: false,
      };

      await expect(UpdateMediaUseCase.apply(command)).rejects.toThrow(`Media with ID ${mediaId} not found in catalog.`);
    });
  });

  describe('when entity is INVENTORY', () => {
    it('should update inventory media processing and return the updated entity', async () => {
      const foundMedia = { id: mediaId, inventoryId, processing: true };
      const updatedMedia = { ...foundMedia, processing: false };

      (Registry.InventoryMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([foundMedia]);
      (InventoryMediaOperator.update as jest.Mock).mockReturnValue(updatedMedia);
      (Registry.InventoryMediaRepository.save as jest.Mock).mockResolvedValue([updatedMedia]);

      const command = {
        entity: MediaEntityType.INVENTORY,
        mediaId,
        processing: false,
      };

      const result = await UpdateMediaUseCase.apply(command);

      expect(Registry.InventoryMediaRepository.findManyByIds).toHaveBeenCalledWith([mediaId]);
      expect(InventoryMediaOperator.update).toHaveBeenCalledWith(foundMedia, { processing: false });
      expect(Registry.InventoryMediaRepository.save).toHaveBeenCalledWith([updatedMedia]);
      expect(result).toEqual(updatedMedia);
    });

    it('should throw if media not found in inventory', async () => {
      (Registry.InventoryMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      const command = {
        entity: MediaEntityType.INVENTORY,
        mediaId,
        processing: false,
      };

      await expect(UpdateMediaUseCase.apply(command)).rejects.toThrow(`Media with ID ${mediaId} not found in inventory.`);
    });
  });
});
