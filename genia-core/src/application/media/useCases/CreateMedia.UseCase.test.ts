import Registry from '#composition/ImplementationRegistry';
import { MediaEntityType, MediaType } from '#domain/common/aggregates/media/MediaEntity';

import CreateMediaUseCase from './CreateMedia.UseCase';

jest.unmock('./CreateMedia.UseCase');

jest.unmock('#application/Common.Type');

describe('CreateMediaUseCase.apply', () => {
  const companyId = 'company-1';
  const catalogId = 'catalog-1';
  const inventoryId = 'inventory-1';
  const mediasInfo = [
    { mediaType: MediaType.IMAGE, url: 'https://example.com/img1.jpg' },
    { mediaType: MediaType.VIDEO, url: 'https://example.com/vid1.mp4' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when entity is CATALOG', () => {
    it('should save catalog media and return the result', async () => {
      const foundCatalog = { id: catalogId, companyId };
      const savedMedia = [
        {
          id: 'media-1', catalogId, url: mediasInfo[0].url, mediaType: mediasInfo[0].mediaType,
        },
        {
          id: 'media-2', catalogId, url: mediasInfo[1].url, mediaType: mediasInfo[1].mediaType,
        },
      ];

      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValue(foundCatalog);
      (Registry.CatalogMediaRepository.save as jest.Mock).mockResolvedValue(savedMedia);

      const command = {
        entity: MediaEntityType.CATALOG,
        entityId: catalogId,
        companyId,
        mediasInfo,
      };

      const result = await CreateMediaUseCase.apply(command);

      expect(Registry.CatalogRepository.findOneById).toHaveBeenCalledWith(catalogId);
      expect(Registry.CatalogMediaRepository.save).toHaveBeenCalled();
      expect(result).toEqual(savedMedia);
    });

    it('should throw if catalog does not exist', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValue(null);

      const command = {
        entity: MediaEntityType.CATALOG,
        entityId: catalogId,
        companyId,
        mediasInfo,
      };

      await expect(CreateMediaUseCase.apply(command)).rejects.toThrow(`Catalog with ID ${catalogId} not found.`);
    });

    it('should throw if catalog does not belong to company', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValue({ id: catalogId, companyId: 'other-company' });

      const command = {
        entity: MediaEntityType.CATALOG,
        entityId: catalogId,
        companyId,
        mediasInfo,
      };

      await expect(CreateMediaUseCase.apply(command)).rejects.toThrow(`Catalog with ID ${catalogId} does not belong to company ${companyId}.`);
    });
  });

  describe('when entity is INVENTORY', () => {
    it('should save inventory media and return the result', async () => {
      const foundInventory = { id: inventoryId, companyId };
      const savedMedia = [
        {
          id: 'media-1', inventoryId, url: mediasInfo[0].url, mediaType: mediasInfo[0].mediaType,
        },
        {
          id: 'media-2', inventoryId, url: mediasInfo[1].url, mediaType: mediasInfo[1].mediaType,
        },
      ];

      (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValue(foundInventory);
      (Registry.InventoryMediaRepository.save as jest.Mock).mockResolvedValue(savedMedia);

      const command = {
        entity: MediaEntityType.INVENTORY,
        entityId: inventoryId,
        companyId,
        mediasInfo,
      };

      const result = await CreateMediaUseCase.apply(command);

      expect(Registry.InventoryRepository.findOneById).toHaveBeenCalledWith(inventoryId);
      expect(Registry.InventoryMediaRepository.save).toHaveBeenCalled();
      expect(result).toEqual(savedMedia);
    });

    it('should throw if inventory does not exist', async () => {
      (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValue(null);

      const command = {
        entity: MediaEntityType.INVENTORY,
        entityId: inventoryId,
        companyId,
        mediasInfo,
      };

      await expect(CreateMediaUseCase.apply(command)).rejects.toThrow(`Inventory with ID ${inventoryId} not found.`);
    });

    it('should throw if inventory does not belong to company', async () => {
      (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValue({ id: inventoryId, companyId: 'other-company' });

      const command = {
        entity: MediaEntityType.INVENTORY,
        entityId: inventoryId,
        companyId,
        mediasInfo,
      };

      await expect(CreateMediaUseCase.apply(command)).rejects.toThrow(`Inventory with ID ${inventoryId} does not belong to company ${companyId}.`);
    });
  });
});
