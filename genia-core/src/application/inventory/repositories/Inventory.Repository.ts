import InventoryEntity from '#domain/aggregates/inventory/Inventory.Entity';

export default interface InventoryRepository {
  save(inventory: InventoryEntity[]): Promise<InventoryEntity[]>;
  findOneById(id: string): Promise<InventoryEntity | undefined>;
  findManyBySkus(skus: string[]): Promise<InventoryEntity[]>
  findManyByIds(ids: string[]): Promise<InventoryEntity[]>;
  findManyForCompanyByProviderSkus(companyId: string, providerId: string, providerSkus: string[]): Promise<InventoryEntity[]>;
  findManyForCompanyBySkus(companyId: string, skus: string[]): Promise<InventoryEntity[]>
}
