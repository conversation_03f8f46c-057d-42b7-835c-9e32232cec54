import { InventoryType } from '#domain/aggregates/inventory/Inventory.Entity';
import { AttributeType } from '#domain/common/Common.Type';

export default interface CreateInventoryEntriesCommand {
  entries: {
    sku?: string;
    name: string;
    description?: string;
    stock: number;
    attributes?: AttributeType[];
    providers?: {
      providerId: string,
      currentPurchasePrice: number,
      currentDiscount?: number | null,
      providerProductSku?: string | null
    }[];
    standardIdentifier?: string;
    type: InventoryType;
    measurementUnit: string;
    hasStockValidation: boolean;
  }[],
  companyId: string;
}
