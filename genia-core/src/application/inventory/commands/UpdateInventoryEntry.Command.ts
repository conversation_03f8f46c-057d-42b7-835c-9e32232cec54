import { InventoryType } from '#domain/aggregates/inventory/Inventory.Entity';
import { AttributeType } from '#domain/common/Common.Type';

export default interface UpdateInventoryEntryCommand {
  id: string;
  sku?: string | null;
  name?: string;
  description?: string;
  attributes?: AttributeType[];
  providers?: {
    providerId: string;
    currentPurchasePrice: number;
    currentDiscount?: number | null;
    providerProductSku?: string | null;
  }[];
  companyId: string;
  standardIdentifier?: string;
  type?: InventoryType;
  measurementUnit?: string;
  hasStockValidation?: boolean;
  disabledAt?: Date;
}
