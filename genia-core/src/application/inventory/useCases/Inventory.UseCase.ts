import CreateInventoryEntriesCommand from '#application/inventory/commands/CreateInventoryEntries.Command';
import UpdateInventoryCommand from '#application/inventory/commands/UpdateInventoryEntry.Command';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import InventoryEntity from '#domain/aggregates/inventory/Inventory.Entity';
import InventoryOperator from '#domain/aggregates/inventory/Inventory.Operator';

async function updateInventoryEntry(updateInventoryCommand: UpdateInventoryCommand): Promise<InventoryEntity> {
  const inventory = await Registry.InventoryRepository.findOneById(updateInventoryCommand.id);
  if (!inventory) throw new Error('Inventory not found', { cause: Cause.NOT_FOUND });

  let { sku: newSku } = updateInventoryCommand;
  const { sku: oldSku } = inventory;

  if (newSku && (oldSku !== newSku)) {
    const inventoryAlreadyExists = !!(await Registry.InventoryRepository.findManyBySkus([newSku])).length;

    if (inventoryAlreadyExists) throw new Error(`Inventory with sku: ${newSku} already exists`, { cause: Cause.CONFLICT });
  }

  return Registry.TransactionService.transactional(async () => {
    if (!newSku && newSku === null) {
      newSku = await Registry.IdentificationService.generateObjectSequenceForCompany(updateInventoryCommand.companyId, 'inventory');
    } else {
      newSku = oldSku;
    }

    const updatedInventory = InventoryOperator.update(inventory, { ...updateInventoryCommand, sku: newSku });

    await Registry.InventoryRepository.save([updatedInventory]);

    return updatedInventory;
  });
}

async function createInventoryEntries(createInventoryEntriesCommand: CreateInventoryEntriesCommand): Promise<InventoryEntity[]> {
  const { companyId, entries } = createInventoryEntriesCommand;
  const providedSkus = entries.reduce<string[]>((accumSkus, entry) => {
    const { sku } = entry;

    if (sku) return [...accumSkus, sku];

    return accumSkus;
  }, []);

  const foundInventory = await Registry.InventoryRepository.findManyBySkus(providedSkus);
  if (foundInventory.length) {
    const inventoryIds = foundInventory.map((inventory) => inventory.sku);
    throw new Error(`Those inventory already exist: ${inventoryIds.join(',')}`, { cause: Cause.CONFLICT });
  }

  return Registry.TransactionService.transactional(async () => {
    const inventoryEntries = [];

    for (const entry of entries) {
      let { sku } = entry;
      const id = await Registry.IdentificationService.generateId();

      if (!sku) {
        sku = await Registry.IdentificationService.generateObjectSequenceForCompany(companyId, 'inventory');
      }

      const inventoryEntry = InventoryOperator.build({
        ...entry, id, sku, companyId,
      });
      inventoryEntries.push(inventoryEntry);
    }

    const inventory = await Registry.InventoryRepository.save(inventoryEntries);

    return inventory;
  });
}

export default {
  updateInventoryEntry,
  createInventoryEntries,
};
