import UpdateInventoryStockCommand from '#application/inventory/commands/UpdateInventoryStock.Command';
import Registry from '#composition/ImplementationRegistry';
import InventoryOperator from '#domain/aggregates/inventory/Inventory.Operator';
import { InventoryMovementType } from '#domain/aggregates/inventory/InventoryHistory.Entity';
import InventoryMovementOperator from '#domain/aggregates/inventory/InventoryHistory.Operator';

import UpdateInventoryStockUseCase from './UpdateInventoryStock.UseCase';

jest.unmock('./UpdateInventoryStock.UseCase');
jest.unmock('#application/Common.Type');

jest.unmock('#application/Common.Type');
jest.mock('#composition/ImplementationRegistry');
jest.mock('#domain/aggregates/inventory/Inventory.Operator');
jest.mock('#domain/aggregates/inventory/InventoryHistory.Operator');

describe('UpdateInventoryStockUseCase.apply', () => {
  const mockInventory = {
    id: 'inv-1',
    companyId: 'company-1',
    stock: 10,
    measurementUnit: 'units',
  };

  const mockCommand: UpdateInventoryStockCommand = {
    inventoryId: 'inv-1',
    quantity: 5,
    movementType: InventoryMovementType.INBOUND,
    userId: 'user-1',
    reason: 'Restock',
    companyId: 'company-1',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValue(mockInventory);
    (InventoryOperator.update as jest.Mock).mockImplementation((inv, update) => ({ ...inv, ...update }));
    (InventoryMovementOperator.build as jest.Mock).mockImplementation((params) => params);
    (Registry.TransactionService.transactional as jest.Mock).mockImplementation((fn) => fn());
    (Registry.InventoryRepository.save as jest.Mock).mockResolvedValue([mockInventory]);
    (Registry.InventoryHistoryRepository.save as jest.Mock).mockResolvedValue([{}]);
  });

  it('should update inventory stock and save movement (INBOUND)', async () => {
    await UpdateInventoryStockUseCase.apply(mockCommand);

    expect(Registry.InventoryRepository.findOneById).toHaveBeenCalledWith('inv-1');
    expect(InventoryOperator.update).toHaveBeenCalledWith(mockInventory, { stock: 15 });
    expect(InventoryMovementOperator.build).toHaveBeenCalledWith(expect.objectContaining({
      inventoryId: 'inv-1',
      quantity: 5,
      measurementUnit: 'units',
      movementType: InventoryMovementType.INBOUND,
      reason: 'Restock',
      userId: 'user-1',
    }));
    expect(Registry.TransactionService.transactional).toHaveBeenCalled();
    expect(Registry.InventoryRepository.save).toHaveBeenCalled();
    expect(Registry.InventoryHistoryRepository.save).toHaveBeenCalled();
  });

  it('should update inventory stock and save movement (OUTBOUND)', async () => {
    const outboundCommand = { ...mockCommand, movementType: InventoryMovementType.OUTBOUND, quantity: 3 };
    await UpdateInventoryStockUseCase.apply(outboundCommand);

    expect(InventoryOperator.update).toHaveBeenCalledWith(mockInventory, { stock: 7 });
    expect(InventoryMovementOperator.build).toHaveBeenCalledWith(expect.objectContaining({
      movementType: InventoryMovementType.OUTBOUND,
      quantity: 3,
    }));
  });

  it('should throw if inventory not found', async () => {
    (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValueOnce(null);

    await expect(UpdateInventoryStockUseCase.apply(mockCommand)).rejects.toThrow('Inventory not found');
  });

  it('should throw if inventory does not belong to the company', async () => {
    (Registry.InventoryRepository.findOneById as jest.Mock).mockResolvedValueOnce({ ...mockInventory, companyId: 'other-company' });

    await expect(UpdateInventoryStockUseCase.apply(mockCommand)).rejects.toThrow('Inventory does not belong to the company');
  });

  it('should call transactional and save both inventory and movement', async () => {
    await UpdateInventoryStockUseCase.apply(mockCommand);

    expect(Registry.TransactionService.transactional).toHaveBeenCalled();
    expect(Registry.InventoryRepository.save).toHaveBeenCalled();
    expect(Registry.InventoryHistoryRepository.save).toHaveBeenCalled();
  });
});
