import CreateInventoryEntriesCommand from '#application/inventory/commands/CreateInventoryEntries.Command';
import UpdateInventoryCommand from '#application/inventory/commands/UpdateInventoryEntry.Command';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import InventoryEntity, { InventoryType } from '#domain/aggregates/inventory/Inventory.Entity';
import InventoryOperator from '#domain/aggregates/inventory/Inventory.Operator';

import InventoryUseCase from './Inventory.UseCase';

jest.unmock('./Inventory.UseCase');

describe('InventoryUseCase', () => {
  const mockDate = new Date('2023-01-01');

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('updateInventoryEntry', () => {
    const updateCommand: UpdateInventoryCommand = {
      id: 'inventory123',
      sku: 'SKU-001-UPDATE',
      name: 'Updated Item',
      companyId: 'company123',
    };

    const existingInventory: InventoryEntity = {
      id: 'inventory123',
      sku: 'SKU-001',
      name: 'Original Item',
      description: '',
      stock: 0,
      restrictedStock: 0,
      disabledAt: null,
      attributes: [{
        key: 'viscoisidad',
        values: ['alta'],
      }],
      companyId: 'company123',
      standardIdentifier: '',
      providers: [],
      type: InventoryType.COMMODITY,
      measurementUnit: 'unit',
      hasStockValidation: true,
      createdAt: mockDate,
      updatedAt: mockDate,
    };

    const inventoryRepository = Registry.InventoryRepository;

    beforeEach(() => {
      (InventoryOperator.update as jest.Mock).mockReturnValue({
        ...existingInventory,
        ...updateCommand,
        updatedAt: mockDate,
      });
    });

    it('Should update an inventory entry successfully', async () => {
      (inventoryRepository.findOneById as jest.Mock).mockResolvedValue(existingInventory);
      (inventoryRepository.save as jest.Mock).mockResolvedValue({
        ...existingInventory,
        ...updateCommand,
        updatedAt: mockDate,
      });

      const updatedInventory = await InventoryUseCase.updateInventoryEntry(updateCommand);

      expect(updatedInventory.sku).toBe(updateCommand.sku);
      expect(updatedInventory.name).toBe(updateCommand.name);
      expect(updatedInventory.updatedAt).toEqual(mockDate);
      expect(inventoryRepository.findOneById).toHaveBeenCalledWith(updateCommand.id);
      expect(InventoryOperator.update).toHaveBeenCalled();
      expect(inventoryRepository.save).toHaveBeenCalled();
    });

    it('Should not generete a new sku when the sku is not being updated', async () => {
      (inventoryRepository.findOneById as jest.Mock).mockResolvedValue(existingInventory);
      await InventoryUseCase.updateInventoryEntry({
        ...updateCommand,
        sku: undefined,
      });

      expect(InventoryOperator.update).toHaveBeenCalledWith(existingInventory, { ...updateCommand, sku: existingInventory.sku });
    });

    it('Should generate a new sku when the sku is sent as null', async () => {
      (inventoryRepository.findOneById as jest.Mock).mockResolvedValue(existingInventory);
      await InventoryUseCase.updateInventoryEntry({
        ...updateCommand,
        sku: null,
      });

      expect(InventoryOperator.update).toHaveBeenCalledWith(existingInventory, { ...updateCommand, sku: 'PREFIX-1' });
    });

    it('Should throw error when inventory not found', async () => {
      (inventoryRepository.findOneById as jest.Mock).mockResolvedValueOnce(undefined);

      await expect(InventoryUseCase.updateInventoryEntry(updateCommand))
        .rejects.toThrow(new Error('Inventory not found', { cause: Cause.NOT_FOUND }));
    });

    it('Should throw error when the inventory sku is being updated for an existing one', async () => {
      (inventoryRepository.findManyBySkus as jest.Mock).mockResolvedValueOnce([updateCommand]);

      await expect(InventoryUseCase.updateInventoryEntry(updateCommand))
        .rejects.toThrow(new Error('Inventory with sku: SKU-001-UPDATE already exists', { cause: Cause.CONFLICT }));
    });
  });

  describe('createInventoryEntries', () => {
    const createCommand: CreateInventoryEntriesCommand = {
      companyId: 'company123',
      entries: [
        {
          name: 'Item 1',
          sku: 'SKU-001',
          description: '',
          stock: 0,
          attributes: [{
            key: 'viscoisidad',
            values: ['alta'],
          }],
          type: InventoryType.COMMODITY,
          measurementUnit: 'unit',
          hasStockValidation: true,
        },
        {
          name: 'Item 2',
          description: '',
          stock: 0,
          attributes: [{
            key: 'tipo',
            categories: [
              {
                key: 'carro',
                values: ['renault', 'toyota'],
              },
              {
                key: 'moto',
                values: ['honda', 'yamaha'],
              },
            ],
          }],
          type: InventoryType.COMMODITY,
          measurementUnit: 'unit',
          hasStockValidation: true,
        },
      ],
    };

    const inventoryRepository = Registry.InventoryRepository;
    const identificationService = Registry.IdentificationService;

    it('Should create multiple inventory entries successfully', async () => {
      (InventoryOperator.build as jest.Mock)
        .mockReturnValueOnce({
          ...createCommand.entries[0],
          id: 'id1',
          sku: 'SKU-001',
          companyId: createCommand.companyId,
          createdAt: mockDate,
          updatedAt: mockDate,
        }).mockReturnValueOnce({
          ...createCommand.entries[1],
          id: 'id2',
          sku: 'AUTO-SKU-001',
          companyId: createCommand.companyId,
          createdAt: mockDate,
          updatedAt: mockDate,
        });

      (identificationService.generateId as jest.Mock)
        .mockResolvedValueOnce('id1')
        .mockResolvedValueOnce('id2');

      (identificationService.generateObjectSequenceForCompany as jest.Mock)
        .mockResolvedValue('AUTO-SKU-001');

      (inventoryRepository.save as jest.Mock)
        .mockImplementation((entries) => Promise.resolve(entries));

      (inventoryRepository.findManyBySkus as jest.Mock)
        .mockImplementation(() => Promise.resolve([] as InventoryEntity[]));

      const result = await InventoryUseCase.createInventoryEntries(createCommand);

      expect(result).toHaveLength(2);
      expect(identificationService.generateId).toHaveBeenCalledTimes(2);
      expect(identificationService.generateObjectSequenceForCompany).toHaveBeenCalledTimes(1);
      expect(inventoryRepository.save).toHaveBeenCalled();
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();

      expect(result[0].sku).toBe('SKU-001');
      expect(result[1].sku).toBe('AUTO-SKU-001');
      expect(result[0].createdAt).toEqual(mockDate);
      expect(result[0].updatedAt).toEqual(mockDate);
    });

    it('Should reject when the provided skus already exist', async () => {
      (inventoryRepository.findManyBySkus as jest.Mock)
        .mockImplementationOnce(() => Promise.resolve([{
          ...createCommand.entries[0],
        }]));

      await expect(InventoryUseCase.createInventoryEntries(createCommand))
        .rejects.toThrow(new Error('Those inventory already exist: SKU-001', { cause: Cause.CONFLICT }));
    });

    it('Should handle transaction failure', async () => {
      (Registry.TransactionService.transactional as jest.Mock)
        .mockRejectedValueOnce(new Error('Transaction failed'));

      await expect(InventoryUseCase.createInventoryEntries(createCommand))
        .rejects.toThrow('Transaction failed');
    });
  });
});
