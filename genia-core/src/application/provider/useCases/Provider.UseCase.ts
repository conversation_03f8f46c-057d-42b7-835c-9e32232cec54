import { difference } from 'lodash';

import CreateProviderEntriesCommand from '#application/provider/commands/CreateProviderEntries.Command';
import UpdateProviderEntryCommand from '#application/provider/commands/UpdateProviderEntry.Command';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import ProviderOperator from '#domain/aggregates/provider/Provider.Operator';

async function createProviderEntries(createProviderEntriesCommand: CreateProviderEntriesCommand): Promise<ProviderEntity[]> {
  const { entries, companyId } = createProviderEntriesCommand;
  const setProviderCompanyIds = new Set<string>();
  const setProviderNames = new Set<string>();

  entries.forEach((entry) => {
    const { providerCompanyId, name } = entry;

    if (setProviderNames.has(name)) throw new Error(`Name: "${name}" is provided for more than one entry`, { cause: Cause.CONFLICT });
    setProviderNames.add(name);

    if (providerCompanyId) setProviderCompanyIds.add(providerCompanyId);
  });

  const providerNames = Array.from(setProviderNames);
  const providerCompanyIds = Array.from(setProviderCompanyIds);

  const foundProvidersWithRepeatedName = await Registry.ProviderRepository.findManyByNamesAndCompanyId(providerNames, companyId);
  if (foundProvidersWithRepeatedName.length) {
    const repeatedNames = foundProvidersWithRepeatedName.map<string>(({ name }) => name);
    throw new Error(`Some names were alredy taken: ${repeatedNames.join(',')}`, { cause: 'BAD_REQUEST' });
  }

  if (providerCompanyIds.length) {
    const foundProviderCompanies = await Registry.CompanyRepository.findManyByIds(providerCompanyIds);

    if (foundProviderCompanies.length !== providerCompanyIds.length) {
      const foundIds = foundProviderCompanies.map(({ id }) => id);
      const notFoundIds = difference(providerCompanyIds, foundIds);
      throw new Error(`Some company were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
    }
  }

  const providers = await Promise.all(entries.map(async (entry) => {
    const id = await Registry.IdentificationService.generateId();

    return ProviderOperator.build({ ...entry, id, companyId });
  }));

  return Registry.ProviderRepository.saveMany(providers);
}

async function deleteProviderEntry(id: string, companyId: string): Promise<undefined> {
  const found = await Registry.ProviderRepository.findOneById(id);

  if (!found) throw new Error('Provider not found', { cause: Cause.NOT_FOUND });

  if (found.companyId !== companyId) throw new Error('Provider does not belong to the provided company', { cause: Cause.FORBIDDEN });

  return Registry.ProviderRepository.deleteById(id);
}

async function updateProviderEntry(updateProviderEntryCommand: UpdateProviderEntryCommand): Promise<ProviderEntity> {
  const { companyId, id, name } = updateProviderEntryCommand;

  const foundProvider = await Registry.ProviderRepository.findOneById(id);

  if (!foundProvider) throw new Error('Provider not found', { cause: Cause.NOT_FOUND });

  if (companyId !== foundProvider.companyId) throw new Error('Forbidden', { cause: Cause.FORBIDDEN });

  if (foundProvider.providerCompanyId) throw new Error('Provider can\'t be updated', { cause: Cause.BAD_REQUEST });

  if (name && (name !== foundProvider.name)) {
    const companyWithSameName = await Registry.ProviderRepository.findOneByNameAndCompanyId(name, companyId);

    if (companyWithSameName) throw new Error('Provider name already taken', { cause: Cause.CONFLICT });
  }

  const updated = ProviderOperator.update(foundProvider, updateProviderEntryCommand);

  return Registry.ProviderRepository.save(updated);
}

export default {
  createProviderEntries,
  deleteProviderEntry,
  updateProviderEntry,
};
