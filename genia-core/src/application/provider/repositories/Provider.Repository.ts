import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';

export default interface ProviderRepository {
  saveMany(Provider: ProviderEntity[]): Promise<ProviderEntity[]>;
  deleteById(id: string): Promise<undefined>
  save(Provider: ProviderEntity): Promise<ProviderEntity>;
  findOneById(id: string): Promise<ProviderEntity | undefined>;
  findOneByNameAndCompanyId(name: string, companyId: string): Promise<ProviderEntity | undefined>;
  findManyByNamesAndCompanyId(names: string[], companyId: string): Promise<ProviderEntity[]>;
  findProvidersForCompany(companyId: string): Promise<ProviderEntity[]>;
  findProviderForCompanyByProviderCompanyId(companyId: string, providerCompanyId: string): Promise<ProviderEntity | undefined>;
}
