import { Integration } from '#application/integration/services/Integration.Service';
import Registry from '#composition/ImplementationRegistry';

import DeleteIntegrationEntryUseCase, { DeleteIntegrationEntryCommand } from './DeleteIntegrationEntry.UseCase';

jest.unmock('./DeleteIntegrationEntry.UseCase');
jest.unmock('#application/Common.Type');

describe('DeleteIntegrationEntryUseCase', () => {
  const mockIntegration: Integration = {
    id: 'integration-id-1',
    type: 'shopify',
    params: {
      store_url: 'test-store.myshopify.com',
      api_version: '2023-04',
    },
    companyId: 'company-id',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const defaultCommand: DeleteIntegrationEntryCommand = {
    integrationId: 'integration-id-1',
    companyId: 'company-id',
  };

  beforeEach(() => {
    jest.restoreAllMocks();
    jest.resetAllMocks();
    (Registry.IntegrationService.retrieveIntegrationById as jest.Mock).mockResolvedValue(mockIntegration);
    (Registry.IntegrationService.removeIntegration as jest.Mock).mockResolvedValue(undefined);
  });

  describe('apply', () => {
    it('should delete integration entry successfully', async () => {
      await DeleteIntegrationEntryUseCase.apply(defaultCommand);

      expect(Registry.IntegrationService.retrieveIntegrationById).toHaveBeenCalledWith('integration-id-1');
      expect(Registry.IntegrationService.removeIntegration).toHaveBeenCalledWith('integration-id-1');
    });

    it('should throw error when integration not found', async () => {
      (Registry.IntegrationService.retrieveIntegrationById as jest.Mock).mockResolvedValue(null);

      await expect(
        DeleteIntegrationEntryUseCase.apply(defaultCommand),
      ).rejects.toThrow('Integration entry not found');

      expect(Registry.IntegrationService.retrieveIntegrationById).toHaveBeenCalledWith('integration-id-1');
      expect(Registry.IntegrationService.removeIntegration).not.toHaveBeenCalled();
    });

    it('should throw error when integration does not belong to company', async () => {
      const integrationFromDifferentCompany: Integration = {
        ...mockIntegration,
        companyId: 'different-company-id',
      };

      (Registry.IntegrationService.retrieveIntegrationById as jest.Mock).mockResolvedValue(integrationFromDifferentCompany);

      await expect(
        DeleteIntegrationEntryUseCase.apply(defaultCommand),
      ).rejects.toThrow('Integration entry does not belong to the company');

      expect(Registry.IntegrationService.retrieveIntegrationById).toHaveBeenCalledWith('integration-id-1');
      expect(Registry.IntegrationService.removeIntegration).not.toHaveBeenCalled();
    });

    it('should handle different integration types', async () => {
      const woocommerceIntegration: Integration = {
        id: 'woocommerce-integration-id',
        type: 'woocommerce',
        params: {
          site_url: 'test-site.com',
          api_key: 'encrypted-api-key',
        },
        companyId: 'company-id',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const woocommerceCommand: DeleteIntegrationEntryCommand = {
        integrationId: 'woocommerce-integration-id',
        companyId: 'company-id',
      };

      (Registry.IntegrationService.retrieveIntegrationById as jest.Mock).mockResolvedValue(woocommerceIntegration);

      await DeleteIntegrationEntryUseCase.apply(woocommerceCommand);

      expect(Registry.IntegrationService.retrieveIntegrationById).toHaveBeenCalledWith('woocommerce-integration-id');
      expect(Registry.IntegrationService.removeIntegration).toHaveBeenCalledWith('woocommerce-integration-id');
    });

    it('should handle integrations without params', async () => {
      const simpleIntegration: Integration = {
        id: 'simple-integration-id',
        type: 'simple-integration',
        companyId: 'company-id',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const simpleCommand: DeleteIntegrationEntryCommand = {
        integrationId: 'simple-integration-id',
        companyId: 'company-id',
      };

      (Registry.IntegrationService.retrieveIntegrationById as jest.Mock).mockResolvedValue(simpleIntegration);

      await DeleteIntegrationEntryUseCase.apply(simpleCommand);

      expect(Registry.IntegrationService.retrieveIntegrationById).toHaveBeenCalledWith('simple-integration-id');
      expect(Registry.IntegrationService.removeIntegration).toHaveBeenCalledWith('simple-integration-id');
    });

    it('should handle service error when retrieving integration', async () => {
      const serviceError = new Error('Database connection failed');
      (Registry.IntegrationService.retrieveIntegrationById as jest.Mock).mockRejectedValue(serviceError);

      await expect(
        DeleteIntegrationEntryUseCase.apply(defaultCommand),
      ).rejects.toThrow('Database connection failed');

      expect(Registry.IntegrationService.retrieveIntegrationById).toHaveBeenCalledWith('integration-id-1');
      expect(Registry.IntegrationService.removeIntegration).not.toHaveBeenCalled();
    });

    it('should handle service error when removing integration', async () => {
      const removalError = new Error('Failed to remove integration');
      (Registry.IntegrationService.removeIntegration as jest.Mock).mockRejectedValue(removalError);

      await expect(
        DeleteIntegrationEntryUseCase.apply(defaultCommand),
      ).rejects.toThrow('Failed to remove integration');

      expect(Registry.IntegrationService.retrieveIntegrationById).toHaveBeenCalledWith('integration-id-1');
      expect(Registry.IntegrationService.removeIntegration).toHaveBeenCalledWith('integration-id-1');
    });

    it('should validate command parameters', async () => {
      const emptyIntegrationIdCommand: DeleteIntegrationEntryCommand = {
        integrationId: '',
        companyId: 'company-id',
      };

      (Registry.IntegrationService.retrieveIntegrationById as jest.Mock).mockResolvedValue(null);

      await expect(
        DeleteIntegrationEntryUseCase.apply(emptyIntegrationIdCommand),
      ).rejects.toThrow('Integration entry not found');

      expect(Registry.IntegrationService.retrieveIntegrationById).toHaveBeenCalledWith('');
      expect(Registry.IntegrationService.removeIntegration).not.toHaveBeenCalled();
    });

    it('should handle concurrent deletion attempts', async () => {
      // Simulate a case where the integration is deleted between the check and the removal
      (Registry.IntegrationService.retrieveIntegrationById as jest.Mock).mockResolvedValue(mockIntegration);
      const concurrencyError = new Error('Integration not found for deletion');
      (Registry.IntegrationService.removeIntegration as jest.Mock).mockRejectedValue(concurrencyError);

      await expect(
        DeleteIntegrationEntryUseCase.apply(defaultCommand),
      ).rejects.toThrow('Integration not found for deletion');

      expect(Registry.IntegrationService.retrieveIntegrationById).toHaveBeenCalledWith('integration-id-1');
      expect(Registry.IntegrationService.removeIntegration).toHaveBeenCalledWith('integration-id-1');
    });
  });
});
