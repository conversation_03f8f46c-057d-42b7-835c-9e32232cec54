import { ApplicationError } from '#application/Common.Type';
import IntegrationErrorCodes from '#application/integration/Integration.ErrorCodes';
import { CreateIntegrationParams, Integration } from '#application/integration/services/Integration.Service';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import Logger from '#composition/Logger';

export interface CreateIntegrationEntriesCommand {
  entries: Omit<CreateIntegrationParams, 'companyId'>[];
  companyId: string;
}

const logger = Logger.getLogger().child({ module: 'CreateIntegrationEntries.UseCase' });

async function apply(command: CreateIntegrationEntriesCommand): Promise<Integration[]> {
  const { entries, companyId } = command;

  const findExistingIntegrationsForCompany = await Registry.IntegrationService.retrieveIntegrationsForCompany(companyId);

  const existingIntegrationTypes = new Set(findExistingIntegrationsForCompany.map((i) => i.type));

  logger.info(command.entries.map((e) => e.type), 'Creating integration entries for company', { companyId });

  if (entries.some((entry) => existingIntegrationTypes.has(entry.type))) {
    logger.error('Integration entries already exist for the company', { companyId });

    throw new ApplicationError(
      'Integration entries already exist for the company',
      Cause.CONFLICT,
      IntegrationErrorCodes.INTEGRATION_ALREADY_EXISTS,
    );
  }

  const createdIntegration = await Registry.IntegrationService.createIntegrations(entries.map((entry) => ({
    ...entry,
    companyId,
  })));

  logger.info(createdIntegration, 'Integration created');
  return createdIntegration;
}

const CreateIntegrationEntriesUseCase = {
  apply,
};

export default CreateIntegrationEntriesUseCase;
