import { Integration } from '#application/integration/services/Integration.Service';
import Registry from '#composition/ImplementationRegistry';

import CreateIntegrationEntriesUseCase, { CreateIntegrationEntriesCommand } from './CreateIntegrationEntries.UseCase';

jest.unmock('./CreateIntegrationEntries.UseCase');
jest.unmock('#application/Common.Type');

describe('CreateIntegrationEntriesUseCase', () => {
  const mockIntegration: Integration = {
    id: 'integration-id-1',
    type: 'shopify',
    params: {
      store_url: 'test-store.myshopify.com',
      api_version: '2023-04',
    },
    companyId: 'company-id',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockExistingIntegration: Integration = {
    id: 'existing-integration-id',
    type: 'woocommerce',
    params: {
      site_url: 'test-site.com',
    },
    companyId: 'company-id',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const defaultCommand: CreateIntegrationEntriesCommand = {
    companyId: 'company-id',
    entries: [
      {
        token: 'encrypted-token-1',
        type: 'shopify',
        params: {
          store_url: 'test-store.myshopify.com',
          api_version: '2023-04',
        },
      },
    ],
  };

  beforeEach(() => {
    jest.restoreAllMocks();
    jest.resetAllMocks();
    // Reset to default successful behavior
    (Registry.IntegrationService.retrieveIntegrationsForCompany as jest.Mock).mockReset();
    (Registry.IntegrationService.createIntegrations as jest.Mock).mockReset();
  });

  describe('apply', () => {
    it('should create integration entries successfully', async () => {
      (Registry.IntegrationService.retrieveIntegrationsForCompany as jest.Mock).mockResolvedValue([]);
      (Registry.IntegrationService.createIntegrations as jest.Mock).mockResolvedValue([mockIntegration]);

      const result = await CreateIntegrationEntriesUseCase.apply(defaultCommand);

      expect(Registry.IntegrationService.retrieveIntegrationsForCompany).toHaveBeenCalledWith('company-id');
      expect(Registry.IntegrationService.createIntegrations).toHaveBeenCalledWith([
        {
          token: 'encrypted-token-1',
          type: 'shopify',
          params: {
            store_url: 'test-store.myshopify.com',
            api_version: '2023-04',
          },
          companyId: 'company-id',
        },
      ]);
      expect(result).toEqual([mockIntegration]);
    });

    it('should create multiple integration entries successfully', async () => {
      const multipleEntriesCommand: CreateIntegrationEntriesCommand = {
        companyId: 'company-id',
        entries: [
          {
            token: 'encrypted-token-1',
            type: 'shopify',
            params: {
              store_url: 'test-store.myshopify.com',
              api_version: '2023-04',
            },
          },
          {
            token: 'encrypted-token-2',
            type: 'woocommerce',
            params: {
              site_url: 'test-site.com',
            },
          },
        ],
      };

      const mockMultipleIntegrations: Integration[] = [
        mockIntegration,
        {
          id: 'integration-id-2',
          type: 'woocommerce',
          params: {
            site_url: 'test-site.com',
          },
          companyId: 'company-id',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      (Registry.IntegrationService.retrieveIntegrationsForCompany as jest.Mock).mockResolvedValue([]);
      (Registry.IntegrationService.createIntegrations as jest.Mock).mockResolvedValue(mockMultipleIntegrations);

      const result = await CreateIntegrationEntriesUseCase.apply(multipleEntriesCommand);

      expect(Registry.IntegrationService.createIntegrations).toHaveBeenCalledWith([
        {
          token: 'encrypted-token-1',
          type: 'shopify',
          params: {
            store_url: 'test-store.myshopify.com',
            api_version: '2023-04',
          },
          companyId: 'company-id',
        },
        {
          token: 'encrypted-token-2',
          type: 'woocommerce',
          params: {
            site_url: 'test-site.com',
          },
          companyId: 'company-id',
        },
      ]);
      expect(result).toEqual(mockMultipleIntegrations);
    });

    it('should create integration entries without params successfully', async () => {
      const commandWithoutParams: CreateIntegrationEntriesCommand = {
        companyId: 'company-id',
        entries: [
          {
            token: 'encrypted-token-1',
            type: 'simple-integration',
          },
        ],
      };

      const mockIntegrationWithoutParams: Integration = {
        id: 'integration-id-1',
        type: 'simple-integration',
        companyId: 'company-id',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (Registry.IntegrationService.retrieveIntegrationsForCompany as jest.Mock).mockResolvedValue([]);
      (Registry.IntegrationService.createIntegrations as jest.Mock).mockResolvedValue([mockIntegrationWithoutParams]);

      const result = await CreateIntegrationEntriesUseCase.apply(commandWithoutParams);

      expect(Registry.IntegrationService.createIntegrations).toHaveBeenCalledWith([
        {
          token: 'encrypted-token-1',
          type: 'simple-integration',
          companyId: 'company-id',
        },
      ]);
      expect(result).toEqual([mockIntegrationWithoutParams]);
    });

    it('should throw error when integration type already exists for company', async () => {
      (Registry.IntegrationService.retrieveIntegrationsForCompany as jest.Mock).mockResolvedValue([mockExistingIntegration]);

      const conflictCommand: CreateIntegrationEntriesCommand = {
        companyId: 'company-id',
        entries: [
          {
            token: 'encrypted-token-1',
            type: 'woocommerce', // Same type as existing integration
            params: {
              site_url: 'another-site.com',
            },
          },
        ],
      };

      await expect(
        CreateIntegrationEntriesUseCase.apply(conflictCommand),
      ).rejects.toThrow('Integration entries already exist for the company');

      expect(Registry.IntegrationService.retrieveIntegrationsForCompany).toHaveBeenCalledWith('company-id');
      expect(Registry.IntegrationService.createIntegrations).not.toHaveBeenCalled();
    });

    it('should throw error when one of multiple integration types already exists', async () => {
      (Registry.IntegrationService.retrieveIntegrationsForCompany as jest.Mock).mockResolvedValue([
        mockExistingIntegration, // woocommerce type exists
      ]);

      const conflictCommand: CreateIntegrationEntriesCommand = {
        companyId: 'company-id',
        entries: [
          {
            token: 'encrypted-token-1',
            type: 'shopify', // New type
            params: {
              store_url: 'test-store.myshopify.com',
            },
          },
          {
            token: 'encrypted-token-2',
            type: 'woocommerce', // Existing type - should cause conflict
            params: {
              site_url: 'another-site.com',
            },
          },
        ],
      };

      await expect(
        CreateIntegrationEntriesUseCase.apply(conflictCommand),
      ).rejects.toThrow('Integration entries already exist for the company');

      expect(Registry.IntegrationService.retrieveIntegrationsForCompany).toHaveBeenCalledWith('company-id');
      expect(Registry.IntegrationService.createIntegrations).not.toHaveBeenCalled();
    });

    it('should allow creating integration of different type when other types exist', async () => {
      (Registry.IntegrationService.retrieveIntegrationsForCompany as jest.Mock).mockResolvedValue([
        mockExistingIntegration, // woocommerce type exists
      ]);

      const nonConflictCommand: CreateIntegrationEntriesCommand = {
        companyId: 'company-id',
        entries: [
          {
            token: 'encrypted-token-1',
            type: 'shopify', // Different type from existing
            params: {
              store_url: 'test-store.myshopify.com',
            },
          },
        ],
      };

      (Registry.IntegrationService.createIntegrations as jest.Mock).mockResolvedValue([mockIntegration]);

      const result = await CreateIntegrationEntriesUseCase.apply(nonConflictCommand);

      expect(Registry.IntegrationService.retrieveIntegrationsForCompany).toHaveBeenCalledWith('company-id');
      expect(Registry.IntegrationService.createIntegrations).toHaveBeenCalledWith([
        {
          token: 'encrypted-token-1',
          type: 'shopify',
          params: {
            store_url: 'test-store.myshopify.com',
          },
          companyId: 'company-id',
        },
      ]);
      expect(result).toEqual([mockIntegration]);
    });

    it('should handle integration service errors', async () => {
      const serviceError = new Error('Database connection failed');
      (Registry.IntegrationService.retrieveIntegrationsForCompany as jest.Mock).mockRejectedValue(serviceError);

      await expect(
        CreateIntegrationEntriesUseCase.apply(defaultCommand),
      ).rejects.toThrow('Database connection failed');

      expect(Registry.IntegrationService.retrieveIntegrationsForCompany).toHaveBeenCalledWith('company-id');
      expect(Registry.IntegrationService.createIntegrations).not.toHaveBeenCalled();
    });

    it('should handle integration creation service errors', async () => {
      const creationError = new Error('Failed to create integration');
      (Registry.IntegrationService.retrieveIntegrationsForCompany as jest.Mock).mockResolvedValue([]);
      (Registry.IntegrationService.createIntegrations as jest.Mock).mockRejectedValue(creationError);

      await expect(
        CreateIntegrationEntriesUseCase.apply(defaultCommand),
      ).rejects.toThrow('Failed to create integration');

      expect(Registry.IntegrationService.retrieveIntegrationsForCompany).toHaveBeenCalledWith('company-id');
      expect(Registry.IntegrationService.createIntegrations).toHaveBeenCalledWith([
        {
          token: 'encrypted-token-1',
          type: 'shopify',
          params: {
            store_url: 'test-store.myshopify.com',
            api_version: '2023-04',
          },
          companyId: 'company-id',
        },
      ]);
    });
  });
});
