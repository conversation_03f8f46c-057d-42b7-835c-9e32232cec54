import { ApplicationError } from '#application/Common.Type';
import IntegrationErrorCodes from '#application/integration/Integration.ErrorCodes';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import Logger from '#composition/Logger';

export interface DeleteIntegrationEntryCommand {
  integrationId: string;
  companyId: string;
}

const logger = Logger.getLogger().child({ module: 'DeleteIntegrationEntry.UseCase' });

async function apply(command: DeleteIntegrationEntryCommand): Promise<void> {
  const { integrationId } = command;

  const existingIntegration = await Registry.IntegrationService.retrieveIntegrationById(integrationId);

  if (!existingIntegration) {
    logger.error('Integration entry not found', { integrationId });

    throw new ApplicationError(
      'Integration entry not found',
      Cause.NOT_FOUND,
      IntegrationErrorCodes.INTEGRATION_NOT_FOUND,
    );
  }

  if (existingIntegration.companyId !== command.companyId) {
    logger.error('Integration entry does not belong to the company', { integrationId, companyId: command.companyId });

    throw new ApplicationError(
      'Integration entry does not belong to the company',
      Cause.FORBIDDEN,
      IntegrationErrorCodes.INTEGRATION_FORBIDDEN,
    );
  }

  await Registry.IntegrationService.removeIntegration(integrationId);

  logger.info('Integration entry deleted', { integrationId });
}

const DeleteIntegrationEntryUseCase = {
  apply,
};

export default DeleteIntegrationEntryUseCase;
