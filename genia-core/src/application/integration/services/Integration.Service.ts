export interface CreateIntegrationParams {
  token: string;
  type: string;
  params?: Record<string, string>;
  companyId: string;
}

export interface Integration extends Omit<CreateIntegrationParams, 'token'> {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IntegrationService {
  createIntegrations(integrations: CreateIntegrationParams[]): Promise<Integration[]>;
  removeIntegration(id: string): Promise<void>;
  retrieveIntegrationById(id: string): Promise<Integration | null>;
  retrieveIntegrationsForCompany(companyId: string): Promise<Integration[]>;
}
