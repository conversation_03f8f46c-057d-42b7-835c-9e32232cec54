import ClientEntity from '#domain/aggregates/client/Client.Entity';

interface ClientRepository {
  saveMany(clients: ClientEntity[]): Promise<ClientEntity[]>;
  save(client: ClientEntity): Promise<ClientEntity>;
  findOneById(id: string): Promise<ClientEntity | undefined>;
  findManyByIds(ids: string[]): Promise<ClientEntity[]>;
  findManyByClientCompanyIds(companyIds: string[]): Promise<ClientEntity[]>;
}

export default ClientRepository;
