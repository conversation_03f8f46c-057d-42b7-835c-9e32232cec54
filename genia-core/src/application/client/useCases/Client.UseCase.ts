import { difference } from 'lodash';

import CreateClientEntriesCommand from '#application/client/commands/CreateClientEntries.Command';
import UpdateClientEntryCommand from '#application/client/commands/UpdateClientEntry.Command';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import ClientEntity from '#domain/aggregates/client/Client.Entity';
import ClientOperator from '#domain/aggregates/client/Client.Operator';

async function createClientEntries(createClientEntriesCommand: CreateClientEntriesCommand): Promise<ClientEntity[]> {
  const { entries, companyId } = createClientEntriesCommand;
  const setClientCompanyIds = new Set<string>();
  const setStoreDiscountIds = new Set<string>();

  entries.forEach((entry) => {
    const { clientCompanyId, storeDiscounts } = entry;

    if (clientCompanyId) setClientCompanyIds.add(clientCompanyId);

    storeDiscounts?.forEach((storeDiscountId) => setStoreDiscountIds.add(storeDiscountId));
  });

  const storeDiscountIds = Array.from(setStoreDiscountIds);
  const clientCompanyIds = Array.from(setClientCompanyIds);

  if (storeDiscountIds.length) {
    const foundStoreDiscounts = await Registry.StoreDiscountRepository.findManyByIds(storeDiscountIds);

    if (foundStoreDiscounts.length !== storeDiscountIds.length) {
      const foundIds = foundStoreDiscounts.map(({ id }) => id);
      const notFoundIds = difference(storeDiscountIds, foundIds);
      throw new Error(`Some storeDiscounts were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
    }
  }

  if (clientCompanyIds.length) {
    const foundClientCompanies = await Registry.CompanyRepository.findManyByIds(clientCompanyIds);

    if (foundClientCompanies.length !== clientCompanyIds.length) {
      const foundIds = foundClientCompanies.map(({ id }) => id);
      const notFoundIds = difference(clientCompanyIds, foundIds);
      throw new Error(`Some company were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
    }
  }

  const clients = await Promise.all(entries.map(async (entry) => {
    const id = await Registry.IdentificationService.generateId();

    return ClientOperator.build({ ...entry, id, companyId });
  }));

  return Registry.ClientRepository.saveMany(clients);
}

async function updateClientEntry(updateClientEntryCommand: UpdateClientEntryCommand): Promise<ClientEntity> {
  const { id, companyId, storeDiscounts } = updateClientEntryCommand;
  const client = await Registry.ClientRepository.findOneById(id);

  if (!client) throw new Error('Client not found', { cause: Cause.BAD_REQUEST });

  if (client.companyId !== companyId) throw new Error('Forbidden', { cause: Cause.FORBIDDEN });

  if (storeDiscounts && storeDiscounts.length) {
    const foundStoreDiscounts = await Registry.StoreDiscountRepository.findManyByIds(storeDiscounts);

    if (foundStoreDiscounts.length !== storeDiscounts.length) {
      const foundStoreDiscountIds = foundStoreDiscounts.map(({ id: storeDiscountId }) => storeDiscountId);
      const notFoundIds = difference(storeDiscounts, foundStoreDiscountIds);
      throw new Error(`Some storeDiscounts were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
    }
  }

  const updated = ClientOperator.update(client, updateClientEntryCommand);

  return Registry.ClientRepository.save(updated);
}

export default {
  createClientEntries,
  updateClientEntry,
};
