import { CatalogType, InventoryRelation } from '#domain/aggregates/catalog/Catalog.Entity';
import { AttributeType } from '#domain/common/Common.Type';

export default interface UpdateCatalogEntryCommand {
  id: string;
  readId?: string;
  name?: string;
  description?: string;
  price?: number;
  requiresStock?: boolean;
  attributes?: AttributeType[];
  type?: CatalogType;
  discountIds?: string[];
  taxIds?: string[];
  inventoryRelations?: InventoryRelation[];
  companyId: string;
  disabledAt?: Date;
}
