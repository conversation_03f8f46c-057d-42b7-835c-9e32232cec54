import { difference } from 'lodash';

import CreateCatalogEntriesCommand from '#application/catalog/commands/CreateCatalogEntries.Command';
import { FindCatalogEntriesForClientCommand } from '#application/catalog/commands/FindCatalogEntiresForClient.Command';
import { FindCatalogEntriesForProviderCommand } from '#application/catalog/commands/FindCatalogEntriesForProvider.Command';
import UpdateCatalogEntryCommand from '#application/catalog/commands/UpdateCatalogEntry.Command';
import { PaginatedQueryWithSearchAndFilters } from '#application/Common.Type';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import CatalogObject from '#domain/aggregates/catalog/Catalog.Entity';
import CatalogOperator from '#domain/aggregates/catalog/Catalog.Operator';
import CatalogDiscountEntity from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';
import CatalogMediaEntity from '#domain/aggregates/catalogMedia/CatalogMedia.Entity';
import ClientEntity from '#domain/aggregates/client/Client.Entity';
import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import { MediaType } from '#domain/common/aggregates/media/MediaEntity';
import { PaginatedResult } from '#domain/common/Common.Type';
import CatalogDomainService, { CatalogWithDiscount, CatalogWithQuantity } from '#domain/domainServices/Catalog.DomainService';

interface findCatalogForCompaniesAndClientParams {
  companies: string[];
  clients: ClientEntity[];
  query: PaginatedQueryWithSearchAndFilters;
}

interface PublicCatalog extends CatalogWithDiscount {
  media: {
    id: string;
    url: string;
    type: MediaType;
  }[];
}

interface PublicCatalogWithProvider extends PublicCatalog {
  provider: {
    id: string;
    name: string;
    companyId: string;
  };
}

async function getCatalogConditionsForCompaniesAndClients(params: findCatalogForCompaniesAndClientParams): Promise<{
  paginatedCatalog: PaginatedResult<CatalogObject>,
  catalogDiscounts: CatalogDiscountEntity[],
  storeDiscounts: StoreDiscountEntity[]
}> {
  const { query, companies, clients } = params;

  const catalogForCompanies = await Registry.CatalogRepository.findPaginated({
    ...query, filters: { companyId: companies },
  });

  const clientIds = clients.map(({ id }) => id);
  const catalogIds = catalogForCompanies.data.map(({ id }) => id);

  const catalogDiscounts = await Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds(
    companies,
    catalogIds,
    clientIds,
  );

  const globalCatalogDiscounts = await Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds(
    companies,
    catalogIds,
    null,
  );

  const storeDiscounts = await Registry.StoreDiscountRepository.findManyForCompaniesByClientIds(companies, clientIds);

  const globalStoreDiscounts = await Registry.StoreDiscountRepository.findManyForCompaniesByClientIds(companies, null);

  return {
    paginatedCatalog: catalogForCompanies,
    catalogDiscounts: [...catalogDiscounts, ...globalCatalogDiscounts],
    storeDiscounts: [...storeDiscounts, ...globalStoreDiscounts],
  };
}

async function addMediaToCatalog(catalog: CatalogWithDiscount[]): Promise<PublicCatalog[]> {
  const allMediaIds = catalog.flatMap(({ mediaIds }) => mediaIds);

  const media = await Registry.CatalogMediaRepository.findManyByIds(allMediaIds);
  const mediaMap = media.reduce<{ [key: string]: CatalogMediaEntity }>(
    (map, mediaItem) => {
      const newMap = map;
      newMap[mediaItem.id] = mediaItem;
      return newMap;
    },
    {},
  );

  return catalog.map((catalogItem) => {
    const mediaItems = catalogItem.mediaIds.map((mediaId) => {
      const mediaItem = mediaMap[mediaId];
      return {
        id: mediaItem.id,
        url: mediaItem.url,
        type: mediaItem.mediaType,
      };
    });

    return {
      ...catalogItem,
      media: mediaItems,
    };
  });
}

function validateProvidedAndFound(providedIds: string[], foundIds: string[], entityName: string): undefined {
  if (providedIds.length !== foundIds.length) {
    const setProvidedIds = new Set(providedIds);
    const notFoundIds = difference(Array.from(setProvidedIds), foundIds);
    throw new Error(`Some ${entityName} were not found: ${notFoundIds.join(',')}`, { cause: 'BAD_REQUEST' });
  }
}

function mapByCompany<T extends { companyId: string }>(items: T[]): { [key: string]: T[] } {
  return items.reduce<{ [key: string]: T[] }>(
    (map, item) => {
      const newMap = map;
      if (!map[item.companyId]) newMap[item.companyId] = [];
      map[item.companyId].push(item);
      return newMap;
    },
    {},
  );
}

async function createCatalogEntries(createCatalogEntriesCommand: CreateCatalogEntriesCommand): Promise<CatalogObject[]> {
  const setInventoryIds = new Set<string>();
  const setTaxIds = new Set<string>();
  const setDiscountIds = new Set<string>();
  const setReadIds = new Set<string>();

  createCatalogEntriesCommand.entries.forEach(({
    discountIds, taxIds, inventoryRelations, readId,
  }) => {
    inventoryRelations?.forEach(({ inventoryId }) => setInventoryIds.add(inventoryId));
    discountIds?.forEach((discountId) => setDiscountIds.add(discountId));
    taxIds?.forEach((taxId) => setTaxIds.add(taxId));
    if (readId) setReadIds.add(readId);
  });

  const providedInventoryIds = Array.from(setInventoryIds);
  const providedTaxIds = Array.from(setTaxIds);
  const providedDiscountIds = Array.from(setDiscountIds);

  if (providedInventoryIds.length) {
    const foundInventory = await Registry.InventoryRepository.findManyByIds(providedInventoryIds);
    const foundIds = foundInventory.map(({ id }) => id);

    validateProvidedAndFound(providedInventoryIds, foundIds, 'inventory');
  }

  if (providedTaxIds.length) {
    const foundTaxes = await Registry.TaxRepository.findManyByIds(providedTaxIds);
    const foundIds = foundTaxes.map(({ id }) => id);

    validateProvidedAndFound(providedTaxIds, foundIds, 'tax');
  }

  if (providedDiscountIds.length) {
    const foundDiscounts = await Registry.CatalogDiscountRepository.findManyByIds(providedDiscountIds);
    const foundIds = foundDiscounts.map(({ id }) => id);

    validateProvidedAndFound(providedDiscountIds, foundIds, 'discount');
  }

  return Registry.TransactionService.transactional(async () => {
    const { companyId, entries } = createCatalogEntriesCommand;

    const catalogEntries = await Promise.all(entries.map(async (entry) => {
      let { readId } = entry;
      const id = await Registry.IdentificationService.generateId();

      if (!readId) {
        readId = await Registry.IdentificationService.generateObjectSequenceForCompany(companyId, 'catalog');
      }

      return CatalogOperator.build({
        ...entry, id, readId, companyId, createdAt: new Date(), updatedAt: new Date(),
      });
    }));

    return Registry.CatalogRepository.save(catalogEntries);
  });
}

async function updateCatalogEntry(updateCatalogEntryCommand: UpdateCatalogEntryCommand): Promise<CatalogObject> {
  const catalog = await Registry.CatalogRepository.findOneById(updateCatalogEntryCommand.id);

  if (!catalog) throw new Error('Catalog not found', { cause: Cause.NOT_FOUND });

  if (catalog.companyId !== updateCatalogEntryCommand.companyId) throw new Error('Forbidden', { cause: Cause.FORBIDDEN });

  let { readId: newReadId } = updateCatalogEntryCommand;

  const {
    inventoryRelations, taxIds, discountIds, ...catalogUpdates
  } = updateCatalogEntryCommand;

  if (inventoryRelations && inventoryRelations.length) {
    const inventoryIds = inventoryRelations.map(({ inventoryId }) => inventoryId);
    const foundInventory = await Registry.InventoryRepository.findManyByIds(inventoryIds);
    const foundIds = foundInventory.map(({ id }) => id);
    validateProvidedAndFound(inventoryIds, foundIds, 'inventory');
  }

  if (taxIds && taxIds.length) {
    const foundTaxes = await Registry.TaxRepository.findManyByIds(taxIds);
    const foundIds = foundTaxes.map(({ id }) => id);
    validateProvidedAndFound(taxIds, foundIds, 'tax');
  }

  if (discountIds && discountIds.length) {
    const foundDiscounts = await Registry.CatalogDiscountRepository.findManyByIds(discountIds);
    const foundIds = foundDiscounts.map(({ id }) => id);
    validateProvidedAndFound(discountIds, foundIds, 'discount');
  }

  return Registry.TransactionService.transactional(async () => {
    if (!newReadId) {
      newReadId = await Registry.IdentificationService.generateObjectSequenceForCompany(updateCatalogEntryCommand.companyId, 'catalog');
    }

    const updatedCatalog = CatalogOperator.update(catalog, {
      ...catalogUpdates, discountIds, taxIds, inventoryRelations, readId: newReadId,
    });

    const [dbUpdatedCatalog] = await Registry.CatalogRepository.save([updatedCatalog]);

    return dbUpdatedCatalog;
  });
}

async function deleteCatalogEntry(id: string, companyId: string): Promise<undefined> {
  const found = await Registry.CatalogRepository.findOneById(id);

  if (!found) throw new Error('Catalog not found', { cause: Cause.NOT_FOUND });

  if (found.companyId !== companyId) throw new Error('Catalog does not belong to the provided company', { cause: Cause.FORBIDDEN });

  return Registry.CatalogRepository.deleteById(id);
}

async function findCatalogForClient(findCatalogEntriesCommand: FindCatalogEntriesForClientCommand): Promise<PaginatedResult<PublicCatalog>> {
  const { query, companyId, clientId } = findCatalogEntriesCommand;

  const client = await Registry.ClientRepository.findOneById(clientId);

  if (!client) throw new Error('Client not found', { cause: Cause.NOT_FOUND });

  if (client.companyId !== companyId) throw new Error('Client does not belong to the provided company', { cause: Cause.FORBIDDEN });

  const { paginatedCatalog, catalogDiscounts, storeDiscounts } = await getCatalogConditionsForCompaniesAndClients({
    companies: [companyId], clients: [client], query,
  });

  const catalogWithQuantity: CatalogWithQuantity[] = paginatedCatalog.data.map((catalogItem) => ({
    ...catalogItem,
    quantity: 1,
  }));

  const ProcessedCatalog = await addMediaToCatalog(CatalogDomainService.calculateDiscountsForCatalogByClient({
    catalog: catalogWithQuantity,
    catalogDiscounts,
    storeDiscounts,
    client,
  }));

  return {
    data: ProcessedCatalog,
    count: paginatedCatalog.count,
  };
}

async function findProvidersCatalog(findCatalogEntriesCommand: FindCatalogEntriesForProviderCommand): Promise<PaginatedResult<PublicCatalog>> {
  const { query, companyId, query: { filters: { providerId, ...cleanedFilters } = { providerId: [] } } } = findCatalogEntriesCommand;

  const clients = await Registry.ClientRepository.findManyByClientCompanyIds([companyId]);

  if (!clients.length) throw new Error('Company is not client of any other company', { cause: Cause.NOT_FOUND });

  const providers = (await Registry.ProviderRepository.findProvidersForCompany(companyId)).filter(({ id }) => !providerId.length || providerId.includes(id));

  if (!providers.length) throw new Error('Client has not providers', { cause: Cause.NOT_FOUND });

  const providersWithCompany = providers.filter(({ providerCompanyId }) => providerCompanyId);
  const providersWithCompanyIds = providersWithCompany.map(({ providerCompanyId }) => providerCompanyId) as string[];

  const { paginatedCatalog, catalogDiscounts, storeDiscounts } = await getCatalogConditionsForCompaniesAndClients({
    companies: providersWithCompanyIds, clients, query: { ...query, filters: { ...cleanedFilters } },
  });

  const catalogWithMedia = await addMediaToCatalog(paginatedCatalog.data as CatalogWithDiscount[]);

  const catalogByCompany = mapByCompany(catalogWithMedia);
  const clientByCompany = mapByCompany(clients);
  const catalogDiscountsByCompany = mapByCompany(catalogDiscounts);
  const storeDiscountsByCompany = mapByCompany(storeDiscounts);

  const ProcessedCatalog: PublicCatalogWithProvider[] = providersWithCompany.flatMap((provider) => {
    const providerCompanyId = provider.providerCompanyId as string;

    const catalogForProvider = catalogByCompany[providerCompanyId] || [];
    const clientsForProvider = clientByCompany[providerCompanyId] || [];
    const catalogDiscountsForProvider = catalogDiscountsByCompany[providerCompanyId] || [];
    const storeDiscountsForProvider = storeDiscountsByCompany[providerCompanyId] || [];

    const catalogWithQuantity: CatalogWithQuantity[] = catalogForProvider.map((catalogItem) => ({
      ...catalogItem,
      quantity: 1,
    }));

    const catalogWithDiscount = CatalogDomainService.calculateDiscountsForCatalogByClient({
      catalog: catalogWithQuantity,
      catalogDiscounts: catalogDiscountsForProvider,
      storeDiscounts: storeDiscountsForProvider,
      client: clientsForProvider[0],
    });

    return catalogWithDiscount.map((catalogItem) => ({
      ...catalogItem,
      provider: {
        id: provider.id,
        name: provider.name,
        companyId: providerCompanyId,
      },
    }) as PublicCatalogWithProvider);
  });

  return {
    data: ProcessedCatalog,
    count: paginatedCatalog.count,
  };
}

export default {
  createCatalogEntries,
  updateCatalogEntry,
  deleteCatalogEntry,
  findCatalogForClient,
  findProvidersCatalog,
};
