import { FindCatalogEntriesForClientCommand } from '#application/catalog/commands/FindCatalogEntiresForClient.Command';
import { FindCatalogEntriesForProviderCommand } from '#application/catalog/commands/FindCatalogEntriesForProvider.Command';
import UpdateCatalogEntryCommand from '#application/catalog/commands/UpdateCatalogEntry.Command';
import CatalogUseCase from '#application/catalog/useCases/Catalog.UseCase';
import Registry from '#composition/ImplementationRegistry';
import CatalogEntity, { CatalogType } from '#domain/aggregates/catalog/Catalog.Entity';
import CatalogOperator from '#domain/aggregates/catalog/Catalog.Operator';
import CatalogMediaEntity from '#domain/aggregates/catalogMedia/CatalogMedia.Entity';
import ClientEntity from '#domain/aggregates/client/Client.Entity';
import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import { MediaType } from '#domain/common/aggregates/media/MediaEntity';
import CatalogDomainService from '#domain/domainServices/Catalog.DomainService';

jest.unmock('lodash');

jest.unmock('./Catalog.UseCase');

describe('CatalogUseCase', () => {
  const mockDate = new Date('2023-01-01');

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('createCatalogEntries', () => {
    const mockEntries = [
      {
        name: 'aceite',
        description: 'aceite motos 4T mobil',
        price: 100,
        requiresStock: true,
        attributes: [],
        type: CatalogType.PRODUCT,
        discountIds: ['disc123', 'disc124'],
        taxIds: ['tax123', 'tax124'],
        inventoryRelations: [
          {
            inventoryId: 'inv123',
            quantity: 10,
          },
          {
            inventoryId: 'inv124',
            quantity: 10,
          },
        ],
      },
    ];

    const mockCommand = {
      companyId: 'comp123',
      entries: mockEntries,
    };

    const mockFoundInventory = [{ inventoryId: 'inv123' }, { inventoryId: 'inv124' }];
    const mockFoundTaxes = [{ inventoryId: 'tax123' }, { inventoryId: 'tax124' }];
    const mockFoundDiscounts = [{ inventoryId: 'disc123' }, { inventoryId: 'disc124' }];

    it('should create catalog entries successfully', async () => {
      const mockCatalogEntries = [
        {
          id: 'cat123',
          name: 'product',
          readId: 'product_123',
          description: 'product description',
          price: 100,
          requiresStock: true,
          attributes: [],
          type: CatalogType.PRODUCT,
          discountIds: ['disc123'],
          taxIds: ['tax123'],
          inventoryRelations: [
            {
              id: 'inv123',
              quantity: 10,
            },
          ],
          companyId: 'comp123',
        },
      ];

      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundInventory);
      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundTaxes);
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundDiscounts);
      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValueOnce('cat123');
      (Registry.IdentificationService.generateObjectSequenceForCompany as jest.Mock).mockResolvedValueOnce('product_123');
      (CatalogOperator.build as jest.Mock).mockReturnValueOnce(mockCatalogEntries[0]);
      (Registry.CatalogRepository.save as jest.Mock).mockResolvedValueOnce(mockCatalogEntries);

      const result = await CatalogUseCase.createCatalogEntries(mockCommand);

      expect(result).toEqual(mockCatalogEntries);
      expect(Registry.InventoryRepository.findManyByIds).toHaveBeenCalledWith(['inv123', 'inv124']);
      expect(Registry.TaxRepository.findManyByIds).toHaveBeenCalledWith(['tax123', 'tax124']);
      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith(['disc123', 'disc124']);
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).toHaveBeenCalledWith('comp123', 'catalog');
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();
      expect(CatalogOperator.build).toHaveBeenCalledWith({
        ...mockEntries[0],
        id: 'cat123',
        readId: 'product_123',
        companyId: 'comp123',
        createdAt: mockDate,
        updatedAt: mockDate,
      });
      expect(Registry.CatalogRepository.save).toHaveBeenCalledWith(mockCatalogEntries);
      expect(Registry.CatalogRepository.getIdsFromReadIds).not.toHaveBeenCalled();
    });

    it('should throw an error if some catalog are not found', async () => {
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([mockFoundInventory[1]]);

      await expect(CatalogUseCase.createCatalogEntries(mockCommand)).rejects.toThrow('Some inventory were not found: inv123');
    });

    it('should throw an error if some taxes are not found', async () => {
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundInventory);
      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([mockFoundTaxes[1]]);

      await expect(CatalogUseCase.createCatalogEntries(mockCommand)).rejects.toThrow('Some tax were not found: tax123');
    });

    it('should throw an error if some discounts are not found', async () => {
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundInventory);
      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundTaxes);
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([mockFoundDiscounts[1]]);

      await expect(CatalogUseCase.createCatalogEntries(mockCommand)).rejects.toThrow('Some discount were not found: disc123');
    });

    it('should create catalog entry with provided readId', async () => {
      const entriesWithReadId = [
        {
          ...mockEntries[0],
          readId: 'CUSTOM_READ_ID',
        },
      ];

      const mockCatalogEntries = [
        {
          id: 'cat123',
          name: 'product',
          readId: 'CUSTOM_READ_ID',
          description: 'product description',
          price: 100,
          requiresStock: true,
          attributes: [],
          type: CatalogType.PRODUCT,
          discountIds: ['disc123'],
          taxIds: ['tax123'],
          inventoryRelations: [
            {
              id: 'inv123',
              quantity: 10,
            },
          ],
          companyId: 'comp123',
        },
      ];

      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundInventory);
      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundTaxes);
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundDiscounts);
      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValueOnce('cat123');
      (CatalogOperator.build as jest.Mock).mockReturnValueOnce(mockCatalogEntries[0]);
      (Registry.CatalogRepository.save as jest.Mock).mockResolvedValueOnce(mockCatalogEntries);

      const result = await CatalogUseCase.createCatalogEntries({
        companyId: mockCommand.companyId,
        entries: entriesWithReadId,
      });

      expect(result).toEqual(mockCatalogEntries);
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).not.toHaveBeenCalled();
      expect(CatalogOperator.build).toHaveBeenCalledWith({
        ...entriesWithReadId[0],
        id: 'cat123',
        readId: 'CUSTOM_READ_ID',
        companyId: 'comp123',
        createdAt: mockDate,
        updatedAt: mockDate,
      });
    });
  });

  describe('updateCatalogEntry', () => {
    const mockCommand: UpdateCatalogEntryCommand = {
      id: 'cat123',
      companyId: 'comp123',
      name: 'aceite',
      description: 'aceite motos 4T mobil',
      price: 100,
      requiresStock: true,
      attributes: [],
      type: CatalogType.PRODUCT,
      discountIds: ['disc123', 'disc124'],
      taxIds: ['tax123', 'tax124'],
      inventoryRelations: [
        {
          inventoryId: 'inv123',
          quantity: 10,
        },
        {
          inventoryId: 'inv124',
          quantity: 10,
        },
      ],
    };

    const mockCatalog: CatalogEntity = {
      id: 'cat123',
      name: 'aceite',
      readId: 'CAT_123',
      description: 'aceite motos 4T mobil',
      price: 100,
      requiresStock: true,
      attributes: [],
      type: CatalogType.PRODUCT,
      discountIds: ['disc123'],
      taxIds: ['tax123'],
      inventoryRelations: [
        {
          inventoryId: 'inv123',
          quantity: 10,
        },
      ],
      companyId: 'comp123',
      disabledAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      mediaIds: [],
    };

    const mockFoundInventory = [{ inventoryId: 'inv123' }, { inventoryId: 'inv124' }];
    const mockFoundTaxes = [{ inventoryId: 'tax123' }, { inventoryId: 'tax124' }];
    const mockFoundDiscounts = [{ inventoryId: 'disc123' }, { inventoryId: 'disc124' }];

    it('should update catalog entry successfully when no readId is provided', async () => {
      const updatedCatalog: CatalogEntity = {
        ...mockCatalog,
        ...mockCommand,
        readId: 'PREFIX-1',
      };

      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockCatalog);
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundInventory);
      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundTaxes);
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundDiscounts);
      (CatalogOperator.build as jest.Mock).mockReturnValueOnce(updatedCatalog);
      (CatalogOperator.update as jest.Mock).mockReturnValueOnce(updatedCatalog);
      (Registry.CatalogRepository.save as jest.Mock).mockResolvedValueOnce([updatedCatalog]);

      const result = await CatalogUseCase.updateCatalogEntry(mockCommand);

      expect(result).toEqual(updatedCatalog);
      expect(Registry.CatalogRepository.findOneById).toHaveBeenCalledWith('cat123');
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).toHaveBeenCalledWith('comp123', 'catalog');
      expect(Registry.InventoryRepository.findManyByIds).toHaveBeenCalledWith(['inv123', 'inv124']);
      expect(Registry.TaxRepository.findManyByIds).toHaveBeenCalledWith(['tax123', 'tax124']);
      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith(['disc123', 'disc124']);
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();
      expect(Registry.CatalogRepository.save).toHaveBeenCalledWith([updatedCatalog]);
    });

    it('should update catalog entry successfully when readId is provided', async () => {
      const updatedCatalog: CatalogEntity = {
        ...mockCatalog,
        ...mockCommand,
      };

      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockCatalog);
      (CatalogOperator.build as jest.Mock).mockReturnValueOnce(updatedCatalog);
      (CatalogOperator.update as jest.Mock).mockReturnValueOnce(updatedCatalog);
      (Registry.CatalogRepository.save as jest.Mock).mockResolvedValueOnce([updatedCatalog]);

      const result = await CatalogUseCase.updateCatalogEntry({ id: mockCommand.id, companyId: mockCommand.companyId, readId: 'read_id' });

      expect(result).toEqual(updatedCatalog);
      expect(Registry.CatalogRepository.findOneById).toHaveBeenCalledWith('cat123');
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).not.toHaveBeenCalled();
      expect(Registry.InventoryRepository.findManyByIds).not.toHaveBeenCalled();
      expect(Registry.TaxRepository.findManyByIds).not.toHaveBeenCalled();
      expect(Registry.CatalogDiscountRepository.findManyByIds).not.toHaveBeenCalled();
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();
      expect(Registry.CatalogRepository.save).toHaveBeenCalledWith([updatedCatalog]);
    });

    it('should throw an error if catalog is not found', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce(undefined);

      await expect(CatalogUseCase.updateCatalogEntry(mockCommand)).rejects.toThrow('Catalog not found');
    });

    it('should throw an error if catalog is not from the provided company', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce({ companyId: 'comp_wrong' });

      await expect(CatalogUseCase.updateCatalogEntry(mockCommand)).rejects.toThrow('Forbidden');
    });

    it('should throw an error if some inventory are not found', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockCatalog);
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([mockFoundInventory[1]]);

      await expect(CatalogUseCase.updateCatalogEntry(mockCommand)).rejects.toThrow('Some inventory were not found: inv123');
    });

    it('should throw an error if some taxes are not found', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockCatalog);
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundInventory);
      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([mockFoundTaxes[1]]);

      await expect(CatalogUseCase.updateCatalogEntry(mockCommand)).rejects.toThrow('Some tax were not found: tax123');
    });

    it('should throw an error if some discounts are not found', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockCatalog);
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundInventory);
      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockFoundTaxes);
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([mockFoundDiscounts[1]]);

      await expect(CatalogUseCase.updateCatalogEntry(mockCommand)).rejects.toThrow('Some discount were not found: disc123');
    });
  });

  describe('deleteCatalogEntry', () => {
    const mockCatalog = {
      id: 'cat123',
      companyId: 'comp123',
    };

    it('should delete a catalog entry', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockCatalog);

      await CatalogUseCase.deleteCatalogEntry(mockCatalog.id, mockCatalog.companyId);

      expect(Registry.CatalogRepository.findOneById).toHaveBeenCalledWith(mockCatalog.id);
      expect(Registry.CatalogRepository.deleteById).toHaveBeenCalledWith(mockCatalog.id);
    });

    it('should throw an error when the provided catalog was not found', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce(undefined);

      expect(CatalogUseCase.deleteCatalogEntry(mockCatalog.id, mockCatalog.companyId)).rejects.toThrow(new Error('Catalog not found'));

      expect(Registry.CatalogRepository.findOneById).toHaveBeenCalledWith(mockCatalog.id);
      expect(Registry.CatalogRepository.deleteById).not.toHaveBeenCalled();
    });

    it('should throw an error when the provided catalog does not belong to the provided company', async () => {
      (Registry.CatalogRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockCatalog);

      expect(CatalogUseCase.deleteCatalogEntry(mockCatalog.id, 'wrongcompany')).rejects.toThrow(new Error('Catalog does not belong to the provided company'));

      expect(Registry.CatalogRepository.findOneById).toHaveBeenCalledWith(mockCatalog.id);
      expect(Registry.CatalogRepository.deleteById).not.toHaveBeenCalled();
    });
  });

  describe('findCatalogForClient', () => {
    const companyId = 'comp124';

    const mockClient = {
      id: 'client123',
      companyId,
    };

    const mockCommand: FindCatalogEntriesForClientCommand = {
      query: { page: 1, pageSize: 10 },
      companyId,
      clientId: mockClient.id,
    };

    const mockCatalogs = [
      {
        id: 'cat123',
        name: 'product',
        readId: 'product_123',
        description: 'product description',
        price: 100,
        requiresStock: true,
        attributes: [],
        type: CatalogType.PRODUCT,
        discountIds: ['disc123'],
        taxIds: ['tax123'],
        inventoryRelations: [
          {
            id: 'inv123',
            quantity: 10,
          },
        ],
        companyId: 'comp123',
        mediaIds: ['media1', 'media2'],
      },
    ];

    const mockMedia = [
      { id: 'media1', url: 'http://example.com/media1.jpg', mediaType: 'IMAGE' },
      { id: 'media2', url: 'http://example.com/media2.jpg', mediaType: 'VIDEO' },
    ];

    const mockCatalogsWithMedia = [
      {
        ...mockCatalogs[0],
        media: [
          { id: 'media1', url: 'http://example.com/media1.jpg', type: 'IMAGE' },
          { id: 'media2', url: 'http://example.com/media2.jpg', type: 'VIDEO' },
        ],
      },
    ];

    it('should throw if client has not the same company than requested', async () => {
      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValueOnce({ ...mockClient, companyId: 'wrongCompany' });

      await expect(CatalogUseCase.findCatalogForClient({
        ...mockCommand, clientId: mockClient.id,
      })).rejects.toThrow('Client does not belong to the provided company');
    });

    it('should throw if client is not found', async () => {
      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValueOnce(undefined);

      await expect(CatalogUseCase.findCatalogForClient(mockCommand)).rejects.toThrow('Client not found');
    });

    it('Should return the catalog for the client', async () => {
      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockClient);
      (Registry.CatalogRepository.findPaginated as jest.Mock).mockResolvedValueOnce({ data: mockCatalogs, count: 1 });
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockMedia);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce(mockCatalogs);

      const result = await CatalogUseCase.findCatalogForClient(mockCommand);

      expect(result).toEqual({ data: mockCatalogsWithMedia, count: 1 });
      expect(Registry.ClientRepository.findOneById).toHaveBeenCalledWith(mockClient.id);
      expect(Registry.CatalogRepository.findPaginated).toHaveBeenCalledWith({
        ...mockCommand.query,
        filters: { companyId: [companyId] },
      });
      expect(Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds).toHaveBeenCalledWith([companyId], ['cat123'], [mockClient.id]);
      expect(Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds).toHaveBeenCalledWith([companyId], ['cat123'], null);
      expect(Registry.StoreDiscountRepository.findManyForCompaniesByClientIds).toHaveBeenCalledWith([companyId], [mockClient.id]);
      expect(Registry.StoreDiscountRepository.findManyForCompaniesByClientIds).toHaveBeenCalledWith([companyId], null);
      expect(Registry.CatalogMediaRepository.findManyByIds).toHaveBeenCalledWith(['media1', 'media2']);
      expect(CatalogDomainService.calculateDiscountsForCatalogByClient).toHaveBeenCalledWith(expect.objectContaining({
        catalog: expect.any(Array),
        client: mockClient,
      }));
    });
  });

  describe('findProvidersCatalog', () => {
    const companyId = 'comp123';
    const provider1CompanyId = 'prov123';
    const provider2CompanyId = 'prov124';

    const mockCommand: FindCatalogEntriesForProviderCommand = {
      query: {
        page: 1,
        pageSize: 10,
        filters: { providerId: [] },
      },
      companyId,
    };

    const mockClients: ClientEntity[] = [
      { id: 'client123', companyId: provider1CompanyId } as ClientEntity,
      { id: 'client124', companyId: provider2CompanyId } as ClientEntity,
    ];

    const mockProviders: ProviderEntity[] = [
      { id: 'provider1', providerCompanyId: provider1CompanyId, name: 'provider1Name' } as ProviderEntity,
      { id: 'provider2', providerCompanyId: provider2CompanyId, name: 'provider2Name' } as ProviderEntity,
    ];

    const mockCatalogs: CatalogEntity[] = [
      {
        id: 'cat123',
        name: 'Product 1',
        companyId: provider1CompanyId,
        price: 100,
        mediaIds: ['media1', 'media2'],
      } as CatalogEntity,
      {
        id: 'cat124',
        name: 'Product 2',
        companyId: provider2CompanyId,
        price: 200,
        mediaIds: ['media3'],
      } as CatalogEntity,
    ];

    const mockMedia: CatalogMediaEntity[] = [
      { id: 'media1', url: 'http://example.com/media1.jpg', mediaType: 'IMAGE' as MediaType } as CatalogMediaEntity,
      { id: 'media2', url: 'http://example.com/media2.jpg', mediaType: 'VIDEO' as MediaType } as CatalogMediaEntity,
      { id: 'media3', url: 'http://example.com/media3.jpg', mediaType: 'IMAGE' as MediaType } as CatalogMediaEntity,
    ];

    interface CatalogWithMedia extends CatalogEntity {
      media: {
        id: string;
        url: string;
        type: string;
      }[];
    }

    const mockCatalogsWithMedia: CatalogWithMedia[] = [
      {
        ...mockCatalogs[0],
        media: [
          { id: 'media1', url: 'http://example.com/media1.jpg', type: 'IMAGE' },
          { id: 'media2', url: 'http://example.com/media2.jpg', type: 'VIDEO' },
        ],
      } as CatalogWithMedia,
      {
        ...mockCatalogs[1],
        media: [
          { id: 'media3', url: 'http://example.com/media3.jpg', type: 'IMAGE' },
        ],
      } as CatalogWithMedia,
    ];

    interface ProcessedCatalog extends CatalogWithMedia {
      quantity: number;
      discountAmount: number;
      provider: {
        id: string;
        companyId: string;
        name: string;
      }
    }

    const mockProcessedCatalog: ProcessedCatalog[] = [
      {
        ...mockCatalogsWithMedia[0],
        quantity: 1,
        discountAmount: 10,
        provider: {
          id: mockProviders[0].id,
          companyId: mockProviders[0].providerCompanyId,
          name: mockProviders[0].name,
        },
      } as ProcessedCatalog,
      {
        ...mockCatalogsWithMedia[1],
        quantity: 1,
        discountAmount: 20,
        provider: {
          id: mockProviders[1].id,
          companyId: mockProviders[1].providerCompanyId,
          name: mockProviders[1].name,
        },
      } as ProcessedCatalog,
    ];

    it('should throw an error if company is not client of any other company', async () => {
      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce([]);

      await expect(CatalogUseCase.findProvidersCatalog(mockCommand))
        .rejects.toThrow('Company is not client of any other company');
    });

    it('should throw an error if client has no providers', async () => {
      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce(mockClients);
      (Registry.ProviderRepository.findProvidersForCompany as jest.Mock).mockResolvedValueOnce([]);

      await expect(CatalogUseCase.findProvidersCatalog(mockCommand))
        .rejects.toThrow('Client has not providers');
    });

    it('should return catalog from all providers when no providerId filter is specified', async () => {
      // Setup mocks
      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce(mockClients);
      (Registry.ProviderRepository.findProvidersForCompany as jest.Mock).mockResolvedValueOnce(mockProviders);
      (Registry.CatalogRepository.findPaginated as jest.Mock).mockResolvedValueOnce({
        data: mockCatalogs,
        count: mockCatalogs.length,
      });
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockMedia);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce([mockProcessedCatalog[0]]);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce([mockProcessedCatalog[1]]);

      // Execute
      const result = await CatalogUseCase.findProvidersCatalog(mockCommand);

      // Verify
      expect(result).toEqual({
        data: mockProcessedCatalog,
        count: mockCatalogs.length,
      });

      expect(Registry.ClientRepository.findManyByClientCompanyIds).toHaveBeenCalledWith([companyId]);
      expect(Registry.ProviderRepository.findProvidersForCompany).toHaveBeenCalledWith(companyId);
      expect(Registry.CatalogRepository.findPaginated).toHaveBeenCalledWith({
        ...mockCommand.query,
        filters: { companyId: [provider1CompanyId, provider2CompanyId] },
      });
      expect(Registry.CatalogMediaRepository.findManyByIds).toHaveBeenCalledWith(['media1', 'media2', 'media3']);
      expect(CatalogDomainService.calculateDiscountsForCatalogByClient).toHaveBeenCalledTimes(2);
    });

    it('should filter providers by providerId when specified', async () => {
      // Setup command with provider filter
      const mockCommandWithFilter = {
        ...mockCommand,
        query: {
          ...mockCommand.query,
          filters: { providerId: ['provider1'] },
        },
      };

      // Setup mocks
      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce(mockClients);
      (Registry.ProviderRepository.findProvidersForCompany as jest.Mock).mockResolvedValueOnce(mockProviders);
      (Registry.CatalogRepository.findPaginated as jest.Mock).mockResolvedValueOnce({
        data: [mockCatalogs[0]],
        count: 1,
      });
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([mockMedia[0], mockMedia[1]]);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce([mockProcessedCatalog[0]]);

      // Execute
      const result = await CatalogUseCase.findProvidersCatalog(mockCommandWithFilter);

      // Verify
      expect(result).toEqual({
        data: [mockProcessedCatalog[0]],
        count: 1,
      });

      // Verify provider filtering
      expect(Registry.ProviderRepository.findProvidersForCompany).toHaveBeenCalledWith(companyId);
      expect(Registry.CatalogRepository.findPaginated).toHaveBeenCalledWith({
        ...mockCommandWithFilter.query,
        filters: { companyId: [provider1CompanyId] },
      });
    });

    it('should handle providers without companyId', async () => {
      // Provider without companyId
      const mockProvidersWithoutCompany: ProviderEntity[] = [
        { ...mockProviders[0], providerCompanyId: null },
        mockProviders[1],
      ];

      // Setup mocks
      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce(mockClients);
      (Registry.ProviderRepository.findProvidersForCompany as jest.Mock).mockResolvedValueOnce(mockProvidersWithoutCompany);
      (Registry.CatalogRepository.findPaginated as jest.Mock).mockResolvedValueOnce({
        data: [mockCatalogs[1]],
        count: 1,
      });
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([mockMedia[2]]);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce([mockProcessedCatalog[1]]);

      // Execute
      const result = await CatalogUseCase.findProvidersCatalog(mockCommand);

      // Verify
      expect(result).toEqual({
        data: [mockProcessedCatalog[1]],
        count: 1,
      });

      // Verify only provider with companyId is used
      expect(Registry.CatalogRepository.findPaginated).toHaveBeenCalledWith({
        ...mockCommand.query,
        filters: { companyId: [provider2CompanyId] },
      });
    });

    it('should handle when provider company has no catalogs', async () => {
      const emptyPaginatedCatalog = {
        data: [],
        count: 0,
      };

      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce(mockClients);
      (Registry.ProviderRepository.findProvidersForCompany as jest.Mock).mockResolvedValueOnce(mockProviders);
      (Registry.CatalogRepository.findPaginated as jest.Mock).mockResolvedValueOnce(emptyPaginatedCatalog);
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([]);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce([]);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce([]);

      const result = await CatalogUseCase.findProvidersCatalog(mockCommand);

      expect(result).toEqual({
        data: [],
        count: 0,
      });
    });

    it('should handle when a provider has no clients associated with the company', async () => {
      // Use a provider with a company ID but the client list will be empty for that company
      const providerWithNoClients = {
        id: 'provider3',
        providerCompanyId: 'prov125',
      } as ProviderEntity;

      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce(mockClients);
      (Registry.ProviderRepository.findProvidersForCompany as jest.Mock).mockResolvedValueOnce([providerWithNoClients]);
      (Registry.CatalogRepository.findPaginated as jest.Mock).mockResolvedValueOnce({
        data: [],
        count: 0,
      });
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([]);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce([]);

      const result = await CatalogUseCase.findProvidersCatalog(mockCommand);

      expect(result).toEqual({
        data: [],
        count: 0,
      });
    });

    it('should handle when filters are not provided', async () => {
      // Setup command with other filters but no providerId
      const mockCommandWithEmptyFilter: FindCatalogEntriesForProviderCommand = {
        ...mockCommand,
        query: {
          ...mockCommand.query,
          filters: undefined,
        },
      };

      // Setup mocks
      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce(mockClients);
      (Registry.ProviderRepository.findProvidersForCompany as jest.Mock).mockResolvedValueOnce(mockProviders);
      (Registry.CatalogRepository.findPaginated as jest.Mock).mockResolvedValueOnce({
        data: mockCatalogs,
        count: mockCatalogs.length,
      });
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValueOnce([]);
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(mockMedia);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce([mockProcessedCatalog[0]]);
      (CatalogDomainService.calculateDiscountsForCatalogByClient as jest.Mock).mockReturnValueOnce([mockProcessedCatalog[1]]);

      // Execute
      const result = await CatalogUseCase.findProvidersCatalog(mockCommandWithEmptyFilter);

      // Verify
      expect(result).toEqual({
        data: mockProcessedCatalog,
        count: mockCatalogs.length,
      });

      // Verify all providers are considered since no providerId filter is present
      expect(Registry.ProviderRepository.findProvidersForCompany).toHaveBeenCalledWith(companyId);
      expect(Registry.CatalogRepository.findPaginated).toHaveBeenCalledWith({
        ...mockCommandWithEmptyFilter.query,
        filters: {
          companyId: [provider1CompanyId, provider2CompanyId],
        },
      });
    });
  });
});
