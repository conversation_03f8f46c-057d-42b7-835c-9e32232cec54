import { PaginatedQueryFilter, PaginatedQueryWithSearchAndFilters } from '#application/Common.Type';
import CatalogEntity from '#domain/aggregates/catalog/Catalog.Entity';
import { PaginatedResult } from '#domain/common/Common.Type';

export interface CatalogFilters extends PaginatedQueryFilter {
  companyId?: string[];
  disabled?: boolean;
  type?: string;
}

export default interface CatalogRepository {
  save(Catalog: CatalogEntity[]): Promise<CatalogEntity[]>;
  findOneById(id: string): Promise<CatalogEntity | undefined>;
  findManyByIds(ids: string[]): Promise<CatalogEntity[]>;
  findManyForCompanyByIds(companyId: string, ids: string[]): Promise<CatalogEntity[]>;
  deleteById(id: string): Promise<undefined>;
  getIdsFromReadIds(readIds: string[]): Promise<string[]>;
  findPaginated(query: PaginatedQueryWithSearchAndFilters<CatalogFilters>): Promise<PaginatedResult<CatalogEntity>>;
}
