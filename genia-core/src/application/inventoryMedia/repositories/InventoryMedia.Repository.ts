import InventoryMediaEntity from '#domain/aggregates/inventoryMedia/InventoryMedia.Entity';

interface InventoryMediaRepository {
  findManyByInventoryId(id: string): Promise<InventoryMediaEntity[]>;
  findManyByIds(ids: string[]): Promise<InventoryMediaEntity[]>;
  save(medias: InventoryMediaEntity[]): Promise<InventoryMediaEntity[]>;
  deleteByIds(ids: string[]): Promise<undefined>;
}

export default InventoryMediaRepository;
