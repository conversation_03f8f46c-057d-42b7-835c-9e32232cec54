export enum NotificationType {
  CLIENT_INVITATION = 'client_invitation',
  PROVIDER_INVITATION = 'provider_invitation',
  SALE_ORDER_CREATED = 'sale_order_created',
  PURCHASE_ORDER_CREATED = 'purchase_order_created',
}

export interface Notification {
  id: string;
  ownerUserId: string;
  companyId: string;
  type: NotificationType;
  requiredRoles: string[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  payload: Record<any, any> | null;
  createdAt: Date;
}

export default interface NotificationService {
  save(Provider: Notification[]): Promise<Notification[]>;
  deleteByInvitationId(invitationId: string): Promise<undefined>;
}
