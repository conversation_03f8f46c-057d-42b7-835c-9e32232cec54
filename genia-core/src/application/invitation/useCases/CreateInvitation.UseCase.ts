import { difference } from 'lodash';

import { ApplicationError } from '#application/Common.Type';
import CompanyErrorCodes from '#application/company/Company.ErrorCodes';
import CreateInvitationsCommand from '#application/invitation/commands/CreateInvitations.Command';
import { Invitation, InvitationState, InvitationType } from '#application/invitation/services/Invitation.Service';
import { Notification, NotificationType } from '#application/notification/services/Notification.Service';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';

async function sendInvitationMessages(invitationEntities: Invitation[], inviter: string, companyName: string): Promise<undefined> {
  const emailPromises = invitationEntities.filter(({ email }) => email).map(
    ({ id, email }) => Registry.EmailService.sendInvitation(inviter, companyName, email!, id),
  );

  const whatsappPromises = invitationEntities.filter(({ phoneNumber }) => phoneNumber).map(
    ({ id, phoneNumber }) => Registry.MessageService.sendInvitation(inviter, companyName, phoneNumber!, id),
  );

  await Promise.all([...emailPromises, ...whatsappPromises]);
}

async function apply(command: CreateInvitationsCommand): Promise<Invitation[]> {
  const { userId, invitations } = command;

  const user = (await Registry.UserRepository.findOneById(userId))!;

  const { companies: [inviterCompanyId] } = user;

  const invitationEntities: Invitation[] = await Promise.all(invitations.map(async ({
    email, phoneNumber, invitedCompanyId, type,
  }) => ({
    id: await Registry.IdentificationService.generateId(),
    companyId: inviterCompanyId,
    invitedCompanyId: invitedCompanyId || null,
    email: email || null,
    phoneNumber: phoneNumber || null,
    type,
    state: InvitationState.PENDING,
  })));

  return Registry.TransactionService.transactional(async () => {
    const savedInvitations = await Registry.InvitationService.save(invitationEntities);

    const invitationsWithoutCompany = invitationEntities.filter(({ invitedCompanyId }) => !invitedCompanyId);
    const invitationsWithCompany = invitationEntities.filter(({ invitedCompanyId }) => invitedCompanyId);
    const inviterName = `${user.name} ${user.lastName}`;

    if (invitationsWithCompany.length) {
      const invitedCompanyIds = invitationsWithCompany.map(({ invitedCompanyId }) => invitedCompanyId!);
      const foundCompanyIds = (await Registry.CompanyRepository.findManyByIds(invitedCompanyIds)).map(({ id }) => id);

      if (foundCompanyIds.length !== invitedCompanyIds.length) {
        const notFoundIds = difference(invitedCompanyIds, foundCompanyIds);

        throw new ApplicationError(
          `Some companies were not found: ${notFoundIds.join(',')}`,
          Cause.NOT_FOUND,
          CompanyErrorCodes.COMPANY_NOT_FOUND,
        );
      }

      const notifications: Notification[] = await Promise.all(invitationsWithCompany.map(async (invitation) => ({
        id: await Registry.IdentificationService.generateId(),
        ownerUserId: user.id,
        payload: {
          invitationId: invitation.id,
        },
        companyId: invitation.invitedCompanyId!,
        type: invitation.type === InvitationType.CLIENT ? NotificationType.CLIENT_INVITATION : NotificationType.PROVIDER_INVITATION,
        requiredRoles: [],
        createdAt: new Date(Date.now()),
      })));

      await Registry.NotificationService.save(notifications);
    }

    if (invitationsWithoutCompany.length) {
      const { name: companyName } = (await Registry.CompanyRepository.findOneById(inviterCompanyId))!;

      await sendInvitationMessages(invitationsWithoutCompany, inviterName, companyName);
    }

    return savedInvitations;
  });
}

export default {
  apply,
};
