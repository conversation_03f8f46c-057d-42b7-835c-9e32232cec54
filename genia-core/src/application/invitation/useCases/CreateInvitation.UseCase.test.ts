import CreateInvitationsCommand from '#application/invitation/commands/CreateInvitations.Command';
import { InvitationState, InvitationType } from '#application/invitation/services/Invitation.Service';
import { NotificationType } from '#application/notification/services/Notification.Service';
import Registry from '#composition/ImplementationRegistry';

import CreateInvitationUseCase from './CreateInvitation.UseCase';

jest.unmock('lodash');

jest.unmock('#application/Common.Type');

jest.unmock('./CreateInvitation.UseCase');

describe('CreateInvitationUseCase.apply', () => {
  const mockUser = {
    id: 'user-1',
    name: '<PERSON>',
    lastName: 'Doe',
    companies: ['company-1'],
  };

  const mockCompany = {
    id: 'company-1',
    name: 'Test Company',
  };

  beforeEach(() => {
    jest.resetAllMocks();
    (Registry.UserRepository.findOneById as jest.Mock).mockResolvedValue(mockUser);
    (Registry.CompanyRepository.findOneById as jest.Mock).mockResolvedValue(mockCompany);
    (Registry.CompanyRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCompany]);

    (Registry.IdentificationService.generateId as jest.Mock)
      .mockResolvedValueOnce('inv-1')
      .mockResolvedValueOnce('not-1');
    (Registry.InvitationService.save as jest.Mock).mockImplementation((invs) => Promise.resolve(invs));
    (Registry.NotificationService.save as jest.Mock).mockImplementation((nots) => Promise.resolve(nots));
    (Registry.TransactionService.transactional as jest.Mock).mockImplementation((fn) => fn());
  });

  it('should create invitations and send messages when no invitedCompanyId', async () => {
    const command = {
      userId: 'user-1',
      invitations: [
        {
          email: '<EMAIL>',
          phoneNumber: '**********',
          type: InvitationType.CLIENT,
        },
      ],
    };

    const result = await CreateInvitationUseCase.apply(command);

    expect(Registry.EmailService.sendInvitation).toHaveBeenCalledWith(
      'John Doe',
      'Test Company',
      '<EMAIL>',
      'inv-1',
    );

    expect(Registry.MessageService.sendInvitation).toHaveBeenCalledWith(
      'John Doe',
      'Test Company',
      '**********',
      'inv-1',
    );

    expect(Registry.NotificationService.save).not.toHaveBeenCalled();
    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      id: expect.any(String),
      email: '<EMAIL>',
      phoneNumber: '**********',
      companyId: 'company-1',
      type: InvitationType.CLIENT,
      state: InvitationState.PENDING,
    });
  });

  it('should create invitations with notifications when invitedCompanyId exists', async () => {
    const command: CreateInvitationsCommand = {
      userId: 'user-1',
      invitations: [
        {
          invitedCompanyId: 'comp1',
          email: '<EMAIL>',
          phoneNumber: '**********',
          type: InvitationType.CLIENT,
        },
      ],
    };

    const result = await CreateInvitationUseCase.apply(command);

    expect(Registry.EmailService.sendInvitation).not.toHaveBeenCalled();
    expect(Registry.MessageService.sendInvitation).not.toHaveBeenCalled();

    expect(Registry.NotificationService.save).toHaveBeenCalledWith([
      {
        id: expect.any(String),
        ownerUserId: 'user-1',
        payload: { invitationId: result[0].id },
        companyId: command.invitations[0].invitedCompanyId,
        type: NotificationType.CLIENT_INVITATION,
        requiredRoles: [],
        createdAt: expect.any(Date),
      },
    ]);

    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      id: expect.any(String),
      email: command.invitations[0].email,
      phoneNumber: command.invitations[0].phoneNumber,
      companyId: mockCompany.id,
      invitedCompanyId: 'comp1',
      type: InvitationType.CLIENT,
      state: InvitationState.PENDING,
    });
  });

  it('should handle both email and SMS invitations', async () => {
    const command: CreateInvitationsCommand = {
      userId: 'user-1',
      invitations: [
        {
          email: '<EMAIL>',
          type: InvitationType.PROVIDER,
        },
        {
          phoneNumber: '**********',
          type: InvitationType.PROVIDER,
        },
      ],
    };

    await CreateInvitationUseCase.apply(command);

    expect(Registry.EmailService.sendInvitation).toHaveBeenCalledWith(
      'John Doe',
      'Test Company',
      '<EMAIL>',
      'inv-1',
    );

    expect(Registry.MessageService.sendInvitation).toHaveBeenCalledWith(
      'John Doe',
      'Test Company',
      '**********',
      'not-1',
    );
  });

  it('should throw an error when an invited company is not found', async () => {
    const command: CreateInvitationsCommand = {
      userId: 'user-1',
      invitations: [
        {
          email: '<EMAIL>',
          type: InvitationType.CLIENT,
          invitedCompanyId: 'comp2',
        },
        {
          phoneNumber: '**********',
          type: InvitationType.PROVIDER,
          invitedCompanyId: 'comp3',
        },
      ],
    };

    (Registry.CompanyRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

    await expect(CreateInvitationUseCase.apply(command)).rejects.toThrow('Some companies were not found: comp2,comp3');
  });
});
