import { InvitationState, InvitationType } from '#application/invitation/services/Invitation.Service';
import UpdateInvitationUseCase from '#application/invitation/useCases/UpdateInvitation.UseCase';
import { NotificationType } from '#application/notification/services/Notification.Service';
import Registry from '#composition/ImplementationRegistry';
import ClientOperator from '#domain/aggregates/client/Client.Operator';
import ProviderOperator from '#domain/aggregates/provider/Provider.Operator';

jest.unmock('#application/invitation/useCases/UpdateInvitation.UseCase');
jest.unmock('#application/Common.Type');

describe('UpdateInvitationUseCase.apply', () => {
  const mockInvitation = {
    id: 'inv-1',
    companyId: 'comp-1',
    invitedCompanyId: null,
    email: '<EMAIL>',
    phoneNumber: '**********',
    type: InvitationType.CLIENT,
    state: InvitationState.PENDING,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (Registry.InvitationService.findOneById as jest.Mock).mockResolvedValue(mockInvitation);
    (Registry.InvitationService.save as jest.Mock).mockImplementation(async (invs) => invs);
  });

  it('should update invitation successfully when there was no previous companyId', async () => {
    const command = {
      invitationId: 'inv-1',
      invitedCompanyId: 'comp-1',
      userId: 'user-1',
    };

    const result = await UpdateInvitationUseCase.apply(command);

    expect(Registry.InvitationService.findOneById).toHaveBeenCalledWith('inv-1');
    expect(Registry.InvitationService.save).toHaveBeenCalledWith([
      expect.objectContaining({
        ...mockInvitation,
        invitedCompanyId: command.invitedCompanyId,
      }),
    ]);

    expect(Registry.NotificationService.save).toHaveBeenCalledWith([{
      id: expect.any(String),
      ownerUserId: command.userId,
      payload: { invitationId: command.invitationId },
      companyId: command.invitedCompanyId,
      type: NotificationType.CLIENT_INVITATION,
      requiredRoles: [],
      createdAt: expect.any(Date),
    }]);

    expect(result).toEqual({ ...mockInvitation, invitedCompanyId: command.invitedCompanyId });
  });

  it('should throw if invitation not found', async () => {
    (Registry.InvitationService.findOneById as jest.Mock).mockResolvedValue(undefined);

    const command = {
      invitationId: 'non-existent',
      state: InvitationState.ACCEPTED,
      invitedCompanyId: 'comp-1',
      userId: 'user-1',
    };

    await expect(UpdateInvitationUseCase.apply(command)).rejects.toThrow('Invitation with id non-existent not found');
  });

  it('should throw if trying to update invitedCompanyId when already set', async () => {
    const invitationWithCompany = {
      ...mockInvitation,
      invitedCompanyId: 'comp-2',
    };

    (Registry.InvitationService.findOneById as jest.Mock).mockResolvedValue(invitationWithCompany);

    const command = {
      invitationId: 'inv-1',
      invitedCompanyId: 'comp-3',
      userId: 'user-1',
    };

    await expect(UpdateInvitationUseCase.apply(command)).rejects.toThrow('Invited company should not be updated');
  });

  it('should handle state transition to REJECTED', async () => {
    const invitationWithCompany = {
      ...mockInvitation,
      invitedCompanyId: 'comp-2',
    };

    (Registry.InvitationService.findOneById as jest.Mock).mockResolvedValue(invitationWithCompany);

    const command = {
      invitationId: 'inv-1',
      state: InvitationState.REJECTED,
      invitedCompanyId: 'comp-2',
      userId: 'user-1',
    };

    const result = await UpdateInvitationUseCase.apply(command);

    expect(Registry.CompanyRepository.findOneById).not.toHaveBeenCalled();
    expect(Registry.InvitationService.save).toHaveBeenCalledWith([{ ...invitationWithCompany, state: InvitationState.REJECTED }]);
    expect(result.state).toBe(InvitationState.REJECTED);
    expect(Registry.NotificationService.save).not.toHaveBeenCalled();
  });

  it('should handle state transition to ACEPTED as CLIENT_INVITATION', async () => {
    const invitation = {
      ...mockInvitation,
      type: InvitationType.CLIENT,
      companyId: 'providerCompanyId',
      invitedCompanyId: 'clientCompanyId',
      state: InvitationState.PENDING,
    };

    const mockProvider = {
      id: 'providerCompanyId',
      name: 'Provider Company',
      tributaryId: 'prov-tributary',
      contactInformation: {
        salesEmail: 'providerSalesEmail',
        salesPhoneNumber: 'providerSalesPhoneNumber',
        salesWhatsapp: 'providerSalesWhatsapp',
        purchasesEmail: 'providerPurchasesEmail',
        purchasesPhoneNumber: 'providerPurchasesPhoneNumber',
        purchasesWhatsapp: 'providerPurchasesWhatsapp',
        billingEmail: 'providerBillingEmail',
        billingPhoneNumber: 'providerBillingPhoneNumber',
        billingWhatsapp: 'providerBillingWhatsapp',
        billingAddress: 'providerBillingAddress',
        shippingAddress: 'providerShippingAddress',
      },
    };

    const mockClient = {
      id: 'clientCompanyId',
      name: 'Client Company',
      tributaryId: 'cli-tributary',
      contactInformation: {
        salesEmail: 'clientSalesEmail',
        salesPhoneNumber: 'clientSalesPhoneNumber',
        salesWhatsapp: 'clientSalesWhatsapp',
        purchasesEmail: 'clientPurchasesEmail',
        purchasesPhoneNumber: 'clientPurchasesPhoneNumber',
        purchasesWhatsapp: 'clientPurchasesWhatsapp',
        billingEmail: 'clientBillingEmail',
        billingPhoneNumber: 'clientBillingPhoneNumber',
        billingWhatsapp: 'clientBillingWhatsapp',
        billingAddress: 'clientBillingAddress',
        shippingAddress: 'clientShippingAddress',
      },
    };

    (ProviderOperator.build as jest.Mock).mockImplementation((prov) => prov);
    (ClientOperator.build as jest.Mock).mockImplementation((cli) => cli);
    (Registry.InvitationService.findOneById as jest.Mock).mockResolvedValue(invitation);
    (Registry.IdentificationService.generateId as jest.Mock)
      .mockResolvedValueOnce('prov-1')
      .mockResolvedValueOnce('cli-1');
    (Registry.CompanyRepository.findOneById as jest.Mock)
      .mockResolvedValueOnce(mockProvider)
      .mockResolvedValueOnce(mockClient);

    const command = {
      invitationId: 'inv-1',
      invitedCompanyId: 'clientCompanyId',
      state: InvitationState.ACCEPTED,
      userId: 'user-1',
    };

    const result = await UpdateInvitationUseCase.apply(command);

    expect(Registry.CompanyRepository.findOneById).toHaveBeenCalledWith('clientCompanyId');
    expect(Registry.CompanyRepository.findOneById).toHaveBeenCalledWith('providerCompanyId');
    expect(Registry.ProviderRepository.save).toHaveBeenCalledWith({
      id: 'prov-1',
      name: 'Provider Company',
      tributaryId: 'prov-tributary',
      providerCompanyId: 'providerCompanyId',
      companyId: 'clientCompanyId',
      contactInformation: {
        salesEmail: 'providerSalesEmail',
        salesPhoneNumber: 'providerSalesPhoneNumber',
        salesWhatsapp: 'providerSalesWhatsapp',
      },
    });
    expect(Registry.ClientRepository.save).toHaveBeenCalledWith({
      id: 'cli-1',
      name: 'Client Company',
      tributaryId: 'cli-tributary',
      clientCompanyId: 'clientCompanyId',
      companyId: 'providerCompanyId',
      contactInformation: {
        purchasesEmail: 'clientPurchasesEmail',
        purchasesPhoneNumber: 'clientPurchasesPhoneNumber',
        purchasesWhatsapp: 'clientPurchasesWhatsapp',
      },
    });
    expect(Registry.InvitationService.save).toHaveBeenCalledWith([{ ...invitation, state: InvitationState.ACCEPTED }]);
    expect(result.state).toBe(InvitationState.ACCEPTED);
    expect(Registry.NotificationService.save).not.toHaveBeenCalled();
  });

  it('should handle state transition to ACEPTED as PROVIDER_INVITATION', async () => {
    const invitation = {
      ...mockInvitation,
      type: InvitationType.PROVIDER,
      companyId: 'clientCompanyId',
      invitedCompanyId: 'providerCompanyId',
      state: InvitationState.PENDING,
    };

    const mockProvider = {
      id: 'providerCompanyId',
      name: 'Provider Company',
      tributaryId: 'prov-tributary',
      contactInformation: {
        salesEmail: 'providerSalesEmail',
        salesPhoneNumber: 'providerSalesPhoneNumber',
        salesWhatsapp: 'providerSalesWhatsapp',
        purchasesEmail: 'providerPurchasesEmail',
        purchasesPhoneNumber: 'providerPurchasesPhoneNumber',
        purchasesWhatsapp: 'providerPurchasesWhatsapp',
        billingEmail: 'providerBillingEmail',
        billingPhoneNumber: 'providerBillingPhoneNumber',
        billingWhatsapp: 'providerBillingWhatsapp',
        billingAddress: 'providerBillingAddress',
        shippingAddress: 'providerShippingAddress',
      },
    };

    const mockClient = {
      id: 'clientCompanyId',
      name: 'Client Company',
      tributaryId: 'cli-tributary',
      contactInformation: {
        salesEmail: 'clientSalesEmail',
        salesPhoneNumber: 'clientSalesPhoneNumber',
        salesWhatsapp: 'clientSalesWhatsapp',
        purchasesEmail: 'clientPurchasesEmail',
        purchasesPhoneNumber: 'clientPurchasesPhoneNumber',
        purchasesWhatsapp: 'clientPurchasesWhatsapp',
        billingEmail: 'clientBillingEmail',
        billingPhoneNumber: 'clientBillingPhoneNumber',
        billingWhatsapp: 'clientBillingWhatsapp',
        billingAddress: 'clientBillingAddress',
        shippingAddress: 'clientShippingAddress',
      },
    };

    (ProviderOperator.build as jest.Mock).mockImplementation((prov) => prov);
    (ClientOperator.build as jest.Mock).mockImplementation((cli) => cli);
    (Registry.InvitationService.findOneById as jest.Mock).mockResolvedValue(invitation);
    (Registry.IdentificationService.generateId as jest.Mock)
      .mockResolvedValueOnce('prov-1')
      .mockResolvedValueOnce('cli-1');
    (Registry.CompanyRepository.findOneById as jest.Mock)
      .mockResolvedValueOnce(mockProvider)
      .mockResolvedValueOnce(mockClient);

    const command = {
      invitationId: 'inv-1',
      invitedCompanyId: 'providerCompanyId',
      state: InvitationState.ACCEPTED,
      userId: 'user-1',
    };

    const result = await UpdateInvitationUseCase.apply(command);

    expect(Registry.CompanyRepository.findOneById).toHaveBeenCalledWith('clientCompanyId');
    expect(Registry.CompanyRepository.findOneById).toHaveBeenCalledWith('providerCompanyId');
    expect(Registry.ProviderRepository.save).toHaveBeenCalledWith({
      id: 'prov-1',
      name: 'Provider Company',
      tributaryId: 'prov-tributary',
      providerCompanyId: 'providerCompanyId',
      companyId: 'clientCompanyId',
      contactInformation: {
        salesEmail: 'providerSalesEmail',
        salesPhoneNumber: 'providerSalesPhoneNumber',
        salesWhatsapp: 'providerSalesWhatsapp',
      },
    });
    expect(Registry.ClientRepository.save).toHaveBeenCalledWith({
      id: 'cli-1',
      name: 'Client Company',
      tributaryId: 'cli-tributary',
      clientCompanyId: 'clientCompanyId',
      companyId: 'providerCompanyId',
      contactInformation: {
        purchasesEmail: 'clientPurchasesEmail',
        purchasesPhoneNumber: 'clientPurchasesPhoneNumber',
        purchasesWhatsapp: 'clientPurchasesWhatsapp',
      },
    });
    expect(Registry.InvitationService.save).toHaveBeenCalledWith([{ ...invitation, state: InvitationState.ACCEPTED }]);
    expect(result.state).toBe(InvitationState.ACCEPTED);
    expect(Registry.NotificationService.save).not.toHaveBeenCalled();
  });

  it('should throw if provider company was deleted before accepting the invitation', async () => {
    const invitation = {
      ...mockInvitation,
      type: InvitationType.PROVIDER,
      companyId: 'clientCompanyId',
      invitedCompanyId: 'providerCompanyId',
      state: InvitationState.PENDING,
    };

    (ProviderOperator.build as jest.Mock).mockImplementation((prov) => prov);
    (ClientOperator.build as jest.Mock).mockImplementation((cli) => cli);
    (Registry.InvitationService.findOneById as jest.Mock).mockResolvedValue(invitation);
    (Registry.IdentificationService.generateId as jest.Mock)
      .mockResolvedValueOnce('prov-1')
      .mockResolvedValueOnce('cli-1');
    (Registry.CompanyRepository.findOneById as jest.Mock)
      .mockResolvedValueOnce(undefined);

    const command = {
      invitationId: 'inv-1',
      invitedCompanyId: 'providerCompanyId',
      state: InvitationState.ACCEPTED,
      userId: 'user-1',
    };

    await expect(UpdateInvitationUseCase.apply(command)).rejects.toThrow('Provider company with id providerCompanyId not found');
  });

  it('should throw if client company was deleted before accepting the invitation', async () => {
    const invitation = {
      ...mockInvitation,
      type: InvitationType.PROVIDER,
      companyId: 'clientCompanyId',
      invitedCompanyId: 'providerCompanyId',
      state: InvitationState.PENDING,
    };

    (ProviderOperator.build as jest.Mock).mockImplementation((prov) => prov);
    (ClientOperator.build as jest.Mock).mockImplementation((cli) => cli);
    (Registry.InvitationService.findOneById as jest.Mock).mockResolvedValue(invitation);
    (Registry.IdentificationService.generateId as jest.Mock)
      .mockResolvedValueOnce('prov-1')
      .mockResolvedValueOnce('cli-1');
    (Registry.CompanyRepository.findOneById as jest.Mock)
      .mockResolvedValueOnce({ id: 'providerCompanyId', name: 'Provider Company', tributaryId: 'prov-tributary' })
      .mockResolvedValueOnce(undefined);

    const command = {
      invitationId: 'inv-1',
      invitedCompanyId: 'providerCompanyId',
      state: InvitationState.ACCEPTED,
      userId: 'user-1',
    };

    await expect(UpdateInvitationUseCase.apply(command)).rejects.toThrow('Client company with id clientCompanyId not found');
  });
});
