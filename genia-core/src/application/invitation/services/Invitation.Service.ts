export enum InvitationType {
  PROVIDER = 'provider',
  CLIENT = 'client',
}

export enum InvitationState {
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  PENDING = 'pending',
}

export interface Invitation {
  id: string;
  companyId: string;
  invitedCompanyId: string | null;
  email: string | null;
  phoneNumber: string | null;
  type: InvitationType;
  state: InvitationState;
}

export default interface InvitationService {
  save(Provider: Invitation[]): Promise<Invitation[]>;
  findOneById(id: string): Promise<Invitation | undefined>
}
