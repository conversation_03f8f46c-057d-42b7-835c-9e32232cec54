import CatalogDiscountEntity from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';

export default interface CatalogDiscountRepository {
  findManyByIds(ids: string[]): Promise<CatalogDiscountEntity[]>
  findManyByClients(clientIds: string[]): Promise<CatalogDiscountEntity[]>
  saveMany(catalogDiscounts: CatalogDiscountEntity[]): Promise<CatalogDiscountEntity[]>
  findManyForCompaniesByCatalogIdsAndClientIds(companyIds: string[], catalogIds: string[], clientIds: string[] | null): Promise<CatalogDiscountEntity[]>
  deleteByIds(discountIds: string[]): Promise<void>
}
