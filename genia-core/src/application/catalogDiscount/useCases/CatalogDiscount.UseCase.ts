import { difference } from 'lodash';

import CreateCatalogDiscountEntriesCommand from '#application/catalogDiscount/commands/CreateCatalogDiscountEntries.Command';
import UpdateCatalogDiscountEntryCommand from '#application/catalogDiscount/commands/UpdateCatalogDiscountEntry.Command';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import CatalogDiscountEntity from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';
import CatalogDiscountOperator from '#domain/aggregates/catalogDiscount/CatalogDiscount.Operator';
import DiscountDomainService from '#domain/domainServices/CatalogDiscount.DomainService';

async function createCatalogDiscountEntries(createCatalogDiscountEntriesCommand: CreateCatalogDiscountEntriesCommand): Promise<CatalogDiscountEntity[]> {
  const { companyId, entries } = createCatalogDiscountEntriesCommand;

  const setCatalogIds = new Set<string>();
  const setClientIds = new Set<string>();

  entries.forEach((entry) => {
    const setCurrentCatalogIds = new Set<string>();

    entry.catalogIds?.forEach((catalogId) => {
      setCatalogIds.add(catalogId);

      if (setCurrentCatalogIds.has(catalogId)) {
        throw new Error(`Catalog ${catalogId} is duplicated`, { cause: Cause.BAD_REQUEST });
      }

      setCurrentCatalogIds.add(catalogId);
    });
    entry.clientIds?.forEach((clientId) => setClientIds.add(clientId));
  });

  const catalogIds = Array.from(setCatalogIds);
  const clientIds = Array.from(setClientIds);

  if (catalogIds.length) {
    const foundCatalogs = await Registry.CatalogRepository.findManyForCompanyByIds(companyId, catalogIds);

    if (foundCatalogs.length !== catalogIds.length) {
      const foundIds = foundCatalogs.map(({ id }) => id);
      const notFoundIds = difference(catalogIds, foundIds);

      throw new Error(`Some catalogs were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
    }
  }

  if (clientIds.length) {
    const foundClients = await Registry.ClientRepository.findManyByIds(clientIds);

    if (foundClients.length !== clientIds.length) {
      const foundIds = foundClients.map(({ id }) => id);
      const notFoundIds = difference(clientIds, foundIds);

      throw new Error(`Some clients were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
    }
  }

  return Registry.TransactionService.transactional(async () => {
    const catalogDiscountEntries = await Promise.all(entries.map(async (entry) => {
      const id = await Registry.IdentificationService.generateId();

      return CatalogDiscountOperator.build({
        ...entry, id, companyId, catalogIds,
      });
    }));

    return Registry.CatalogDiscountRepository.saveMany(catalogDiscountEntries);
  });
}

async function getCatalogDiscountWithAccessCheck(companyId: string, catalogDiscountId: string): Promise<CatalogDiscountEntity> {
  const [catalogDiscount] = await Registry.CatalogDiscountRepository.findManyByIds([catalogDiscountId]);

  if (!catalogDiscount) {
    throw new Error('Catalog discount not found', { cause: Cause.NOT_FOUND });
  }

  if (companyId !== catalogDiscount.companyId) {
    throw new Error('Catalog discount does not belong to the company', { cause: Cause.FORBIDDEN });
  }

  return catalogDiscount;
}

async function assignCatalogDiscountClients(companyId: string, catalogDiscountId: string, clientIds: string[]): Promise<void> {
  const catalogDiscount = await getCatalogDiscountWithAccessCheck(companyId, catalogDiscountId);

  const foundClients = await Registry.ClientRepository.findManyByIds(clientIds);

  if (foundClients.length !== clientIds.length) {
    const foundIds = foundClients.map(({ id }) => id);
    const notFoundIds = difference(clientIds, foundIds);

    throw new Error(`Some clients were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
  }

  const notCompanyClients = foundClients.filter(({ companyId: clientCompanyId }) => companyId !== clientCompanyId);

  if (notCompanyClients.length) {
    const notCompanyClientIds = notCompanyClients.map(({ id }) => id);

    throw new Error(`Some clients do not belong to the company: ${notCompanyClientIds.join(',')}`, { cause: Cause.FORBIDDEN });
  }

  const updatedCatalogDiscount = CatalogDiscountOperator.assignClients(catalogDiscount, clientIds);

  const clientsCatalogDiscounts = (await Registry.CatalogDiscountRepository.findManyByClients(clientIds))
    .filter(({ id }) => id !== catalogDiscountId);

  DiscountDomainService.validateDiscounts([...clientsCatalogDiscounts, updatedCatalogDiscount]);

  await Registry.CatalogDiscountRepository.saveMany([updatedCatalogDiscount]);
}

async function unassignCatalogDiscountClients(companyId: string, catalogDiscountId: string, clientIds: string[]): Promise<void> {
  const catalogDiscount = await getCatalogDiscountWithAccessCheck(companyId, catalogDiscountId);

  const updatedCatalogDiscount = CatalogDiscountOperator.unassignClients(catalogDiscount, clientIds);

  await Registry.CatalogDiscountRepository.saveMany([updatedCatalogDiscount]);
}

async function assignCatalogDiscountCatalogs(companyId: string, catalogDiscountId: string, catalogIds: string[]): Promise<void> {
  const catalogDiscount = await getCatalogDiscountWithAccessCheck(companyId, catalogDiscountId);

  const foundCatalogs = await Registry.CatalogRepository.findManyByIds(catalogIds);

  if (foundCatalogs.length !== catalogIds.length) {
    const foundIds = foundCatalogs.map(({ id }) => id);
    const notFoundIds = difference(catalogIds, foundIds);

    throw new Error(`Some catalogs were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
  }

  const notCompanyCatalogs = foundCatalogs.filter(({ companyId: catalogCompanyId }) => companyId !== catalogCompanyId);

  if (notCompanyCatalogs.length) {
    const notCompanyCatalogIds = notCompanyCatalogs.map(({ id }) => id);

    throw new Error(`Some catalogs do not belong to the company: ${notCompanyCatalogIds.join(',')}`, { cause: Cause.FORBIDDEN });
  }

  const updatedCatalogDiscount = CatalogDiscountOperator.assignCatalogs(catalogDiscount, catalogIds);

  const clientsCatalogDiscounts = (await Registry.CatalogDiscountRepository.findManyByClients(catalogDiscount.clientIds))
    .filter(({ id }) => id !== catalogDiscountId);

  DiscountDomainService.validateDiscounts([...clientsCatalogDiscounts, updatedCatalogDiscount]);

  await Registry.CatalogDiscountRepository.saveMany([updatedCatalogDiscount]);
}

async function unassignCatalogDiscountCatalogs(companyId: string, catalogDiscountId: string, catalogIds: string[]): Promise<void> {
  const catalogDiscount = await getCatalogDiscountWithAccessCheck(companyId, catalogDiscountId);

  const updatedCatalogDiscount = CatalogDiscountOperator.unassignCatalogs(catalogDiscount, catalogIds);

  await Registry.CatalogDiscountRepository.saveMany([updatedCatalogDiscount]);
}

async function updateCatalogDiscount(updateCatalogDiscountEntryCommand: UpdateCatalogDiscountEntryCommand): Promise<CatalogDiscountEntity> {
  const { companyId, id: catalogDiscountId, ...params } = updateCatalogDiscountEntryCommand;
  const catalogDiscount = await getCatalogDiscountWithAccessCheck(companyId, catalogDiscountId);

  const updatedCatalogDiscount = CatalogDiscountOperator.update(catalogDiscount, params);

  const [result] = await Registry.CatalogDiscountRepository.saveMany([updatedCatalogDiscount]);

  return result;
}

async function deleteCatalogDiscounts(companyId: string, catalogDiscountIds: string[]): Promise<void> {
  const catalogDiscounts = await Registry.CatalogDiscountRepository.findManyByIds(catalogDiscountIds);

  if (catalogDiscountIds.length !== catalogDiscounts.length) {
    const foundIds = catalogDiscounts.map(({ id }) => id);
    const notFoundIds = difference(catalogDiscountIds, foundIds);

    throw new Error(`Some discounts were not found: ${notFoundIds.join(',')}`, { cause: Cause.BAD_REQUEST });
  }

  const notCompanyDiscounts = catalogDiscounts.filter(({ companyId: discountCompanyId }) => discountCompanyId !== companyId);

  if (notCompanyDiscounts.length) {
    const notCompanyDiscountIds = notCompanyDiscounts.map(({ id }) => id);

    throw new Error(`Some discounts do not belong to the company: ${notCompanyDiscountIds.join(',')}`, { cause: Cause.FORBIDDEN });
  }

  return Registry.CatalogDiscountRepository.deleteByIds(catalogDiscountIds);
}

export default {
  createCatalogDiscountEntries,
  assignCatalogDiscountClients,
  assignCatalogDiscountCatalogs,
  unassignCatalogDiscountClients,
  unassignCatalogDiscountCatalogs,
  updateCatalogDiscount,
  deleteCatalogDiscounts,
};
