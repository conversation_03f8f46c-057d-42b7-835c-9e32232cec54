import CreateCatalogDiscountEntriesCommand from '#application/catalogDiscount/commands/CreateCatalogDiscountEntries.Command';
import CatalogDiscountUseCase from '#application/catalogDiscount/useCases/CatalogDiscount.UseCase';
import Registry from '#composition/ImplementationRegistry';
import CatalogDiscountEntity from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';
import CatalogDiscountOperator from '#domain/aggregates/catalogDiscount/CatalogDiscount.Operator';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import CatalogDiscountDomainService from '#domain/domainServices/CatalogDiscount.DomainService';

jest.unmock('lodash');

jest.unmock('./CatalogDiscount.UseCase');

jest.mock('#domain/domainServices/CatalogDiscount.DomainService', () => ({
  validateDiscounts: jest.fn(),
}));

describe('CatalogDiscountUseCase', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createCatalogDiscountEntries', () => {
    const command: CreateCatalogDiscountEntriesCommand = {
      entries: [{
        discountValue: 10,
        discountType: DiscountType.AMOUNT,
        name: '10 USD',
        requiredQuantity: 0,
        startDate: new Date(),
        catalogIds: ['cat_123', 'cat_124'],
        clientIds: ['cl_123', 'cl_124'],
      }],
      companyId: 'comp_123',
    };

    const mockCatalogDiscount: CatalogDiscountEntity = {
      ...command.entries[0],
      id: 'cd_123',
      startDate: new Date(),
      endDate: null,
      disabledAt: null,
      companyId: 'comp_123',
      catalogIds: ['cat_123', 'cat_124'],
      clientIds: ['cl_123', 'cl_124'],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should create the catalog discounts correctly', async () => {
      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValue('cd_123');
      (CatalogDiscountOperator.build as jest.Mock).mockResolvedValue(mockCatalogDiscount);
      (Registry.CatalogDiscountRepository.saveMany as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (Registry.CatalogRepository.findManyForCompanyByIds as jest.Mock).mockResolvedValue([{ id: 'cat_123' }, { id: 'cat_124' }]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([{ id: 'cl_123' }, { id: 'cl_124' }]);

      const got = await CatalogDiscountUseCase.createCatalogDiscountEntries(command);

      expect(got).toEqual([mockCatalogDiscount]);
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(Registry.CatalogRepository.findManyForCompanyByIds).toHaveBeenCalledWith('comp_123', ['cat_123', 'cat_124']);
      expect(Registry.ClientRepository.findManyByIds).toHaveBeenCalledWith(['cl_123', 'cl_124']);
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(CatalogDiscountOperator.build).toHaveBeenCalledWith({
        ...command.entries[0], companyId: 'comp_123', id: 'cd_123',
      });
      expect(Registry.CatalogDiscountRepository.saveMany).toHaveBeenCalledWith([mockCatalogDiscount]);
    });

    it('should create the catalog discounts correctly with no relations', async () => {
      const commandWithNoRelations: CreateCatalogDiscountEntriesCommand = {
        entries: [{
          discountValue: 10,
          discountType: DiscountType.AMOUNT,
          name: '10 USD',
          requiredQuantity: 0,
          startDate: new Date(),
        }],
        companyId: 'comp_123',
      };

      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValue('cd_123');
      (CatalogDiscountOperator.build as jest.Mock).mockResolvedValue(mockCatalogDiscount);
      (Registry.CatalogDiscountRepository.saveMany as jest.Mock).mockResolvedValue([mockCatalogDiscount]);

      const got = await CatalogDiscountUseCase.createCatalogDiscountEntries(commandWithNoRelations);

      expect(got).toEqual([mockCatalogDiscount]);
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(Registry.CatalogRepository.findManyForCompanyByIds).not.toHaveBeenCalled();
      expect(Registry.ClientRepository.findManyByIds).not.toHaveBeenCalled();
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(CatalogDiscountOperator.build).toHaveBeenCalledWith({
        ...commandWithNoRelations.entries[0], companyId: 'comp_123', id: 'cd_123', catalogIds: [],
      });
    });

    it('should throw an error when catalogs are not found', async () => {
      (Registry.CatalogRepository.findManyForCompanyByIds as jest.Mock).mockResolvedValue([{ id: 'cat_123' }]);

      expect(CatalogDiscountUseCase.createCatalogDiscountEntries(command)).rejects.toThrow('Some catalogs were not found: cat_124');
    });

    it('should throw an error when catalogs are provided more than once', async () => {
      const commandWithDuplicatedCatalogs: CreateCatalogDiscountEntriesCommand = {
        entries: [{
          discountValue: 10,
          discountType: DiscountType.AMOUNT,
          name: '10 USD',
          requiredQuantity: 0,
          startDate: new Date(),
          catalogIds: ['cat_123', 'cat_123'],
          clientIds: ['cl_123'],
        }],
        companyId: 'comp_123',
      };

      expect(CatalogDiscountUseCase.createCatalogDiscountEntries(commandWithDuplicatedCatalogs)).rejects.toThrow('Catalog cat_123 is duplicated');
    });

    it('should throw an error when clients are not found', async () => {
      (Registry.CatalogRepository.findManyForCompanyByIds as jest.Mock).mockResolvedValue([{ id: 'cat_123' }, { id: 'cat_124' }]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([{ id: 'cl_123' }]);

      expect(CatalogDiscountUseCase.createCatalogDiscountEntries(command)).rejects.toThrow('Some clients were not found: cl_124');
    });
  });

  describe('assignCatalogDiscountClients', () => {
    const companyId = 'comp_123';
    const catalogDiscountId = 'cd_123';
    const clientIds = ['cl_123', 'cl_124'];

    const mockCatalogDiscount: CatalogDiscountEntity = {
      id: catalogDiscountId,
      name: 'Discount 1',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date(),
      endDate: null,
      disabledAt: null,
      companyId,
      catalogIds: ['cat_123'],
      clientIds: [],
      requiredQuantity: 2,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should assign clients to a catalog discount successfully', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (Registry.CatalogDiscountRepository.findManyByClients as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'cl_123', companyId },
        { id: 'cl_124', companyId },
      ]);
      (CatalogDiscountOperator.assignClients as jest.Mock).mockReturnValue({
        ...mockCatalogDiscount,
        clientIds: ['cl_123', 'cl_124'],
      });

      await CatalogDiscountUseCase.assignCatalogDiscountClients(companyId, catalogDiscountId, clientIds);

      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith([catalogDiscountId]);
      expect(Registry.ClientRepository.findManyByIds).toHaveBeenCalledWith(clientIds);
      expect(CatalogDiscountOperator.assignClients).toHaveBeenCalledWith(mockCatalogDiscount, clientIds);
      expect(Registry.CatalogDiscountRepository.saveMany).toHaveBeenCalledWith([
        { ...mockCatalogDiscount, clientIds: ['cl_123', 'cl_124'] },
      ]);
    });

    it('should throw an error if the catalog discount is not found', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      await expect(
        CatalogDiscountUseCase.assignCatalogDiscountClients(companyId, catalogDiscountId, clientIds),
      ).rejects.toThrow('Catalog discount not found');
    });

    it('should throw an error if the catalog discount does not belong to the company', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { ...mockCatalogDiscount, companyId: 'other_company' },
      ]);

      await expect(
        CatalogDiscountUseCase.assignCatalogDiscountClients(companyId, catalogDiscountId, clientIds),
      ).rejects.toThrow('Catalog discount does not belong to the company');
    });

    it('should throw an error if some clients are not found', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([{ id: 'cl_123', companyId }]);

      await expect(
        CatalogDiscountUseCase.assignCatalogDiscountClients(companyId, catalogDiscountId, clientIds),
      ).rejects.toThrow('Some clients were not found: cl_124');
    });

    it('should throw an error if some clients do not belong to the company', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'cl_123', companyId },
        { id: 'cl_124', companyId: 'other_company' },
      ]);

      await expect(
        CatalogDiscountUseCase.assignCatalogDiscountClients(companyId, catalogDiscountId, clientIds),
      ).rejects.toThrow('Some clients do not belong to the company: cl_124');
    });

    it('should validate discounts before assigning clients', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (Registry.ClientRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'cl_123', companyId },
        { id: 'cl_124', companyId },
      ]);
      (Registry.CatalogDiscountRepository.findManyByClients as jest.Mock).mockResolvedValue([mockCatalogDiscount]);

      await CatalogDiscountUseCase.assignCatalogDiscountClients(companyId, catalogDiscountId, clientIds);

      expect(CatalogDiscountDomainService.validateDiscounts).toHaveBeenCalledWith([{ ...mockCatalogDiscount, clientIds: ['cl_123', 'cl_124'] }]);
    });
  });

  describe('assignCatalogDiscountCatalogs', () => {
    const companyId = 'comp_123';
    const catalogDiscountId = 'cd_123';
    const catalogIds = ['cat_123', 'cat_124'];

    const mockCatalogDiscount: CatalogDiscountEntity = {
      id: catalogDiscountId,
      name: 'Discount 1',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date(),
      endDate: null,
      disabledAt: null,
      companyId,
      catalogIds: ['cat_125'],
      clientIds: ['cl_123'],
      requiredQuantity: 2,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should assign catalogs to a catalog discount successfully', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'cat_123', companyId },
        { id: 'cat_124', companyId },
      ]);
      (CatalogDiscountOperator.assignCatalogs as jest.Mock).mockReturnValue({
        ...mockCatalogDiscount,
        catalogIds: ['cat_125', 'cat_123', 'cat_124'],
      });

      await CatalogDiscountUseCase.assignCatalogDiscountCatalogs(companyId, catalogDiscountId, catalogIds);

      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith([catalogDiscountId]);
      expect(Registry.CatalogRepository.findManyByIds).toHaveBeenCalledWith(catalogIds);
      expect(CatalogDiscountOperator.assignCatalogs).toHaveBeenCalledWith(mockCatalogDiscount, catalogIds);
      expect(Registry.CatalogDiscountRepository.saveMany).toHaveBeenCalledWith([
        { ...mockCatalogDiscount, catalogIds: ['cat_125', 'cat_123', 'cat_124'] },
      ]);
    });

    it('should throw an error if the catalog discount is not found', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      await expect(
        CatalogDiscountUseCase.assignCatalogDiscountCatalogs(companyId, catalogDiscountId, catalogIds),
      ).rejects.toThrow('Catalog discount not found');
    });

    it('should throw an error if the catalog discount does not belong to the company', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { ...mockCatalogDiscount, companyId: 'other_company' },
      ]);

      await expect(
        CatalogDiscountUseCase.assignCatalogDiscountCatalogs(companyId, catalogDiscountId, catalogIds),
      ).rejects.toThrow('Catalog discount does not belong to the company');
    });

    it('should throw an error if some catalogs are not found', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([{ id: 'cat_123', companyId }]);

      await expect(
        CatalogDiscountUseCase.assignCatalogDiscountCatalogs(companyId, catalogDiscountId, catalogIds),
      ).rejects.toThrow('Some catalogs were not found: cat_124');
    });

    it('should throw an error if some catalogs do not belong to the company', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'cat_123', companyId },
        { id: 'cat_124', companyId: 'other_company' },
      ]);

      await expect(
        CatalogDiscountUseCase.assignCatalogDiscountCatalogs(companyId, catalogDiscountId, catalogIds),
      ).rejects.toThrow('Some catalogs do not belong to the company: cat_124');
    });
  });

  describe('unassignCatalogDiscountClients', () => {
    const companyId = 'comp_123';
    const catalogDiscountId = 'cd_123';
    const clientIds = ['cl_123', 'cl_124'];

    const mockCatalogDiscount: CatalogDiscountEntity = {
      id: catalogDiscountId,
      name: 'Discount 1',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date(),
      endDate: null,
      disabledAt: null,
      companyId,
      catalogIds: ['cat_123'],
      clientIds: ['cl_123', 'cl_124'],
      requiredQuantity: 2,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should unassign clients from a catalog discount successfully', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (CatalogDiscountOperator.unassignClients as jest.Mock).mockReturnValue({
        ...mockCatalogDiscount,
        clientIds: [],
      });

      await CatalogDiscountUseCase.unassignCatalogDiscountClients(companyId, catalogDiscountId, clientIds);

      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith([catalogDiscountId]);
      expect(CatalogDiscountOperator.unassignClients).toHaveBeenCalledWith(mockCatalogDiscount, clientIds);
      expect(Registry.CatalogDiscountRepository.saveMany).toHaveBeenCalledWith([
        { ...mockCatalogDiscount, clientIds: [] },
      ]);
    });

    it('should throw an error if the catalog discount is not found', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      await expect(
        CatalogDiscountUseCase.unassignCatalogDiscountClients(companyId, catalogDiscountId, clientIds),
      ).rejects.toThrow('Catalog discount not found');
    });

    it('should throw an error if the catalog discount does not belong to the company', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { ...mockCatalogDiscount, companyId: 'other_company' },
      ]);

      await expect(
        CatalogDiscountUseCase.unassignCatalogDiscountClients(companyId, catalogDiscountId, clientIds),
      ).rejects.toThrow('Catalog discount does not belong to the company');
    });

    it('should throw an error if some clients do not have access to the catalog discount', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (CatalogDiscountOperator.unassignClients as jest.Mock).mockImplementation(() => {
        throw new Error('Some clients do not have access to the catalog discount: cl_125');
      });

      await expect(
        CatalogDiscountUseCase.unassignCatalogDiscountClients(companyId, catalogDiscountId, ['cl_125']),
      ).rejects.toThrow('Some clients do not have access to the catalog discount: cl_125');
    });
  });

  describe('unassignCatalogDiscountCatalogs', () => {
    const companyId = 'comp_123';
    const catalogDiscountId = 'cd_123';
    const catalogIds = ['cat_123', 'cat_124'];

    const mockCatalogDiscount: CatalogDiscountEntity = {
      id: catalogDiscountId,
      name: 'Discount 1',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date(),
      endDate: null,
      disabledAt: null,
      companyId,
      catalogIds: ['cat_123', 'cat_124', 'cat_125'],
      clientIds: ['cl_123'],
      requiredQuantity: 2,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should unassign catalogs from a catalog discount successfully', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (CatalogDiscountOperator.unassignCatalogs as jest.Mock).mockReturnValue({
        ...mockCatalogDiscount,
        catalogIds: ['cat_125'],
      });

      await CatalogDiscountUseCase.unassignCatalogDiscountCatalogs(companyId, catalogDiscountId, catalogIds);

      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith([catalogDiscountId]);
      expect(CatalogDiscountOperator.unassignCatalogs).toHaveBeenCalledWith(mockCatalogDiscount, catalogIds);
      expect(Registry.CatalogDiscountRepository.saveMany).toHaveBeenCalledWith([
        { ...mockCatalogDiscount, catalogIds: ['cat_125'] },
      ]);
    });

    it('should throw an error if the catalog discount is not found', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      await expect(
        CatalogDiscountUseCase.unassignCatalogDiscountCatalogs(companyId, catalogDiscountId, catalogIds),
      ).rejects.toThrow('Catalog discount not found');
    });

    it('should throw an error if the catalog discount does not belong to the company', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { ...mockCatalogDiscount, companyId: 'other_company' },
      ]);

      await expect(
        CatalogDiscountUseCase.unassignCatalogDiscountCatalogs(companyId, catalogDiscountId, catalogIds),
      ).rejects.toThrow('Catalog discount does not belong to the company');
    });

    it('should throw an error if some catalogs are not assigned to the catalog discount', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (CatalogDiscountOperator.unassignCatalogs as jest.Mock).mockImplementation(() => {
        throw new Error('Some catalogs are not assigned: cat_126');
      });

      await expect(
        CatalogDiscountUseCase.unassignCatalogDiscountCatalogs(companyId, catalogDiscountId, ['cat_126']),
      ).rejects.toThrow('Some catalogs are not assigned: cat_126');
    });
  });

  describe('updateCatalogDiscount', () => {
    const companyId = 'comp_123';
    const catalogDiscountId = 'cd_123';

    const mockCatalogDiscount: CatalogDiscountEntity = {
      id: catalogDiscountId,
      name: 'Discount 1',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date(),
      endDate: null,
      disabledAt: null,
      companyId,
      catalogIds: ['cat_123', 'cat_124'],
      clientIds: ['cl_123'],
      requiredQuantity: 2,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const updateCommand = {
      id: catalogDiscountId,
      companyId,
      name: 'Updated Discount',
      discountValue: 20,
      requiredQuantity: 5,
    };

    it('should update the catalog discount successfully', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogDiscount]);
      (CatalogDiscountOperator.update as jest.Mock).mockReturnValue({
        ...mockCatalogDiscount,
        ...updateCommand,
        updatedAt: new Date(),
      });
      (Registry.CatalogDiscountRepository.saveMany as jest.Mock).mockResolvedValue([
        { ...mockCatalogDiscount, ...updateCommand, updatedAt: new Date() },
      ]);

      const result = await CatalogDiscountUseCase.updateCatalogDiscount(updateCommand);

      const { id, companyId: _, ...params } = updateCommand;

      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith([catalogDiscountId]);
      expect(CatalogDiscountOperator.update).toHaveBeenCalledWith(mockCatalogDiscount, params);
      expect(Registry.CatalogDiscountRepository.saveMany).toHaveBeenCalledWith([
        { ...mockCatalogDiscount, ...updateCommand, updatedAt: expect.any(Date) },
      ]);
      expect(result).toEqual({ ...mockCatalogDiscount, ...updateCommand, updatedAt: expect.any(Date) });
    });

    it('should throw an error if the catalog discount is not found', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      await expect(CatalogDiscountUseCase.updateCatalogDiscount(updateCommand)).rejects.toThrow(
        'Catalog discount not found',
      );
    });

    it('should throw an error if the catalog discount does not belong to the company', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { ...mockCatalogDiscount, companyId: 'other_company' },
      ]);

      await expect(CatalogDiscountUseCase.updateCatalogDiscount(updateCommand)).rejects.toThrow(
        'Catalog discount does not belong to the company',
      );
    });
  });

  describe('deleteCatalogDiscounts', () => {
    const companyId = 'comp_123';
    const catalogDiscountIds = ['cd_001', 'cd_002'];

    it('should delete catalog discounts successfully', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'cd_001', companyId },
        { id: 'cd_002', companyId },
      ]);
      (Registry.CatalogDiscountRepository.deleteByIds as jest.Mock).mockResolvedValueOnce(undefined);

      await CatalogDiscountUseCase.deleteCatalogDiscounts(companyId, catalogDiscountIds);

      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith(catalogDiscountIds);
      expect(Registry.CatalogDiscountRepository.deleteByIds).toHaveBeenCalledWith(catalogDiscountIds);
    });

    it('should throw an error if some discounts are not found', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'cd_001', companyId },
      ]);

      await expect(CatalogDiscountUseCase.deleteCatalogDiscounts(companyId, catalogDiscountIds)).rejects.toThrow(
        'Some discounts were not found: cd_002',
      );

      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith(catalogDiscountIds);
      expect(Registry.CatalogDiscountRepository.deleteByIds).not.toHaveBeenCalled();
    });

    it('should throw an error if some discounts do not belong to the company', async () => {
      (Registry.CatalogDiscountRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'cd_001', companyId },
        { id: 'cd_002', companyId: 'other_company' },
      ]);

      await expect(CatalogDiscountUseCase.deleteCatalogDiscounts(companyId, catalogDiscountIds)).rejects.toThrow(
        'Some discounts do not belong to the company: cd_002',
      );

      expect(Registry.CatalogDiscountRepository.findManyByIds).toHaveBeenCalledWith(catalogDiscountIds);
      expect(Registry.CatalogDiscountRepository.deleteByIds).not.toHaveBeenCalled();
    });
  });
});
