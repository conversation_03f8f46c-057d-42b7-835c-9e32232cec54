import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';

export default interface CreateCatalogDiscountEntriesCommand {
  entries: {
    discountValue: number;
    discountType: DiscountType;
    name: string;
    requiredQuantity: number;
    startDate: Date;
    endDate?: Date | null;
    disabledAt?: Date | null;
    clientIds?: string[];
    catalogIds?: string[];
  }[]
  companyId: string;
}
