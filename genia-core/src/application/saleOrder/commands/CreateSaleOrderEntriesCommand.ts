export interface CreateSaleOrderEntriesOrderItem {
  catalogId: string;
  quantity: number;
}

interface CreateSaleOrderEntriesCommand {
  companyId: string;
  assignedUserId: string;
  entries : {
    readId?: string;
    clientId: string;
    notes?: string | null;
    shippingAddress?: string | null;
    shippingPrice?: number;
    deliveryDate?: Date;
    orderItems: CreateSaleOrderEntriesOrderItem[];
  }[];
}

export default CreateSaleOrderEntriesCommand;
