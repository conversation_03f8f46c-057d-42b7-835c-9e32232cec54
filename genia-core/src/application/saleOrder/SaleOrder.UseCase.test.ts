import { v4 } from 'uuid';

import OrderUseCase from '#application/common/order/Order.UseCase';
import CalculateSaleOrderConditionsCommand from '#application/saleOrder/commands/CalculateSaleOrderConditions.Command';
import CreateSaleOrderEntriesCommand from '#application/saleOrder/commands/CreateSaleOrderEntriesCommand';
import Registry from '#composition/ImplementationRegistry';
import CatalogEntity, { CatalogType } from '#domain/aggregates/catalog/Catalog.Entity';
import ClientObject from '#domain/aggregates/client/Client.Entity';
import ProviderObject from '#domain/aggregates/provider/Provider.Entity';
import PurchaseOrderEntity, { PurchaseOrderStatus } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Entity';
import PurchaseOrderOperator from '#domain/aggregates/purchaseOrder/PurchaseOrder.Operator';
import SaleOrderEntity, { SaleOrderStatus } from '#domain/aggregates/saleOrder/SaleOrder.Entity';
import SaleOrderOperator from '#domain/aggregates/saleOrder/SaleOrder.Operator';
import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import OrderDomainService from '#domain/domainServices/Order.DomainService';
import OrderConditionsDomainService, { CalculateConditionsParams } from '#domain/domainServices/OrderConditions.DomainService';

import SaleOrderUseCase from './SaleOrder.UseCase';

jest.unmock('./SaleOrder.UseCase');

describe('SaleOrderUseCase', () => {
  const mockClient: ClientObject = {
    id: 'client-id',
    companyId: 'company-id',
    clientCompanyId: 'client-company-id',
    name: 'Test Client',
    createdAt: new Date(),
    updatedAt: new Date(),
  } as ClientObject;

  const mockClientWithoutCompany: ClientObject = {
    id: 'client-id',
    companyId: 'company-id',
    clientCompanyId: null,
    name: 'Test Client',
    createdAt: new Date(),
    updatedAt: new Date(),
  } as ClientObject;

  const mockProvider: ProviderObject = {
    id: 'provider-id',
    providerCompanyId: 'company-id',
    name: 'Test Provider',
    createdAt: new Date(),
    updatedAt: new Date(),
  } as unknown as ProviderObject;

  const mockCatalogItem: CatalogEntity = {
    id: 'catalog-item-1',
    price: 100,
    taxIds: ['tax-1'],
    companyId: 'company-id',
    readId: 'SKU-1',
    requiresStock: true,
    name: 'Test Item',
    attributes: [],
    createdAt: new Date(),
    description: 'Test Description',
    disabledAt: null,
    inventoryRelations: [],
    mediaIds: [],
    type: CatalogType.PRODUCT,
    updatedAt: new Date(),
    discountIds: [],
  };

  const mockTax = {
    id: 'tax-1',
    name: 'VAT',
    amount: 16,
    type: TaxType.PERCENTAGE,
    value: 14.4,
  };

  const mockStoreDiscount: StoreDiscountEntity = {
    id: 'discount-1',
    discountValue: 10,
    name: 'Store Discount',
    endDate: null,
    startDate: new Date(),
    discountType: DiscountType.PERCENTAGE,
    requiredAmount: 100,
    clientIds: ['client-id'],
    companyId: 'company-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    disabledAt: null,
  };

  const mockOrderItemCalculated = {
    catalogId: 'catalog-item-1',
    quantity: 2,
    unitPrice: mockCatalogItem.price,
    unitPriceAfterDiscount: 90,
    unitPriceAfterDiscountAndTaxes: 104.4,
    discounts: {
      appliedDiscount: {
        catalogDiscount: null,
        storeDiscount: {
          ...mockStoreDiscount,
          priceAfterDiscount: 90,
        },
      },
      applicableDiscounts: {
        catalogDiscounts: [],
        storeDiscounts: [{
          ...mockStoreDiscount,
          priceAfterDiscount: 90,
        }],
      },
    },
    taxes: [mockTax],
  };

  const mockCalculatedOrderConditionsParams: CalculateConditionsParams = {
    orderItems: [mockOrderItemCalculated],
    shippingPrice: 0,
  };

  const mockOrderConditions = {
    subtotal: 180,
    subtotalBeforeDiscount: 200,
    totalDiscount: 20,
    totalTaxes: 28.8,
    total: 208.8,
    taxes: [mockTax],
    orderItems: [
      {
        catalogId: 'catalog-item-1',
        quantity: 2,
        unitPrice: mockCatalogItem.price,
        unitPriceAfterDiscount: 90,
        unitPriceAfterDiscountAndTaxes: 104.4,
        subtotal: 180,
        total: 208.8,
        taxes: [mockTax],
      },
    ],
  };

  const defaultCommand: CalculateSaleOrderConditionsCommand = {
    companyId: 'company-id',
    clientId: 'client-id',
    orderItems: [
      {
        catalogId: 'catalog-item-1',
        quantity: 2,
      },
    ],
  };

  const itemUUID = 'itemUUID';

  beforeEach(() => {
    jest.restoreAllMocks();
    jest.resetAllMocks();
    (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue(mockClient);
    (Registry.ProviderRepository.findProviderForCompanyByProviderCompanyId as jest.Mock).mockResolvedValue(mockProvider);
    (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogItem]);
    (OrderUseCase.getCatalogConditions as jest.Mock).mockResolvedValue([mockOrderItemCalculated]);
    (OrderConditionsDomainService.calculateConditions as jest.Mock).mockReturnValue(mockOrderConditions);
    (v4 as jest.Mock).mockReturnValue(itemUUID);
  });

  describe('calculateSaleOrderConditions', () => {
    it('should throw error when orderItems is empty', async () => {
      const emptyCommand: CalculateSaleOrderConditionsCommand = {
        companyId: 'company-id',
        clientId: 'client-id',
        orderItems: [],
      };

      await expect(
        SaleOrderUseCase.calculateSaleOrderConditions(emptyCommand),
      ).rejects.toThrow('Order items are required');
    });

    it('should throw error when client not found', async () => {
      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue(null);

      await expect(
        SaleOrderUseCase.calculateSaleOrderConditions(defaultCommand),
      ).rejects.toThrow('Client not found');
    });

    it('should throw error when client does not belong to company', async () => {
      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue({
        ...mockClient,
        companyId: 'different-company-id',
      });

      await expect(
        SaleOrderUseCase.calculateSaleOrderConditions(defaultCommand),
      ).rejects.toThrow('Client does not belong to company');
    });

    it('should throw error when provider not found for client company', async () => {
      (Registry.ProviderRepository.findProviderForCompanyByProviderCompanyId as jest.Mock).mockResolvedValue(null);

      await expect(
        SaleOrderUseCase.calculateSaleOrderConditions(defaultCommand),
      ).rejects.toThrow('Company company-id is not a provider for client client-company-id');
    });

    it('should skip provider validation when client has no company', async () => {
      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue(mockClientWithoutCompany);

      const result = await SaleOrderUseCase.calculateSaleOrderConditions(defaultCommand);

      expect(Registry.ProviderRepository.findProviderForCompanyByProviderCompanyId).not.toHaveBeenCalled();
      expect(result).toEqual({
        ...mockOrderConditions,
        orderItems: [
          expect.objectContaining({
            catalogId: 'catalog-item-1',
            quantity: 2,
          }),
        ],
      });
    });

    it('should calculate order conditions successfully', async () => {
      const result = await SaleOrderUseCase.calculateSaleOrderConditions(defaultCommand);

      expect(Registry.ClientRepository.findOneById).toHaveBeenCalledWith('client-id');
      expect(Registry.ProviderRepository.findProviderForCompanyByProviderCompanyId).toHaveBeenCalledWith('client-company-id', 'company-id');
      expect(OrderUseCase.getCatalogConditions).toHaveBeenCalledWith(
        defaultCommand.orderItems,
        'company-id',
        mockClient,
      );
      expect(OrderConditionsDomainService.calculateConditions).toHaveBeenCalledWith(mockCalculatedOrderConditionsParams);

      expect(result).toEqual({
        ...mockOrderConditions,
        orderItems: [
          expect.objectContaining({
            catalogId: 'catalog-item-1',
            quantity: 2,
          }),
        ],
      });
    });
  });

  describe('createSaleOrderEntries', () => {
    const mockSaleOrder: SaleOrderEntity = {
      id: 'sale-order-id',
      readId: 'SO-001',
      companyId: 'company-id',
      clientId: 'client-id',
      assignedUserId: 'user-id',
      status: SaleOrderStatus.CLIENT_APPROVAL,
      subtotalBeforeDiscount: 200,
      totalDiscount: 20,
      subtotal: 180,
      totalTaxes: 28.8,
      total: 208.8,
      taxes: [mockTax],
      orderItems: [{
        catalogId: 'catalog-item-1',
        productId: 'SKU-1',
        name: 'Test Item',
        quantity: 2,
        unitPrice: 100,
        unitPriceAfterDiscount: 90,
        unitPriceAfterDiscountAndTaxes: 104.4,
        subtotal: 180,
        total: 208.8,
        discount: {
          value: 10,
          type: DiscountType.PERCENTAGE,
        },
        taxes: [mockTax],
        createdAt: new Date(),
        updatedAt: new Date(),
      }],
      createdAt: new Date(),
      updatedAt: new Date(),
      reviewStartedAt: null,
      shippedAt: null,
      shippingAddress: null,
      shippingPrice: 0,
      notes: 'Test notes',
      deliveryDate: new Date(),
      relatedPurchaseOrderId: null,
    };

    const mockPurchaseOrder: PurchaseOrderEntity = {
      id: 'purchase-order-id',
      readId: 'PO-001',
      companyId: 'client-company-id',
      providerId: 'provider-id',
      status: PurchaseOrderStatus.CLIENT_APPROVAL,
      subtotalBeforeDiscount: 200,
      totalDiscount: 20,
      subtotal: 180,
      totalTaxes: 28.8,
      total: 208.8,
      assignedUserId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      reviewStartedAt: null,
      shippedAt: null,
      shippingAddress: null,
      shippingPrice: 0,
      notes: null,
      taxes: [mockTax],
      deliveryDate: new Date(),
      orderItems: [
        {
          id: itemUUID,
          referenceId: 'catalog-item-1',
          productId: 'SKU-1',
          name: 'Test Item',
          quantity: 2,
          unitPrice: 100,
          unitPriceAfterDiscount: 90,
          unitPriceAfterDiscountAndTaxes: 104.4,
          subtotal: 180,
          total: 208.8,
          createdAt: new Date(),
          updatedAt: new Date(),
          inventoryIds: [],
          discount: {
            value: 10,
            type: DiscountType.PERCENTAGE,
          },
          taxes: [mockTax],
        },
      ],
      relatedSaleOrderId: 'sale-order-id',
    };

    const mockUpdatedSaleOrder = {
      ...mockSaleOrder,
      status: SaleOrderStatus.CLIENT_APPROVAL,
      relatedPurchaseOrderId: 'purchase-order-id',
    };

    const createCommand: CreateSaleOrderEntriesCommand = {
      companyId: 'company-id',
      assignedUserId: 'user-id',
      entries: [
        {
          clientId: 'client-id',
          deliveryDate: new Date(),
          notes: 'Test notes',
          orderItems: [
            {
              catalogId: 'catalog-item-1',
              quantity: 2,
            },
          ],
        },
      ],
    };

    // New test command with custom readId
    const createCommandWithReadId: CreateSaleOrderEntriesCommand = {
      companyId: 'company-id',
      assignedUserId: 'user-id',
      entries: [
        {
          clientId: 'client-id',
          deliveryDate: new Date(),
          notes: 'Test notes',
          readId: 'CUSTOM-SO-001', // Custom readId
          orderItems: [
            {
              catalogId: 'catalog-item-1',
              quantity: 2,
            },
          ],
        },
      ],
    };

    beforeEach(() => {
      jest.clearAllMocks();
      (Registry.IdentificationService.generateId as jest.Mock)
        .mockResolvedValueOnce('sale-order-id')
        .mockResolvedValueOnce('purchase-order-id');
      (Registry.IdentificationService.generateObjectSequenceForCompany as jest.Mock)
        .mockResolvedValueOnce('SO-001')
        .mockResolvedValueOnce('PO-001');
      (Registry.SaleOrderRepository.save as jest.Mock)
        .mockResolvedValueOnce([mockSaleOrder])
        .mockResolvedValueOnce([mockUpdatedSaleOrder]);
      (Registry.PurchaseOrderRepository.save as jest.Mock).mockResolvedValue([mockPurchaseOrder]);
      (Registry.TransactionService.transactional as jest.Mock).mockImplementation((fn) => fn());
      // Remove SaleOrderOperator.build mock since it's no longer called directly
      (PurchaseOrderOperator.build as jest.Mock).mockReturnValue(mockPurchaseOrder);

      // Add mock for inventory repository
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'inventory-1', stock: 10, restrictedStock: 0 },
      ]);

      // Add mock for OrderDomainService.createSaleOrder
      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        saleOrder: mockSaleOrder,
        inventory: [{ id: 'inventory-1', stock: 10, restrictedStock: 2 }],
      });
    });

    it('should create a sale order successfully', async () => {
      const result = await SaleOrderUseCase.createSaleOrderEntries(createCommand);

      expect(Registry.ClientRepository.findOneById).toHaveBeenCalledWith('client-id');
      expect(Registry.ProviderRepository.findProviderForCompanyByProviderCompanyId).toHaveBeenCalledWith('client-company-id', 'company-id');
      expect(Registry.CatalogRepository.findManyByIds).toHaveBeenCalledWith(['catalog-item-1']);

      // Verify inventory handling
      expect(Registry.InventoryRepository.findManyByIds).toHaveBeenCalled();

      // Verify OrderDomainService.createSaleOrder was called
      expect(OrderDomainService.createSaleOrder).toHaveBeenCalledWith({
        catalog: [expect.objectContaining({ id: 'catalog-item-1' })],
        inventory: [expect.objectContaining({ id: 'inventory-1' })],
        orderBuildParams: expect.objectContaining({
          id: 'sale-order-id',
          readId: 'SO-001',
          companyId: 'company-id',
          clientId: 'client-id',
          assignedUserId: 'user-id',
        }),
      });

      // Verify inventory was saved
      expect(Registry.InventoryRepository.save).toHaveBeenCalledWith([
        expect.objectContaining({ id: 'inventory-1' }),
      ]);

      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledWith([mockSaleOrder]);
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();

      // Verify purchase order creation when client has company ID
      expect(Registry.IdentificationService.generateId).toHaveBeenCalledTimes(2);
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).toHaveBeenCalledTimes(2);
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).toHaveBeenNthCalledWith(2, 'client-company-id', 'purchase_order');

      expect(PurchaseOrderOperator.build).toHaveBeenCalledWith(expect.objectContaining(mockPurchaseOrder));

      expect(Registry.PurchaseOrderRepository.save).toHaveBeenCalledWith([mockPurchaseOrder]);

      // Verify sale order was updated with purchase order ID
      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledTimes(2);
      expect(Registry.SaleOrderRepository.save).toHaveBeenNthCalledWith(2, [
        expect.objectContaining({
          id: 'sale-order-id',
          status: SaleOrderStatus.CLIENT_APPROVAL,
          relatedPurchaseOrderId: 'purchase-order-id',
        }),
      ]);

      expect(result).toEqual([mockUpdatedSaleOrder]);
    });

    it('should throw error when catalog item not found', async () => {
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([]);

      await expect(
        SaleOrderUseCase.createSaleOrderEntries(createCommand),
      ).rejects.toThrow('Catalog item catalog-item-1 not found');
    });

    it('should throw error when catalog item does not belong to company', async () => {
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([
        {
          ...mockCatalogItem,
          companyId: 'different-company-id',
        },
      ]);

      await expect(
        SaleOrderUseCase.createSaleOrderEntries(createCommand),
      ).rejects.toThrow('Catalog item does not belong to company');
    });

    it('should handle case when there is no applied discount', async () => {
      const orderItemCalculatedWithoutDiscount = {
        ...mockOrderItemCalculated,
        discounts: {
          appliedDiscount: {
            catalogDiscount: null,
            storeDiscount: null,
          },
          applicableDiscounts: {
            catalogDiscounts: [],
            storeDiscounts: [],
          },
        },
        unitPriceAfterDiscount: mockCatalogItem.price, // No discount applied
      };

      const orderConditionsWithoutDiscount = {
        ...mockOrderConditions,
        totalDiscount: 0,
        subtotalBeforeDiscount: 200,
        subtotal: 200,
        orderItems: [
          {
            ...mockOrderConditions.orderItems[0],
            unitPriceAfterDiscount: mockCatalogItem.price,
            subtotal: 200,
          },
        ],
      };

      (OrderUseCase.getCatalogConditions as jest.Mock).mockResolvedValue([orderItemCalculatedWithoutDiscount]);
      (OrderConditionsDomainService.calculateConditions as jest.Mock).mockReturnValue(orderConditionsWithoutDiscount);

      // Mock the domain service with the updated build params
      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        saleOrder: {
          ...mockSaleOrder,
          orderItems: [
            {
              ...mockSaleOrder.orderItems[0],
              discount: null,
            },
          ],
        },
        inventory: [{ id: 'inventory-1', stock: 10, restrictedStock: 2 }],
      });

      await SaleOrderUseCase.createSaleOrderEntries(createCommand);

      // Check that the build params passed to OrderDomainService.createSaleOrder have null discount
      expect(OrderDomainService.createSaleOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBuildParams: expect.objectContaining({
            orderItems: [
              expect.objectContaining({
                discount: null, // No discount should be applied
              }),
            ],
          }),
        }),
      );
    });

    it('should handle null client company ID', async () => {
      // Create a client with no company ID
      const noCompanyClient = {
        ...mockClient,
        clientCompanyId: null,
      };

      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValue(noCompanyClient);

      const result = await SaleOrderUseCase.calculateSaleOrderConditions(defaultCommand);

      // Verify provider wasn't looked up since there's no client company
      expect(Registry.ProviderRepository.findProviderForCompanyByProviderCompanyId).not.toHaveBeenCalled();

      // Rest of verification
      expect(result).toEqual({
        ...mockOrderConditions,
        orderItems: [
          expect.objectContaining({
            catalogId: 'catalog-item-1',
            quantity: 2,
          }),
        ],
      });
    });

    it('should handle amount-type discount correctly', async () => {
      // Create an amount discount instead of percentage
      const amountDiscount = {
        ...mockStoreDiscount,
        discountType: DiscountType.AMOUNT,
        discountValue: 20, // $20 flat discount
      };

      const orderItemWithAmountDiscount = {
        ...mockOrderItemCalculated,
        unitPriceAfterDiscount: 80, // 100 - 20
        discounts: {
          appliedDiscount: {
            catalogDiscount: null,
            storeDiscount: {
              ...amountDiscount,
              priceAfterDiscount: 80,
            },
          },
          applicableDiscounts: {
            catalogDiscounts: [],
            storeDiscounts: [{
              ...amountDiscount,
              priceAfterDiscount: 80,
            }],
          },
        },
      };

      const orderConditionsWithAmountDiscount = {
        ...mockOrderConditions,
        subtotal: 160, // 80 * 2
        totalDiscount: 40, // 20 * 2
        orderItems: [
          {
            ...mockOrderConditions.orderItems[0],
            unitPriceAfterDiscount: 80,
            subtotal: 160,
          },
        ],
      };

      (OrderUseCase.getCatalogConditions as jest.Mock).mockResolvedValue([orderItemWithAmountDiscount]);
      (OrderConditionsDomainService.calculateConditions as jest.Mock).mockReturnValue(orderConditionsWithAmountDiscount);

      // Mock OrderDomainService to return a saleOrder with AMOUNT discount
      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        saleOrder: {
          ...mockSaleOrder,
          orderItems: [
            {
              ...mockSaleOrder.orderItems[0],
              discount: {
                value: 20,
                type: DiscountType.AMOUNT,
              },
            },
          ],
        },
        inventory: [{ id: 'inventory-1', stock: 10, restrictedStock: 2 }],
      });

      await SaleOrderUseCase.createSaleOrderEntries(createCommand);

      // Verify that the discount type is set to AMOUNT in the build params
      expect(OrderDomainService.createSaleOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBuildParams: expect.objectContaining({
            orderItems: [
              expect.objectContaining({
                discount: {
                  value: 20,
                  type: DiscountType.AMOUNT,
                },
              }),
            ],
          }),
        }),
      );
    });

    it('should create a purchase order when client has a company ID', async () => {
      // Setup mocks for purchase order creation
      (Registry.IdentificationService.generateId as jest.Mock)
        .mockResolvedValueOnce('sale-order-id')
        .mockResolvedValueOnce('purchase-order-id');

      (Registry.IdentificationService.generateObjectSequenceForCompany as jest.Mock)
        .mockResolvedValueOnce('SO-001')
        .mockResolvedValueOnce('PO-001');

      (PurchaseOrderOperator.build as jest.Mock).mockReturnValue(mockPurchaseOrder);
      (Registry.PurchaseOrderRepository.save as jest.Mock).mockResolvedValue([mockPurchaseOrder]);
      (Registry.SaleOrderRepository.save as jest.Mock)
        .mockResolvedValueOnce([mockSaleOrder])
        .mockResolvedValueOnce([mockUpdatedSaleOrder]);

      const result = await SaleOrderUseCase.createSaleOrderEntries(createCommand);

      // Verify purchase order created
      expect(Registry.IdentificationService.generateObjectSequenceForCompany)
        .toHaveBeenCalledWith('client-company-id', 'purchase_order');
      expect(PurchaseOrderOperator.build).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'purchase-order-id',
          readId: 'PO-001',
          providerId: 'provider-id',
          status: PurchaseOrderStatus.CLIENT_APPROVAL,
          relatedSaleOrderId: 'sale-order-id',
          orderItems: expect.arrayContaining([
            expect.objectContaining({
              referenceId: 'catalog-item-1',
            }),
          ]),
        }),
      );
      expect(Registry.PurchaseOrderRepository.save).toHaveBeenCalledWith([mockPurchaseOrder]);

      // Verify sale order was updated with purchase order relation
      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledTimes(2);
      expect(Registry.SaleOrderRepository.save).toHaveBeenLastCalledWith([
        expect.objectContaining({
          id: 'sale-order-id',
          status: SaleOrderStatus.CLIENT_APPROVAL,
          relatedPurchaseOrderId: 'purchase-order-id',
        }),
      ]);

      // Verify final result has the updated sale order
      expect(result).toEqual([mockUpdatedSaleOrder]);
    });

    it('should handle case when there are no inventory items to update', async () => {
      // Mock the domain service to return an empty inventory array
      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        saleOrder: mockSaleOrder,
        inventory: [],
      });

      await SaleOrderUseCase.createSaleOrderEntries(createCommand);

      // Verify InventoryRepository.save was not called
      expect(Registry.InventoryRepository.save).not.toHaveBeenCalled();
    });

    it('should create a sale order with inventory tracking when catalog has inventory relations', async () => {
      // Setup a catalog with inventory relations
      const catalogWithInventory = {
        ...mockCatalogItem,
        inventoryRelations: [
          { inventoryId: 'inventory-1', quantity: 2 },
          { inventoryId: 'inventory-2', quantity: 1 },
        ],
      };

      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([catalogWithInventory]);

      // Setup inventory items
      const mockInventoryItems = [
        { id: 'inventory-1', stock: 100, restrictedStock: 0 },
        { id: 'inventory-2', stock: 50, restrictedStock: 0 },
      ];

      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValue(mockInventoryItems);

      // Mock updated inventory with restricted stock
      const updatedInventory = [
        { id: 'inventory-1', stock: 100, restrictedStock: 4 }, // 2 units × 2 quantity
        { id: 'inventory-2', stock: 50, restrictedStock: 2 }, // 2 units × 1 quantity
      ];

      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        saleOrder: mockSaleOrder,
        inventory: updatedInventory,
      });

      await SaleOrderUseCase.createSaleOrderEntries(createCommand);

      // Verify inventory IDs were collected from catalog relations
      expect(Registry.InventoryRepository.findManyByIds).toHaveBeenCalledWith(['inventory-1', 'inventory-2']);

      // Verify OrderDomainService was called with correct params
      expect(OrderDomainService.createSaleOrder).toHaveBeenCalledWith({
        catalog: [catalogWithInventory],
        inventory: mockInventoryItems,
        orderBuildParams: expect.any(Object),
      });

      // Verify updated inventory was saved
      expect(Registry.InventoryRepository.save).toHaveBeenCalledWith(updatedInventory);
    });

    it('should create a sale order with a custom readId when provided', async () => {
      // Setup custom sale order with custom readId
      const customSaleOrder = {
        ...mockSaleOrder,
        readId: 'CUSTOM-SO-001',
      };

      const customUpdatedSaleOrder = {
        ...mockUpdatedSaleOrder,
        readId: 'CUSTOM-SO-001',
      };

      // Update mocks for this test case
      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        saleOrder: customSaleOrder,
        inventory: [{ id: 'inventory-1', stock: 10, restrictedStock: 2 }],
      });

      (Registry.SaleOrderRepository.save as jest.Mock)
        .mockResolvedValueOnce([customSaleOrder])
        .mockResolvedValueOnce([customUpdatedSaleOrder]);

      await SaleOrderUseCase.createSaleOrderEntries(createCommandWithReadId);

      // Verify order domain service was called with custom readId
      expect(OrderDomainService.createSaleOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBuildParams: expect.objectContaining({
            readId: 'CUSTOM-SO-001',
          }),
        }),
      );

      // Verify generateObjectSequenceForCompany was NOT called since we provided a custom readId
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).not.toHaveBeenCalledWith('company-id', 'sale_order');
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).toHaveBeenCalledTimes(1); // Called only for purchase order

      // Verify sale order was saved with custom readId
      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledWith([customSaleOrder]);
    });

    it('should generate a readId when none is provided', async () => {
      const result = await SaleOrderUseCase.createSaleOrderEntries(createCommand);

      // Verify generateObjectSequenceForCompany was called to generate readId for sale order
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).toHaveBeenCalledWith('company-id', 'sale_order');

      // Verify order domain service was called with generated readId
      expect(OrderDomainService.createSaleOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBuildParams: expect.objectContaining({
            readId: 'SO-001',
          }),
        }),
      );

      // Verify result has the generated readId
      expect(result[0].readId).toBe('SO-001');
    });
  });

  describe('updateSaleOrderEntry', () => {
    const mockSaleOrder: SaleOrderEntity = {
      id: 'sale-order-id',
      readId: 'SO-001',
      companyId: 'company-id',
      clientId: 'client-id',
      assignedUserId: 'user-id',
      status: SaleOrderStatus.PENDING,
      subtotalBeforeDiscount: 200,
      totalDiscount: 20,
      subtotal: 180,
      totalTaxes: 28.8,
      total: 208.8,
      taxes: [mockTax],
      orderItems: [{
        catalogId: 'catalog-item-1',
        productId: 'SKU-1',
        name: 'Test Item',
        quantity: 2,
        unitPrice: 100,
        unitPriceAfterDiscount: 90,
        unitPriceAfterDiscountAndTaxes: 104.4,
        subtotal: 180,
        total: 208.8,
        discount: {
          value: 10,
          type: DiscountType.PERCENTAGE,
        },
        taxes: [mockTax],
        createdAt: new Date(),
        updatedAt: new Date(),
      }],
      createdAt: new Date(),
      updatedAt: new Date(),
      reviewStartedAt: null,
      shippedAt: null,
      shippingAddress: null,
      shippingPrice: 0,
      notes: 'Original notes',
      deliveryDate: new Date('2023-01-01'),
      relatedPurchaseOrderId: null,
    };

    const mockPurchaseOrder: PurchaseOrderEntity = {
      id: 'purchase-order-id',
      readId: 'PO-001',
      companyId: 'client-company-id',
      providerId: 'provider-id',
      status: PurchaseOrderStatus.PENDING,
      subtotalBeforeDiscount: 200,
      totalDiscount: 20,
      subtotal: 180,
      totalTaxes: 28.8,
      total: 208.8,
      assignedUserId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      reviewStartedAt: null,
      shippedAt: null,
      shippingAddress: null,
      shippingPrice: 0,
      notes: 'Original notes',
      taxes: [mockTax],
      deliveryDate: new Date('2023-01-01'),
      orderItems: [
        {
          id: 'poi-1',
          referenceId: 'catalog-item-1',
          productId: 'SKU-1',
          name: 'Test Item',
          quantity: 2,
          unitPrice: 100,
          unitPriceAfterDiscount: 90,
          unitPriceAfterDiscountAndTaxes: 104.4,
          subtotal: 180,
          total: 208.8,
          createdAt: new Date(),
          updatedAt: new Date(),
          inventoryIds: [],
          discount: {
            value: 10,
            type: DiscountType.PERCENTAGE,
          },
          taxes: [mockTax],
        },
      ],
      relatedSaleOrderId: 'sale-order-id',
    };

    const mockUpdatedSaleOrder = {
      ...mockSaleOrder,
      status: SaleOrderStatus.PROCESSING,
      notes: 'Updated notes',
      deliveryDate: new Date('2023-02-01'),
      shippingAddress: 'New shipping address',
    };

    const mockUpdatedPurchaseOrder = {
      ...mockPurchaseOrder,
      status: PurchaseOrderStatus.PROCESSING,
      deliveryDate: new Date('2023-02-01'),
      shippingAddress: 'New shipping address',
    };

    const updateCommand = {
      companyId: 'company-id',
      saleOrder: {
        id: 'sale-order-id',
        status: SaleOrderStatus.PROCESSING,
        notes: 'Updated notes',
        deliveryDate: new Date('2023-02-01'),
        shippingAddress: 'New shipping address',
        assignedUserId: 'new-user-id',
      },
    };

    beforeEach(() => {
      jest.clearAllMocks();
      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValue(mockSaleOrder);
      (Registry.PurchaseOrderRepository.findOneById as jest.Mock).mockResolvedValue(mockPurchaseOrder);
      (OrderUseCase.validateOrderPatch as jest.Mock).mockResolvedValue(true);
      (SaleOrderOperator.update as jest.Mock).mockReturnValue(mockUpdatedSaleOrder);
      (PurchaseOrderOperator.update as jest.Mock).mockReturnValue(mockUpdatedPurchaseOrder);
      (Registry.SaleOrderRepository.save as jest.Mock).mockResolvedValue([mockUpdatedSaleOrder]);
      (Registry.PurchaseOrderRepository.save as jest.Mock).mockResolvedValue([mockUpdatedPurchaseOrder]);
      (Registry.TransactionService.transactional as jest.Mock).mockImplementation((fn) => fn());

      // Add mocks for the new functionality
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([{
        ...mockCatalogItem,
        inventoryRelations: [{ inventoryId: 'inventory-1' }],
      }]);
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValue([{ id: 'inventory-1' }]);
      (OrderDomainService.updateSaleOrderStatus as jest.Mock).mockReturnValue({
        saleOrder: mockUpdatedSaleOrder,
        inventory: [],
      });
      (OrderDomainService.updatePurchaseOrderStatus as jest.Mock).mockReturnValue({
        purchaseOrder: mockUpdatedPurchaseOrder,
        inventory: [],
      });
    });

    it('should update a sale order successfully without changing status', async () => {
      const updateCommandWithoutStatus = {
        companyId: 'company-id',
        saleOrder: {
          id: 'sale-order-id',
          notes: 'Updated notes',
          deliveryDate: new Date('2023-02-01'),
          shippingAddress: 'New shipping address',
          assignedUserId: 'new-user-id',
        },
      };

      const result = await SaleOrderUseCase.updateSaleOrderEntry(updateCommandWithoutStatus);

      expect(Registry.SaleOrderRepository.findOneById).toHaveBeenCalledWith('sale-order-id');
      expect(OrderUseCase.validateOrderPatch).toHaveBeenCalledWith({
        companyId: 'company-id',
        entity: mockSaleOrder,
        params: updateCommandWithoutStatus.saleOrder,
      });

      // Verify catalog and inventory are NOT looked up when status isn't changing
      expect(Registry.CatalogRepository.findManyByIds).not.toHaveBeenCalled();
      expect(Registry.InventoryRepository.findManyByIds).not.toHaveBeenCalled();

      expect(SaleOrderOperator.update).toHaveBeenCalledWith(
        mockSaleOrder,
        {
          notes: 'Updated notes',
          deliveryDate: updateCommandWithoutStatus.saleOrder.deliveryDate,
          shippingAddress: 'New shipping address',
          assignedUserId: 'new-user-id',
        },
      );

      // Verify OrderDomainService was not called since status didn't change
      expect(OrderDomainService.updateSaleOrderStatus).not.toHaveBeenCalled();

      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledWith([mockUpdatedSaleOrder]);
      expect(result).toEqual(mockUpdatedSaleOrder);
    });

    it('should update a sale order with status change', async () => {
      const result = await SaleOrderUseCase.updateSaleOrderEntry(updateCommand);

      // Verify catalog and inventory lookup when status is changing
      expect(Registry.CatalogRepository.findManyByIds).toHaveBeenCalledWith(['catalog-item-1']);
      expect(Registry.InventoryRepository.findManyByIds).toHaveBeenCalledWith(['inventory-1']);

      // The update call should not include status
      expect(SaleOrderOperator.update).toHaveBeenCalledWith(
        mockSaleOrder,
        {
          notes: 'Updated notes',
          deliveryDate: updateCommand.saleOrder.deliveryDate,
          shippingAddress: 'New shipping address',
          assignedUserId: 'new-user-id',
        },
      );

      // Verify OrderDomainService was called to update status
      expect(OrderDomainService.updateSaleOrderStatus).toHaveBeenCalledWith({
        order: mockUpdatedSaleOrder,
        inventory: [{ id: 'inventory-1' }],
        catalog: [expect.objectContaining({ id: 'catalog-item-1' })],
        modifierCompanyId: 'company-id',
        status: SaleOrderStatus.PROCESSING,
      });

      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledWith([mockUpdatedSaleOrder]);
      expect(result).toEqual(mockUpdatedSaleOrder);
    });

    it('should update related purchase order when it exists without status change', async () => {
      // Set up mock sale order with related purchase order
      const saleOrderWithRelation = {
        ...mockSaleOrder,
        relatedPurchaseOrderId: 'purchase-order-id',
      };

      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValue(saleOrderWithRelation);

      const updateCommandWithoutStatus = {
        companyId: 'company-id',
        saleOrder: {
          id: 'sale-order-id',
          notes: 'Updated notes',
          deliveryDate: new Date('2023-02-01'),
          shippingAddress: 'New shipping address',
        },
      };

      await SaleOrderUseCase.updateSaleOrderEntry(updateCommandWithoutStatus);

      // Verify purchase order was fetched and updated without status change
      expect(Registry.PurchaseOrderRepository.findOneById).toHaveBeenCalledWith('purchase-order-id');
      expect(PurchaseOrderOperator.update).toHaveBeenCalledWith(
        mockPurchaseOrder,
        {
          deliveryDate: updateCommandWithoutStatus.saleOrder.deliveryDate,
          shippingAddress: 'New shipping address',
        },
        'company-id',
      );

      // Verify purchase order status update wasn't called
      expect(OrderDomainService.updatePurchaseOrderStatus).not.toHaveBeenCalled();

      expect(Registry.PurchaseOrderRepository.save).toHaveBeenCalledWith([mockUpdatedPurchaseOrder]);
    });

    it('should update related purchase order with status change', async () => {
      // Set up mock sale order with related purchase order
      const saleOrderWithRelation = {
        ...mockSaleOrder,
        relatedPurchaseOrderId: 'purchase-order-id',
      };

      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValue(saleOrderWithRelation);

      // Mock purchase order with inventory
      const mockPurchaseOrderWithInventory = {
        ...mockPurchaseOrder,
        orderItems: [
          {
            ...mockPurchaseOrder.orderItems[0],
            inventoryIds: ['po-inventory-1'],
          },
        ],
      };

      (Registry.PurchaseOrderRepository.findOneById as jest.Mock).mockResolvedValue(mockPurchaseOrderWithInventory);
      (Registry.InventoryRepository.findManyByIds as jest.Mock)
        .mockResolvedValueOnce([{ id: 'inventory-1' }]) // For sale order inventory
        .mockResolvedValueOnce([{ id: 'po-inventory-1' }]); // For purchase order inventory

      const updatedPurchaseOrderWithInventory = {
        ...mockUpdatedPurchaseOrder,
        orderItems: mockPurchaseOrderWithInventory.orderItems,
      };

      const updatedInventory = [{ id: 'po-inventory-1', quantity: 8 }];

      (OrderDomainService.updatePurchaseOrderStatus as jest.Mock).mockReturnValue({
        purchaseOrder: updatedPurchaseOrderWithInventory,
        inventory: updatedInventory,
      });

      await SaleOrderUseCase.updateSaleOrderEntry(updateCommand);

      // Verify purchase order inventory was looked up and updated
      expect(Registry.InventoryRepository.findManyByIds).toHaveBeenCalledWith(['po-inventory-1']);

      expect(OrderDomainService.updatePurchaseOrderStatus).toHaveBeenCalledWith({
        order: mockUpdatedPurchaseOrder,
        inventory: [{ id: 'po-inventory-1' }],
        modifierCompanyId: 'company-id',
        status: SaleOrderStatus.PROCESSING,
      });

      // Verify purchase order inventory was saved
      expect(Registry.InventoryRepository.save).toHaveBeenCalledWith(updatedInventory);
    });

    it('should not save purchase order inventory when no inventory updates are needed', async () => {
      // Set up mock sale order with related purchase order
      const saleOrderWithRelation = {
        ...mockSaleOrder,
        relatedPurchaseOrderId: 'purchase-order-id',
      };

      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValue(saleOrderWithRelation);
      (OrderDomainService.updatePurchaseOrderStatus as jest.Mock).mockReturnValue({
        purchaseOrder: mockUpdatedPurchaseOrder,
        inventory: [], // Empty inventory updates
      });

      await SaleOrderUseCase.updateSaleOrderEntry(updateCommand);

      // Initial inventory save from sale order update might be called if that has inventory updates
      // But we should check that we don't make an extra save call for purchase order inventory
      expect(Registry.InventoryRepository.save).toHaveBeenCalledTimes(
        (OrderDomainService.updateSaleOrderStatus as jest.Mock).mock.results[0].value.inventory.length ? 1 : 0,
      );
    });

    it('should throw error when related purchase order not found', async () => {
      // Set up mock sale order with related purchase order id that doesn't exist
      const saleOrderWithRelation = {
        ...mockSaleOrder,
        relatedPurchaseOrderId: 'purchase-order-id',
      };

      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValue(saleOrderWithRelation);
      (Registry.PurchaseOrderRepository.findOneById as jest.Mock).mockResolvedValue(null);

      await expect(
        SaleOrderUseCase.updateSaleOrderEntry(updateCommand),
      ).rejects.toThrow('Related purchase order not found');
    });

    it('should throw error when order validation fails', async () => {
      (OrderUseCase.validateOrderPatch as jest.Mock).mockRejectedValue(
        new Error('Invalid status transition'),
      );

      await expect(
        SaleOrderUseCase.updateSaleOrderEntry(updateCommand),
      ).rejects.toThrow('Invalid status transition');
    });

    it('should use existing assignedUserId when not provided in the update', async () => {
      const commandWithoutAssignedUser = {
        companyId: 'company-id',
        saleOrder: {
          id: 'sale-order-id',
          status: SaleOrderStatus.PROCESSING,
          notes: 'Updated notes',
          deliveryDate: new Date('2023-02-01'),
          shippingAddress: 'New shipping address',
        },
      };

      await SaleOrderUseCase.updateSaleOrderEntry(commandWithoutAssignedUser);

      // Verify that update was called with the existing assignedUserId but without status
      expect(SaleOrderOperator.update).toHaveBeenCalledWith(
        mockSaleOrder,
        {
          notes: 'Updated notes',
          deliveryDate: commandWithoutAssignedUser.saleOrder.deliveryDate,
          shippingAddress: 'New shipping address',
          assignedUserId: 'user-id', // Should use the existing value
        },
      );

      // Verify status was updated through OrderDomainService
      expect(OrderDomainService.updateSaleOrderStatus).toHaveBeenCalledWith(expect.objectContaining({
        status: SaleOrderStatus.PROCESSING,
      }));
    });

    it('should save inventory when updating sale order status returns updated inventory', async () => {
      // Mock inventory updates returned by updateSaleOrderStatus
      const updatedInventory = [
        { id: 'inventory-1', stock: 8, restrictedStock: 0 }, // Stock decreased from 10 to 8
      ];

      (OrderDomainService.updateSaleOrderStatus as jest.Mock).mockReturnValue({
        saleOrder: mockUpdatedSaleOrder,
        inventory: updatedInventory, // Return non-empty inventory array
      });

      const result = await SaleOrderUseCase.updateSaleOrderEntry(updateCommand);

      // Verify that the updated inventory was saved
      expect(Registry.InventoryRepository.save).toHaveBeenCalledWith(updatedInventory);

      // Verify sale order was saved and returned correctly
      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledWith([mockUpdatedSaleOrder]);
      expect(result).toEqual(mockUpdatedSaleOrder);
    });

    it('should handle both sale order and purchase order inventory updates', async () => {
      // Set up mock sale order with related purchase order
      const saleOrderWithRelation = {
        ...mockSaleOrder,
        relatedPurchaseOrderId: 'purchase-order-id',
      };

      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValue(saleOrderWithRelation);

      // Sale order inventory updates
      const updatedSaleOrderInventory = [
        { id: 'inventory-1', stock: 8, restrictedStock: 0 },
      ];

      // Purchase order inventory updates
      const updatedPurchaseOrderInventory = [
        { id: 'po-inventory-1', quantity: 12 },
      ];

      // Mock purchase order with inventory
      const mockPurchaseOrderWithInventory = {
        ...mockPurchaseOrder,
        orderItems: [
          {
            ...mockPurchaseOrder.orderItems[0],
            inventoryId: 'po-inventory-1',
          },
        ],
      };

      (Registry.PurchaseOrderRepository.findOneById as jest.Mock).mockResolvedValue(mockPurchaseOrderWithInventory);
      (Registry.InventoryRepository.findManyByIds as jest.Mock)
        .mockResolvedValueOnce([{ id: 'inventory-1' }]) // For sale order inventory
        .mockResolvedValueOnce([{ id: 'po-inventory-1' }]); // For purchase order inventory

      // Both domain service methods will return inventory updates
      (OrderDomainService.updateSaleOrderStatus as jest.Mock).mockReturnValue({
        saleOrder: mockUpdatedSaleOrder,
        inventory: updatedSaleOrderInventory,
      });

      (OrderDomainService.updatePurchaseOrderStatus as jest.Mock).mockReturnValue({
        purchaseOrder: mockUpdatedPurchaseOrder,
        inventory: updatedPurchaseOrderInventory,
      });

      await SaleOrderUseCase.updateSaleOrderEntry(updateCommand);

      // Verify both inventory updates were saved
      expect(Registry.InventoryRepository.save).toHaveBeenCalledTimes(2);
      expect(Registry.InventoryRepository.save).toHaveBeenNthCalledWith(1, updatedSaleOrderInventory);
      expect(Registry.InventoryRepository.save).toHaveBeenNthCalledWith(2, updatedPurchaseOrderInventory);
    });

    it('should handle completing a sale order with inventory deduction', async () => {
      // Prepare a command to complete the order
      const completeOrderCommand = {
        companyId: 'company-id',
        saleOrder: {
          id: 'sale-order-id',
          status: SaleOrderStatus.COMPLETED,
          notes: 'Order completed',
        },
      };

      // Mock catalog with inventory relations
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([{
        ...mockCatalogItem,
        inventoryRelations: [
          { inventoryId: 'inventory-1', quantity: 1 },
        ],
      }]);

      // Mock inventory available
      const mockInventoryWithStock = { id: 'inventory-1', stock: 10, restrictedStock: 4 };
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValue([mockInventoryWithStock]);

      // Mock inventory after completion - stock decreased and restrictedStock reset
      const completedInventory = [
        { id: 'inventory-1', stock: 8, restrictedStock: 2 }, // Stock decreased by 2, restricted reduced by 2
      ];

      // Mock domain service to return updated inventory
      (OrderDomainService.updateSaleOrderStatus as jest.Mock).mockReturnValue({
        saleOrder: {
          ...mockUpdatedSaleOrder,
          status: SaleOrderStatus.COMPLETED,
        },
        inventory: completedInventory,
      });

      await SaleOrderUseCase.updateSaleOrderEntry(completeOrderCommand);

      // Verify stock was updated when order was completed
      expect(OrderDomainService.updateSaleOrderStatus).toHaveBeenCalledWith(expect.objectContaining({
        status: SaleOrderStatus.COMPLETED,
        catalog: expect.arrayContaining([
          expect.objectContaining({ inventoryRelations: expect.arrayContaining([{ inventoryId: 'inventory-1', quantity: 1 }]) }),
        ]),
        inventory: [mockInventoryWithStock],
      }));

      // Verify updated inventory was saved
      expect(Registry.InventoryRepository.save).toHaveBeenCalledWith(completedInventory);
    });
  });

  describe('getAvailableStatuses', () => {
    const mockSaleOrder: SaleOrderEntity = {
      id: 'sale-order-id',
      readId: 'SO-001',
      companyId: 'company-id',
      clientId: 'client-id',
      assignedUserId: 'user-id',
      status: SaleOrderStatus.PENDING,
      subtotalBeforeDiscount: 200,
      totalDiscount: 20,
      subtotal: 180,
      totalTaxes: 28.8,
      total: 208.8,
      taxes: [mockTax],
      orderItems: [{
        catalogId: 'catalog-item-1',
        productId: 'SKU-1',
        name: 'Test Item',
        quantity: 2,
        unitPrice: 100,
        unitPriceAfterDiscount: 90,
        unitPriceAfterDiscountAndTaxes: 104.4,
        subtotal: 180,
        total: 208.8,
        discount: {
          value: 10,
          type: DiscountType.PERCENTAGE,
        },
        taxes: [mockTax],
        createdAt: new Date(),
        updatedAt: new Date(),
      }],
      createdAt: new Date(),
      updatedAt: new Date(),
      reviewStartedAt: null,
      shippedAt: null,
      shippingAddress: null,
      shippingPrice: 0,
      notes: 'Original notes',
      deliveryDate: new Date('2023-01-01'),
      relatedPurchaseOrderId: null,
    };

    const availableStatuses = [
      SaleOrderStatus.PROCESSING,
      SaleOrderStatus.CANCELLED,
    ];

    const command = {
      companyId: 'company-id',
      saleOrderId: 'sale-order-id',
    };

    beforeEach(() => {
      jest.clearAllMocks();
      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValue(mockSaleOrder);
      (SaleOrderOperator.availableStatuses as jest.Mock).mockReturnValue(availableStatuses);
    });

    it('should return available statuses for a valid sale order', async () => {
      const result = await SaleOrderUseCase.getAvailableStatuses(command);

      expect(Registry.SaleOrderRepository.findOneById).toHaveBeenCalledWith('sale-order-id');
      expect(SaleOrderOperator.availableStatuses).toHaveBeenCalledWith(mockSaleOrder);
      expect(result).toEqual(availableStatuses);
    });

    it('should throw error when sale order not found', async () => {
      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValue(null);

      await expect(
        SaleOrderUseCase.getAvailableStatuses(command),
      ).rejects.toThrow('Sale order not found');
    });

    it('should throw error when sale order does not belong to company', async () => {
      const differentCompanyCommand = {
        companyId: 'different-company-id',
        saleOrderId: 'sale-order-id',
      };

      await expect(
        SaleOrderUseCase.getAvailableStatuses(differentCompanyCommand),
      ).rejects.toThrow('Sale order does not belong to company');
    });
  });
});
