import { OrderItemCalculated } from '#application/common/order/results/CalculateOrderConditions.Result';
import { TaxType } from '#domain/aggregates/tax/Tax.Entity';

export interface OrderItemCalculatedWithTotals extends OrderItemCalculated {
  total: number;
  subtotal: number;
}

export default interface CalculateSaleOrderConditionsResult {
  total: number,
  subtotal: number,
  subtotalBeforeDiscount: number,
  totalDiscount: number,
  totalTaxes: number,
  shippingPrice: number,
  hasTaxOnShipping: boolean,
  taxes: {
    id: string;
    name: string;
    amount: number;
    type: TaxType;
    value: number;
  }[];
  orderItems: OrderItemCalculatedWithTotals[],
}
