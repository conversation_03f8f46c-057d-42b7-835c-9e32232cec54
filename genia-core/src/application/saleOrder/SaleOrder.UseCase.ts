import { v4 } from 'uuid';

import OrderUseCase from '#application/common/order/Order.UseCase';
import CalculateSaleOrderConditionsCommand, { CalculateSaleOrderConditionsOrderItem } from '#application/saleOrder/commands/CalculateSaleOrderConditions.Command';
import CreateSaleOrderEntriesCommand from '#application/saleOrder/commands/CreateSaleOrderEntriesCommand';
import { findSaleOrderAvailableStatusesCommand } from '#application/saleOrder/commands/FindSaleOrderAvailableStatuses.Command';
import UpdateSaleOrderEntryCommand from '#application/saleOrder/commands/UpdateSaleOrderEntry.command';
import CalculateSaleOrderConditionsResult from '#application/saleOrder/results/CalculateSaleOrderConditions.Result';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import CatalogEntity from '#domain/aggregates/catalog/Catalog.Entity';
import ClientEntity from '#domain/aggregates/client/Client.Entity';
import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import PurchaseOrderEntity, { PurchaseOrderStatus } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Entity';
import PurchaseOrderOperator from '#domain/aggregates/purchaseOrder/PurchaseOrder.Operator';
import SaleOrderEntity, { SaleOrderStatus } from '#domain/aggregates/saleOrder/SaleOrder.Entity';
import SaleOrderOperator, { SaleOrderBuildParams, SaleOrderItemBuildParams } from '#domain/aggregates/saleOrder/SaleOrder.Operator';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import OrderDomainService from '#domain/domainServices/Order.DomainService';
import OrderConditionsDomainService from '#domain/domainServices/OrderConditions.DomainService';

async function validateAndGetClientProvider(clientCompanyId: string, providerCompanyId: string): Promise<ProviderEntity> {
  const provider = await Registry.ProviderRepository.findProviderForCompanyByProviderCompanyId(clientCompanyId, providerCompanyId);

  if (!provider) throw new Error(`Company ${providerCompanyId} is not a provider for client ${clientCompanyId}`, { cause: Cause.NOT_FOUND });

  return provider;
}

async function validateAndGetClient(clientId: string, companyId: string): Promise<ClientEntity> {
  const client = await Registry.ClientRepository.findOneById(clientId);

  if (!client) throw new Error('Client not found', { cause: Cause.BAD_REQUEST });

  if (client.companyId !== companyId) {
    throw new Error('Client does not belong to company', { cause: Cause.BAD_REQUEST });
  }

  return client;
}

async function getConditionsForClient(
  client: ClientEntity,
  companyId: string,
  orderItems: CalculateSaleOrderConditionsOrderItem[],
  shippingPrice: number,
): Promise<CalculateSaleOrderConditionsResult> {
  const catalogConditions = await OrderUseCase.getCatalogConditions(orderItems, companyId, client);

  const orderConditions = OrderConditionsDomainService.calculateConditions({
    orderItems: catalogConditions,
    shippingPrice,
  });

  return {
    ...orderConditions,
    orderItems: catalogConditions.map((item, i) => ({
      ...item,
      ...orderConditions.orderItems[i],
    })),
  };
}

async function calculateSaleOrderConditions(command: CalculateSaleOrderConditionsCommand): Promise<CalculateSaleOrderConditionsResult> {
  const {
    companyId, clientId, orderItems, shippingPrice = 0,
  } = command;

  if (orderItems.length === 0) {
    throw new Error('Order items are required', { cause: Cause.BAD_REQUEST });
  }

  const client = await validateAndGetClient(clientId, companyId);

  if (client.clientCompanyId) {
    await validateAndGetClientProvider(client.clientCompanyId, companyId);
  }

  return getConditionsForClient(
    client,
    companyId,
    orderItems,
    shippingPrice,
  );
}

async function createSaleOrderEntries(command: CreateSaleOrderEntriesCommand): Promise<SaleOrderEntity[]> {
  const { entries, companyId, assignedUserId } = command;

  const entities = await Promise.all(entries.map(async (entry) => {
    const {
      orderItems, clientId, deliveryDate, notes, shippingAddress, shippingPrice, readId,
    } = entry;

    const client = await validateAndGetClient(clientId, companyId);
    const provider = client.clientCompanyId ? await validateAndGetClientProvider(client.clientCompanyId, companyId) : null;

    const conditions = await getConditionsForClient(
      client,
      companyId,
      orderItems,
      shippingPrice || 0,
    );

    const catalog = await Registry.CatalogRepository.findManyByIds(orderItems.map((item) => item.catalogId));
    const catalogMap = new Map(catalog.map((item) => [item.id, item]));

    orderItems.forEach((item) => {
      const catalogItem = catalogMap.get(item.catalogId);

      if (!catalogItem) {
        throw new Error(`Catalog item ${item.catalogId} not found`, { cause: Cause.NOT_FOUND });
      }

      if (catalogItem.companyId !== companyId) {
        throw new Error('Catalog item does not belong to company', { cause: Cause.BAD_REQUEST });
      }
    });

    const inventoryIds = new Set(catalog.flatMap((item) => item.inventoryRelations).map((relation) => relation.inventoryId));
    const inventories = await Registry.InventoryRepository.findManyByIds(Array.from(inventoryIds));

    return Registry.TransactionService.transactional(async () => {
      const id = await Registry.IdentificationService.generateId();
      const newReadId = readId || await Registry.IdentificationService.generateObjectSequenceForCompany(companyId, 'sale_order');

      const items: SaleOrderItemBuildParams[] = conditions.orderItems.map((item) => {
        const appliedDiscount = item.discounts?.appliedDiscount.catalogDiscount || item.discounts?.appliedDiscount.storeDiscount;
        const discount = appliedDiscount ? {
          value: appliedDiscount.discountValue,
          type: appliedDiscount.discountType === DiscountType.PERCENTAGE ? DiscountType.PERCENTAGE : DiscountType.AMOUNT,
        } : null;

        return {
          id: v4(),
          catalogId: item.catalogId,
          productId: (catalogMap.get(item.catalogId) as CatalogEntity).readId,
          discount,
          name: (catalogMap.get(item.catalogId) as CatalogEntity).name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          unitPriceAfterDiscount: item.unitPriceAfterDiscount,
          unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscountAndTaxes,
          subtotal: item.subtotal,
          total: item.total,
          taxes: item.taxes,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      });

      const buildParams: SaleOrderBuildParams = {
        id,
        readId: newReadId,
        companyId,
        clientId,
        assignedUserId,
        shippingAddress,
        status: SaleOrderStatus.PENDING,
        subtotalBeforeDiscount: conditions.subtotalBeforeDiscount,
        totalDiscount: conditions.totalDiscount,
        subtotal: conditions.subtotal,
        totalTaxes: conditions.totalTaxes,
        shippingPrice,
        total: conditions.total,
        taxes: conditions.taxes,
        orderItems: items,
        deliveryDate,
        notes,
        createdAt: new Date(),
      };

      const { saleOrder: newSaleOrder, inventory } = OrderDomainService.createSaleOrder(
        {
          catalog,
          inventory: inventories,
          orderBuildParams: buildParams,
        },
      );

      let [result] = await Registry.SaleOrderRepository.save([newSaleOrder]);

      if (inventory.length) await Registry.InventoryRepository.save(inventory);

      if (client.clientCompanyId && provider) {
        const purchaseOrderId = await Registry.IdentificationService.generateId();
        const purchaseOrderReadId = await Registry.IdentificationService.generateObjectSequenceForCompany(client.clientCompanyId, 'purchase_order');

        const purchaseOrder: PurchaseOrderEntity = PurchaseOrderOperator.build({
          ...result,
          id: purchaseOrderId,
          readId: purchaseOrderReadId,
          providerId: provider.id,
          companyId: client.clientCompanyId,
          notes: null,
          assignedUserId: null,
          status: PurchaseOrderStatus.CLIENT_APPROVAL,
          orderItems: items.map(({ catalogId, ...item }) => ({
            ...item,
            id: v4(),
            referenceId: catalogId,
            inventoryIds: [],
          })),
          relatedSaleOrderId: result.id,
        });

        await Registry.PurchaseOrderRepository.save([purchaseOrder]);

        result = {
          ...result,
          status: SaleOrderStatus.CLIENT_APPROVAL,
          relatedPurchaseOrderId: purchaseOrderId,
        };

        await Registry.SaleOrderRepository.save([result]);
      }

      return result;
    }).catch((error) => {
      throw error;
    });
  }));

  return entities as unknown as SaleOrderEntity[];
}

async function updateSaleOrderEntry(command: UpdateSaleOrderEntryCommand): Promise<SaleOrderEntity> {
  const {
    companyId, saleOrder: {
      id, deliveryDate, notes, shippingAddress, status, assignedUserId,
    },
  } = command;

  const saleOrderEntity = await Registry.SaleOrderRepository.findOneById(id) as SaleOrderEntity;

  await OrderUseCase.validateOrderPatch({
    companyId,
    entity: saleOrderEntity,
    params: {
      id, deliveryDate, notes, shippingAddress, status, assignedUserId,
    },
  });

  return Registry.TransactionService.transactional(async () => {
    let updatedSaleOrder = SaleOrderOperator.update(saleOrderEntity, {
      deliveryDate,
      shippingAddress,
      notes,
      assignedUserId: assignedUserId || saleOrderEntity.assignedUserId,
    });

    if (status) {
      const catalog = await Registry.CatalogRepository.findManyByIds(saleOrderEntity.orderItems.map((item) => item.catalogId));
      const inventoryIds = catalog.flatMap((item) => item.inventoryRelations).map((relation) => relation.inventoryId);
      const inventories = await Registry.InventoryRepository.findManyByIds(inventoryIds);

      const updatedOrderAndInventory = OrderDomainService.updateSaleOrderStatus({
        order: updatedSaleOrder,
        inventory: inventories,
        catalog,
        modifierCompanyId: companyId,
        status,
      });

      updatedSaleOrder = updatedOrderAndInventory.saleOrder;

      if (updatedOrderAndInventory.inventory.length) await Registry.InventoryRepository.save(updatedOrderAndInventory.inventory);
    }

    if (saleOrderEntity.relatedPurchaseOrderId) {
      const purchaseOrder = await Registry.PurchaseOrderRepository.findOneById(saleOrderEntity.relatedPurchaseOrderId);

      if (!purchaseOrder) throw new Error('Related purchase order not found', { cause: Cause.NOT_FOUND });

      let updatedPurchaseOrder = PurchaseOrderOperator.update(purchaseOrder, {
        deliveryDate,
        shippingAddress,
      }, companyId);

      if (status) {
        const purchaseOrderInventoryIds = purchaseOrder.orderItems.flatMap((item) => item.inventoryIds);
        const purchaseOrderInventories = await Registry.InventoryRepository.findManyByIds(purchaseOrderInventoryIds);

        const { purchaseOrder: purchaseOrderWithStatus, inventory: updatedInventory } = OrderDomainService.updatePurchaseOrderStatus({
          order: updatedPurchaseOrder,
          inventory: purchaseOrderInventories,
          modifierCompanyId: companyId,
          status,
        });

        updatedPurchaseOrder = purchaseOrderWithStatus;

        if (updatedInventory.length) await Registry.InventoryRepository.save(updatedInventory);
      }

      await Registry.PurchaseOrderRepository.save([updatedPurchaseOrder]);
    }

    const [updatedSaleOrderEntity] = await Registry.SaleOrderRepository.save([updatedSaleOrder]);

    return updatedSaleOrderEntity;
  }).catch((error) => {
    throw error;
  });
}

async function getAvailableStatuses(command: findSaleOrderAvailableStatusesCommand): Promise<PurchaseOrderStatus[]> {
  const { companyId, saleOrderId } = command;

  const saleOrder = await Registry.SaleOrderRepository.findOneById(saleOrderId);

  if (!saleOrder) throw new Error('Sale order not found', { cause: Cause.NOT_FOUND });

  if (saleOrder.companyId !== companyId) throw new Error('Sale order does not belong to company', { cause: Cause.FORBIDDEN });

  return SaleOrderOperator.availableStatuses(saleOrder);
}

export default {
  calculateSaleOrderConditions,
  createSaleOrderEntries,
  updateSaleOrderEntry,
  getAvailableStatuses,
};
