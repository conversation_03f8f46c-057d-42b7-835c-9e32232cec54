import EnvConfiguration from './Env.configuration';

jest.unmock('./Env.configuration');
jest.unmock('assert');

describe('EnvConfiguration', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  const requiredEnvVars = [
    'POSTGRES_DATABASE', 'POSTGRES_HOST',
    'POSTGRES_PASSWORD', 'POSTGRES_PORT', 'POSTGRES_USER', 'LOG_LEVEL',
    'ALLOWED_ORIGINS', 'HASURA_DEFAULT_ROLE', 'API_KEY', 'AUTH0_AUDIENCE',
    'AUTH0_DOMAIN', 'AUTH0_ALGORITHM', 'CLAIMS_NAMESPACE', 'STORE_COMPANY_ID',
    'GCS_PRIVATE_BUCKET_NAME', 'GCS_PUBLIC_BUCKET_NAME', 'SENDER_EMAIL',
    'SENDER_EMAIL_APP_PASSWORD', 'SUPLIFAI_WEB_URL', 'WHATSAPP_GRAPH_API_TOKEN',
    'SENDER_PHONE_NUMBER', 'GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET',
    'GOOGLE_REDIRECT_URI', 'ENCRYPTION_KEY', 'BIIZTRAL_WEB_URL',
  ];

  describe('Required environment variables', () => {
    test.each(requiredEnvVars)('%s is required', (varName) => {
      delete process.env[varName];
      expect(() => EnvConfiguration.getEnv(true)).toThrow();
    });
  });

  describe('Environment variable parsing', () => {
    beforeEach(() => {
      process.env = {
        ...process.env,
        POSTGRES_DATABASE: 'database',
        POSTGRES_HOST: 'host',
        POSTGRES_PASSWORD: 'password',
        POSTGRES_PORT: '5432',
        POSTGRES_USER: 'user',
        PORT: '3000',
        LOG_LEVEL: 'info',
        NODE_ENV: 'development',
        SWAGGER_PORT: '3001',
        ALLOWED_ORIGINS: 'http://localhost:3001',
        HASURA_DEFAULT_ROLE: 'read_user',
        API_KEY: 'api_key',
        AUTH0_AUDIENCE: 'audience',
        AUTH0_DOMAIN: 'domain',
        AUTH0_ALGORITHM: 'algorithm',
        CLAIMS_NAMESPACE: 'namespace',
        STORE_COMPANY_ID: 'storeCompanyId',
        GCS_PRIVATE_BUCKET_NAME: 'gcsPrivateBucketName',
        GCS_PUBLIC_BUCKET_NAME: 'gcsPublicBucketName',
        SENDER_EMAIL: '<EMAIL>',
        SENDER_EMAIL_APP_PASSWORD: 'app_password',
        BIIZTRAL_WEB_URL: 'http://localhost:3000',
        WHATSAPP_GRAPH_API_TOKEN: 'token',
        SENDER_PHONE_NUMBER: '*********',
        BIIZTRAL_LOGO_URL: 'www.biiztral.com/logo',
        GOOGLE_CLIENT_ID: 'google_client_id',
        GOOGLE_CLIENT_SECRET: 'google_client_secret',
        GOOGLE_REDIRECT_URI: 'http://localhost:3000/auth/google/callback',
        ENCRYPTION_KEY: 'encryption_key_32_characters_long',
      };
    });

    it('correctly parses environment variables', () => {
      const env = EnvConfiguration.getEnv();
      expect(env).toEqual(expect.objectContaining({
        POSTGRES_DATABASE: 'database',
        POSTGRES_HOST: 'host',
        POSTGRES_PASSWORD: 'password',
        POSTGRES_PORT: 5432,
        POSTGRES_USER: 'user',
        PORT: 3000,
        LOG_LEVEL: 'info',
        IS_DEV: true,
        SWAGGER_PORT: 3001,
        ALLOWED_ORIGINS: ['http://localhost:3001'],
        HASURA_DEFAULT_ROLE: 'read_user',
        API_KEY: 'api_key',
        AUTH0_AUDIENCE: 'audience',
        AUTH0_DOMAIN: 'domain',
        AUTH0_ALGORITHM: 'algorithm',
        CLAIMS_NAMESPACE: 'namespace',
        STORE_COMPANY_ID: 'storeCompanyId',
        GCS_PRIVATE_BUCKET_NAME: 'gcsPrivateBucketName',
        GCS_PUBLIC_BUCKET_NAME: 'gcsPublicBucketName',
        SENDER_EMAIL: '<EMAIL>',
        SENDER_EMAIL_APP_PASSWORD: 'app_password',
        BIIZTRAL_WEB_URL: 'http://localhost:3000',
        WHATSAPP_GRAPH_API_TOKEN: 'token',
        SENDER_PHONE_NUMBER: '*********',
        BIIZTRAL_LOGO_URL: 'www.biiztral.com/logo',
        GOOGLE_CLIENT_ID: 'google_client_id',
        GOOGLE_CLIENT_SECRET: 'google_client_secret',
        GOOGLE_REDIRECT_URI: 'http://localhost:3000/auth/google/callback',
        ENCRYPTION_KEY: 'encryption_key_32_characters_long',
      }));
    });

    it('should handle defaults', () => {
      delete process.env.PORT;
      delete process.env.SWAGGER_PORT;

      const env = EnvConfiguration.getEnv(true);
      expect(env).toEqual(expect.objectContaining({
        PORT: 3000,
        SWAGGER_PORT: 3001,
      }));
    });

    it('should throw an error if LOG_LEVEL is not one of the allowed values', () => {
      process.env.LOG_LEVEL = 'invalid';
      expect(() => EnvConfiguration.getEnv(true)).toThrow('LOG_LEVEL must be one of fatal, error, warn, info, debug, trace');
      process.env.LOG_LEVEL = '';
      expect(() => EnvConfiguration.getEnv(true)).toThrow('LOG_LEVEL must be one of fatal, error, warn, info, debug, trace');
    });
  });

  describe('Singleton behavior', () => {
    test('returns the same instance on subsequent calls', () => {
      const firstCall = EnvConfiguration.getEnv();
      const secondCall = EnvConfiguration.getEnv();
      expect(firstCall).toBe(secondCall);
    });
  });
});
