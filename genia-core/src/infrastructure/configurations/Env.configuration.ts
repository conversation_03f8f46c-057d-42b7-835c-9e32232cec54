import assert from 'assert';

import dotenv from 'dotenv';
import dotenvExpand from 'dotenv-expand';

type LogLevel = 'fatal' | 'error' | 'warn' | 'info' | 'debug' | 'trace';

export interface Environment {
  LOG_LEVEL: LogLevel;
  IS_DEV: boolean,
  PORT: number;
  POSTGRES_DATABASE: string;
  POSTGRES_HOST: string;
  POSTGRES_PASSWORD: string;
  POSTGRES_PORT: number;
  POSTGRES_USER: string;
  SWAGGER_PORT: number;
  ALLOWED_ORIGINS: string[];
  HASURA_DEFAULT_ROLE: string;
  API_KEY: string;
  AUTH0_AUDIENCE: string;
  AUTH0_DOMAIN: string;
  AUTH0_ALGORITHM: string;
  CLAIMS_NAMESPACE: string;
  STORE_COMPANY_ID: string;
  GCS_PRIVATE_BUCKET_NAME: string;
  GCS_PUBLIC_BUCKET_NAME: string;
  SENDER_EMAIL: string;
  SENDER_EMAIL_APP_PASSWORD: string;
  BIIZTRAL_WEB_URL: string;
  WHATSAPP_GRAPH_API_TOKEN: string;
  SENDER_PHONE_NUMBER: string;
  BIIZTRAL_LOGO_URL: string;
  GOOGLE_CLIENT_ID: string;
  GOOGLE_CLIENT_SECRET: string;
  GOOGLE_REDIRECT_URI: string;
  ENCRYPTION_KEY: string;
}

let envConfigurationSingleton: Environment;

function getEnv(reload = false): Environment {
  if (envConfigurationSingleton && !reload) return envConfigurationSingleton;

  dotenvExpand.expand(dotenv.config({ path: `${__dirname}/../../../.env` }));

  const allowedLogLevels = ['fatal', 'error', 'warn', 'info', 'debug', 'trace'];

  const {
    POSTGRES_DATABASE,
    POSTGRES_HOST,
    POSTGRES_PASSWORD,
    POSTGRES_PORT,
    POSTGRES_USER,
    PORT,
    LOG_LEVEL,
    NODE_ENV,
    ALLOWED_ORIGINS,
    HASURA_DEFAULT_ROLE,
    API_KEY,
    AUTH0_AUDIENCE,
    AUTH0_DOMAIN,
    AUTH0_ALGORITHM,
    SWAGGER_PORT,
    CLAIMS_NAMESPACE,
    STORE_COMPANY_ID,
    GCS_PRIVATE_BUCKET_NAME,
    GCS_PUBLIC_BUCKET_NAME,
    SENDER_EMAIL,
    SENDER_EMAIL_APP_PASSWORD,
    BIIZTRAL_WEB_URL,
    WHATSAPP_GRAPH_API_TOKEN,
    SENDER_PHONE_NUMBER,
    BIIZTRAL_LOGO_URL,
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    GOOGLE_REDIRECT_URI,
    ENCRYPTION_KEY,
  } = process.env;

  assert(POSTGRES_DATABASE, 'env var POSTGRES_DATABASE is required');
  assert(POSTGRES_HOST, 'env var POSTGRES_HOST is required');
  assert(POSTGRES_PASSWORD, 'env var POSTGRES_PASSWORD is required');
  assert(POSTGRES_PORT, 'env var POSTGRES_PORT is required');
  assert(POSTGRES_USER, 'env var POSTGRES_USER is required');
  assert(ALLOWED_ORIGINS, 'env var ALLOWED_ORIGINS is required');
  assert(HASURA_DEFAULT_ROLE, 'env var HASURA_DEFAULT_ROLE is required');
  assert(API_KEY, 'env var API_LEY is required');
  assert(AUTH0_AUDIENCE, 'env var AUTH0_AUDIENCE is required');
  assert(AUTH0_DOMAIN, 'env var AUTH0_DOMAIN is required');
  assert(AUTH0_ALGORITHM, 'env var AUTHO_ALGORITHM is required');
  assert(CLAIMS_NAMESPACE, 'env var CLAIMS_NAMESPACE is required');
  assert(STORE_COMPANY_ID, 'env var STORE_COMPANY_ID is required');
  assert(GCS_PRIVATE_BUCKET_NAME, 'env var GCS_PRIVATE_BUCKET_NAME is required');
  assert(GCS_PUBLIC_BUCKET_NAME, 'env var GCS_PUBLIC_BUCKET_NAME is required');
  assert(SENDER_EMAIL, 'env var SENDER_EMAIL is required');
  assert(SENDER_EMAIL_APP_PASSWORD, 'env var SENDER_EMAIL_APP_PASSWORD is required');
  assert(BIIZTRAL_WEB_URL, 'env var BIIZTRAL_WEB_URL is required');
  assert(WHATSAPP_GRAPH_API_TOKEN, 'env var WHATSAPP_GRAPH_API_TOKEN is required');
  assert(SENDER_PHONE_NUMBER, 'env var SENDER_PHONE_NUMBER is required');
  assert(GOOGLE_CLIENT_ID, 'env var GOOGLE_CLIENT_ID is required');
  assert(GOOGLE_CLIENT_SECRET, 'env var GOOGLE_CLIENT_SECRET is required');
  assert(GOOGLE_REDIRECT_URI, 'env var GOOGLE_REDIRECT_URI is required');
  assert(ENCRYPTION_KEY, 'env var ENCRYPTION_KEY is required');

  assert(allowedLogLevels.includes(LOG_LEVEL || ''), 'LOG_LEVEL must be one of fatal, error, warn, info, debug, trace');

  envConfigurationSingleton = {
    LOG_LEVEL: LOG_LEVEL as LogLevel,
    IS_DEV: NODE_ENV === 'development',
    PORT: Number.parseInt(PORT || '3000', 10),
    POSTGRES_DATABASE,
    POSTGRES_HOST,
    POSTGRES_PASSWORD,
    POSTGRES_PORT: Number.parseInt(POSTGRES_PORT, 10),
    POSTGRES_USER,
    SWAGGER_PORT: Number.parseInt(SWAGGER_PORT || '3001', 10),
    ALLOWED_ORIGINS: ALLOWED_ORIGINS.split(','),
    HASURA_DEFAULT_ROLE,
    API_KEY,
    AUTH0_AUDIENCE,
    AUTH0_DOMAIN,
    AUTH0_ALGORITHM,
    CLAIMS_NAMESPACE,
    STORE_COMPANY_ID,
    GCS_PUBLIC_BUCKET_NAME,
    GCS_PRIVATE_BUCKET_NAME,
    SENDER_EMAIL,
    SENDER_EMAIL_APP_PASSWORD,
    BIIZTRAL_WEB_URL,
    WHATSAPP_GRAPH_API_TOKEN,
    SENDER_PHONE_NUMBER,
    BIIZTRAL_LOGO_URL: BIIZTRAL_LOGO_URL || '',
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    GOOGLE_REDIRECT_URI,
    ENCRYPTION_KEY,
  };

  return envConfigurationSingleton;
}

const EnvConfiguration = {
  getEnv,
};

export default EnvConfiguration;
