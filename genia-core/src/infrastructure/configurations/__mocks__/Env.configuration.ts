import EnvConfiguration, { Environment } from '../Env.configuration';

const getEnvMock = jest.fn<Environment, []>(() => ({
  LOG_LEVEL: 'info',
  IS_DEV: false,
  PORT: 3000,
  POSTGRES_DATABASE: 'database',
  POSTGRES_HOST: 'host',
  POSTGRES_PASSWORD: 'password',
  POSTGRES_PORT: 5432,
  POSTGRES_USER: 'user',
  SWAGGER_PORT: 3001,
  ALLOWED_ORIGINS: ['http://localhost:3001'],
  HASURA_DEFAULT_ROLE: 'read_user',
  API_KEY: 'api_key',
  AUTH0_AUDIENCE: 'audience',
  AUTH0_DOMAIN: 'domain',
  AUTH0_ALGORITHM: 'algorithm',
  CLAIMS_NAMESPACE: 'https://claims.io',
  STORE_COMPANY_ID: 'storeCompanyId',
  GCS_PUBLIC_BUCKET_NAME: 'gcsPublicBucketName',
  GCS_PRIVATE_BUCKET_NAME: 'gcsPrivateBucketName',
  SENDER_EMAIL: '<EMAIL>',
  SENDER_EMAIL_APP_PASSWORD: 'th1s 1s v3ry 53cur3',
  BIIZTRAL_WEB_URL: 'https://app.suplif.ai/',
  WHATSAPP_GRAPH_API_TOKEN: 'token',
  SENDER_PHONE_NUMBER: '*********',
  BIIZTRAL_LOGO_URL: 'www.biiztral.com/logo',
  ENCRYPTION_KEY: 'encryptionKey',
  GOOGLE_CLIENT_ID: 'googleClientId',
  GOOGLE_CLIENT_SECRET: 'googleClientSecret',
  GOOGLE_REDIRECT_URI: 'https://app.suplif.ai/auth/google/callback',
}));

const EnvConfigurationMock: typeof EnvConfiguration = {
  getEnv: getEnvMock,
};

export default EnvConfigurationMock;
