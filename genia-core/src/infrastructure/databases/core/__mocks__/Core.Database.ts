import path from 'path';

import knex, { Knex } from 'knex';

import EnvConfiguration from '#infrastructure/configurations/Env.configuration';

import CoreDatabase, { CoreDBConfiguration } from '../Core.Database';

jest.unmock('pg');
jest.unmock('knex');
jest.unmock('dotenv');
jest.unmock('lodash');

const { default: originalCoreDB } = jest.requireActual<{default: typeof CoreDatabase}>('../Core.Database');
const { default: originalEnv } = jest.requireActual<{default: typeof EnvConfiguration}>('#infrastructure/configurations/Env.configuration');

const {
  POSTGRES_HOST, POSTGRES_PASSWORD, POSTGRES_PORT, POSTGRES_USER,
} = originalEnv.getEnv();

const coreDatabasePath = path.resolve(__dirname, '..');

const configMock: CoreDBConfiguration = {
  host: POSTGRES_HOST,
  database: 'test',
  user: POSTGRES_USER,
  password: POSTGRES_PASSWORD,
  port: POSTGRES_PORT,
};

let db: Knex;

function getClient(): Knex {
  if (!db) {
    db = knex({
      client: 'pg',
      connection: configMock,
      pool: {
        max: 10,
        min: 2,
      },
      migrations: {
        directory: `${coreDatabasePath}/migrations`,
      },
      seeds: {
        directory: `${coreDatabasePath}/seeds`,
      },
      ...originalCoreDB.getKnexPreAndPostProcessors(),
    });
  }

  return db;
}
const CoreDatabaseMock: typeof CoreDatabase = {
  getClient: jest.fn().mockImplementation(getClient),
  getConfiguration: jest.fn().mockReturnValue(configMock),
  getKnexPreAndPostProcessors: jest.fn().mockImplementation(originalCoreDB.getKnexPreAndPostProcessors),
};

export default CoreDatabaseMock;
