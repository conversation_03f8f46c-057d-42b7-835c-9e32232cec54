import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

import { seedCompanies } from './02_Company';

export const seedClients = [{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea8aa',
  name: 'autolab',
  tributaryId: '10880111020',
  companyId: seedCompanies[0].id, // PitsDepot development
  clientCompanyId: null,
  notes: 'This client is important',
},
{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea8bb',
  name: 'kavak',
  tributaryId: '10880111021',
  companyId: seedCompanies[0].id, // PitsDepot development
  clientCompanyId: null,
  notes: null,
},
{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea8cc',
  name: 'PitsDepot development as Client',
  tributaryId: null,
  companyId: seedCompanies[1].id, // Acme Company
  clientCompanyId: seedCompanies[0].id, // PitsDepot development
  notes: null,
},
{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea8dd',
  name: 'PitsDepot development as Client',
  tributaryId: null,
  companyId: seedCompanies[2].id, // Umbrella Corporation
  clientCompanyId: seedCompanies[0].id, // PitsDepot development
  notes: null,
},
{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea8ee',
  name: 'PitsDepot development as Client',
  tributaryId: null,
  companyId: seedCompanies[3].id, // Dharma Initiative
  clientCompanyId: seedCompanies[0].id, // PitsDepot development
  notes: null,
},
{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea8ff',
  name: 'PitsDepot development as Client',
  tributaryId: null,
  companyId: seedCompanies[4].id, // LexCorp
  clientCompanyId: seedCompanies[0].id, // PitsDepot development
  notes: null,
},
{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea8d5',
  name: 'Planet Expres as Client',
  tributaryId: null,
  companyId: seedCompanies[4].id, // LexCorp
  clientCompanyId: seedCompanies[5].id, // Planet Express
  notes: null,
},
{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea8d6',
  name: 'Planet Express as Client',
  tributaryId: null,
  companyId: seedCompanies[0].id, // PitsDepot development
  clientCompanyId: seedCompanies[5].id, // Planet Express
  notes: null,
},
];

export async function seed(knex: Knex): Promise<void> {
  await knex(TableNamesConfiguration.CLIENT).insert(seedClients);
}
