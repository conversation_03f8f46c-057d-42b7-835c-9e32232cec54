import { Knex } from 'knex';

import { CatalogType } from '#domain/aggregates/catalog/Catalog.Entity';
import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function seed(knex: Knex): Promise<void> {
  const subscriptions = await knex(TableNamesConfiguration.SUBSCRIPTION)
    .select('id', 'companyId', 'tierId');

  if (!subscriptions.length) return;

  // Company names for special IA credits overrides
  const companies = await knex(TableNamesConfiguration.COMPANY).select('id', 'name');
  const companyNameById = new Map<string, string>(companies.map((c) => [String(c.id), String(c.name).toLowerCase()]));

  // Map quotas by name for quick lookup
  const quotas = await knex(TableNamesConfiguration.QUOTA).select('id', 'name');
  const quotaIdByName = new Map<string, string>(quotas.map((q) => [String(q.name), String(q.id)]));

  // For each subscription, compute usage per quota available on its tier
  for (const sub of subscriptions) {
    const { id: subscriptionId, companyId, tierId } = sub as { id: string, companyId: string, tierId: string };

    // Get quotas configured for the tier
    const tierQuotas = await knex(TableNamesConfiguration.TIER_QUOTA)
      .select('quotaId')
      .where({ tierId });
    if (!tierQuotas.length) break;

    // Compute usage per company
    const [{ count: usersCountRow }] = await knex(TableNamesConfiguration.USER_COMPANY)
      .where({ companyId })
      .count({ count: '*' });
    const usersCount = Number(usersCountRow || 0);

    const [{ count: inventoryCountRow }] = await knex(TableNamesConfiguration.INVENTORY)
      .where({ companyId })
      .count({ count: '*' });
    const inventoryCount = Number(inventoryCountRow || 0);

    const [{ count: catalogCountRow }] = await knex(TableNamesConfiguration.CATALOG)
      .where({ companyId, type: CatalogType.PRODUCT })
      .count({ count: '*' });
    const catalogCount = Number(catalogCountRow || 0);

    const [{ count: serviceCountRow }] = await knex(TableNamesConfiguration.CATALOG)
      .where({ companyId, type: CatalogType.SERVICE })
      .count({ count: '*' });
    const serviceCount = Number(serviceCountRow || 0);

    const rowsToInsert: { subscriptionId: string, quotaId: string, usage: number }[] = [];

    for (const tq of tierQuotas) {
      const quotaId = String((tq as { quotaId: string }).quotaId);
      // Resolve by quota name
      const quotaName = [...quotaIdByName.entries()].find(([, id]) => id === quotaId)?.[0];
      let usage = 0;
      if (quotaName === 'users') usage = Math.max(1, usersCount);
      else if (quotaName === 'inventoryItems') usage = Math.max(0, inventoryCount);
      else if (quotaName === 'productItems') usage = Math.max(0, catalogCount);
      else if (quotaName === 'serviceItems') usage = Math.max(0, serviceCount);
      else if (quotaName === 'iaCredits') {
        const companyName = companyNameById.get(String(companyId)) || '';
        if (companyName.includes('pitsdepot')) usage = 150;
        else if (companyName.includes('planet express')) usage = 15;
        else usage = 0;
      }

      rowsToInsert.push({ subscriptionId, quotaId, usage });
    }

    for (const row of rowsToInsert) {
      const exists = await knex(TableNamesConfiguration.SUBSCRIPTION_QUOTA_USAGE)
        .where({ subscriptionId: row.subscriptionId, quotaId: row.quotaId })
        .first();
      if (!exists) {
        // eslint-disable-next-line no-await-in-loop
        await knex(TableNamesConfiguration.SUBSCRIPTION_QUOTA_USAGE).insert(row);
      }
    }
  }
}
