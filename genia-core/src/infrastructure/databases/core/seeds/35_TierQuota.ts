import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function seed(knex: Knex): Promise<void> {
  const now = new Date();

  const starterTierId = '11111111-1111-4111-8111-111111111111';
  const professionalTierId = '22222222-2222-4222-8222-222222222222';
  const enterpriseTierId = '33333333-3333-4333-8333-333333333333';
  const eliteTierId = '44444444-4444-4444-8444-444444444444';

  const quotas = await knex(TableNamesConfiguration.QUOTA).select('id', 'name');
  const quotaIdByName = new Map<string, string>(quotas.map((q) => [q.name as string, q.id as string]));
  const getQuotaId = (name: string): string => {
    const id = quotaIdByName.get(name);
    if (!id) throw new Error(`Quota not found for name: ${name}`);
    return id;
  };

  const rows = [
    // Starter
    {
      tierId: starterTierId, quotaId: getQuotaId('iaCredits'), quantity: 50, createdAt: now, updatedAt: now,
    },
    {
      tierId: starterTierId, quotaId: getQuotaId('users'), quantity: 5, createdAt: now, updatedAt: now,
    },
    {
      tierId: starterTierId, quotaId: getQuotaId('productItems'), quantity: 50, createdAt: now, updatedAt: now,
    },
    {
      tierId: starterTierId, quotaId: getQuotaId('inventoryItems'), quantity: 50, createdAt: now, updatedAt: now,
    },
    {
      tierId: starterTierId, quotaId: getQuotaId('serviceItems'), quantity: 5, createdAt: now, updatedAt: now,
    },
    // Professional
    {
      tierId: professionalTierId, quotaId: getQuotaId('iaCredits'), quantity: 150, createdAt: now, updatedAt: now,
    },
    {
      tierId: professionalTierId, quotaId: getQuotaId('users'), quantity: 10, createdAt: now, updatedAt: now,
    },
    {
      tierId: professionalTierId, quotaId: getQuotaId('productItems'), quantity: 100, createdAt: now, updatedAt: now,
    },
    {
      tierId: professionalTierId, quotaId: getQuotaId('serviceItems'), quantity: 10, createdAt: now, updatedAt: now,
    },
    {
      tierId: professionalTierId, quotaId: getQuotaId('inventoryItems'), quantity: 100, createdAt: now, updatedAt: now,
    },
    // Enterprise/Elite only iaCredits
    {
      tierId: enterpriseTierId, quotaId: getQuotaId('iaCredits'), quantity: 1000, createdAt: now, updatedAt: now,
    },
    {
      tierId: eliteTierId, quotaId: getQuotaId('iaCredits'), quantity: 1500, createdAt: now, updatedAt: now,
    },
  ];

  // Avoid duplicates if run multiple times
  for (const row of rows) {
    const exists = await knex(TableNamesConfiguration.TIER_QUOTA)
      .where({ tierId: row.tierId, quotaId: row.quotaId })
      .first();
    if (!exists) {
      // eslint-disable-next-line no-await-in-loop
      await knex(TableNamesConfiguration.TIER_QUOTA).insert(row);
    }
  }
}
