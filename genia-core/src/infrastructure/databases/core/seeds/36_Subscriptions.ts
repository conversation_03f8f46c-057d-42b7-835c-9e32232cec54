import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function seed(knex: Knex): Promise<void> {
  const companies = await knex(TableNamesConfiguration.COMPANY).select('id', 'name');

  const tiers = await knex(TableNamesConfiguration.TIER).select('id', 'name');
  const tierIdByName = new Map<string, string>(tiers.map((t) => [String(t.name), String(t.id)]));
  const eliteId = tierIdByName.get('Elite');
  const starterId = tierIdByName.get('Starter');
  const professionalId = tierIdByName.get('Professional');
  if (!eliteId || !starterId || !professionalId) {
    throw new Error('Required tiers missing: ensure Elite, Starter and Professional exist before seeding subscriptions');
  }

  const expiresAt = new Date();
  expiresAt.setFullYear(expiresAt.getFullYear() + 1);

  const pitsDepot = companies.find((c) => c.name.toLowerCase().includes('pitsdepot'));
  const planetExpress = companies.find((c) => c.name.toLowerCase().includes('planet express'));

  const rows: Array<{ id?: string, companyId: string, tierId: string, expiresAt: Date, createdAt?: Date, updatedAt?: Date }> = [];

  if (pitsDepot) rows.push({ companyId: String(pitsDepot.id), tierId: eliteId, expiresAt });
  if (planetExpress) rows.push({ companyId: String(planetExpress.id), tierId: professionalId, expiresAt });

  const excludedIds = new Set<string>([
    pitsDepot ? String(pitsDepot.id) : '',
    planetExpress ? String(planetExpress.id) : '',
  ].filter(Boolean));

  for (const company of companies) {
    const companyId = String(company.id);
    if (!excludedIds.has(companyId)) {
      rows.push({ companyId, tierId: starterId, expiresAt });
    }
  }

  for (const row of rows) {
    // Avoid duplicates per company
    const exists = await knex(TableNamesConfiguration.SUBSCRIPTION)
      .where({ companyId: row.companyId })
      .first();
    if (!exists) {
      // eslint-disable-next-line no-await-in-loop
      await knex(TableNamesConfiguration.SUBSCRIPTION).insert(row);
    }
  }
}
