import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function seed(knex: Knex): Promise<void> {
  const now = new Date();

  const starterTierId = '11111111-1111-4111-8111-111111111111';
  const professionalTierId = '22222222-2222-4222-8222-222222222222';
  const enterpriseTierId = '33333333-3333-4333-8333-333333333333';
  const eliteTierId = '44444444-4444-4444-8444-444444444444';

  const existing = await knex(TableNamesConfiguration.TIER)
    .select('id')
    .whereIn('id', [starterTierId, professionalTierId, enterpriseTierId, eliteTierId]);
  if (existing.length === 4) return;

  const toInsert = [
    {
      id: starterTierId,
      name: 'Starter',
      createdAt: now,
      updatedAt: now,
    },
    {
      id: professionalTierId,
      name: 'Professional',
      createdAt: now,
      updatedAt: now,
    },
    {
      id: enterpriseTierId,
      name: 'Enterprise',
      createdAt: now,
      updatedAt: now,
    },
    {
      id: eliteTierId,
      name: 'Elite',
      createdAt: now,
      updatedAt: now,
    },
  ];

  const existingIds = new Set(existing.map((e) => e.id as string));
  const filtered = toInsert.filter((row) => !existingIds.has(row.id));
  if (filtered.length) {
    await knex(TableNamesConfiguration.TIER).insert(filtered);
  }
}
