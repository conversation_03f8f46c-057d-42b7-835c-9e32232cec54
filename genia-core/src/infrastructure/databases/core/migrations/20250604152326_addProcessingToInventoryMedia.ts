import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

const tableName = TableNamesConfiguration.INVENTORY_MEDIA;

export async function up(knex: Knex): Promise<void> {
  await knex.schema.table(tableName, (table: Knex.AlterTableBuilder) => {
    table.boolean('processing').defaultTo(true).notNullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.table(tableName, (table: Knex.AlterTableBuilder) => {
    table.dropColumn('processing');
  });
}
