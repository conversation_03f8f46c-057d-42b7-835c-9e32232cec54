import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import MigrationUtility from '#infrastructure/databases/core/utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.SUBSCRIPTION, async (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('company_id').notNullable().index();
    table.uuid('tier_id').notNullable().index();
    table.timestamp('expires_at').nullable().index();

    table.foreign('company_id').references('id').inTable(TableNamesConfiguration.COMPANY).onDelete('RESTRICT')
      .onUpdate('RESTRICT');
    table.foreign('tier_id').references('id').inTable(TableNamesConfiguration.TIER).onDelete('RESTRICT')
      .onUpdate('RESTRICT');

    MigrationUtility.setTimeStamps(table, knex);
  });

  await MigrationUtility.autoUpdateTrigger(TableNamesConfiguration.SUBSCRIPTION, knex);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.SUBSCRIPTION);
}
