import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import MigrationUtility from '#infrastructure/databases/core/utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.TIER_QUOTA, async (table) => {
    table.uuid('tier_id').notNullable();
    table.uuid('quota_id').notNullable();
    table.integer('quantity').notNullable();

    table.primary(['tier_id', 'quota_id']);

    table.foreign('tier_id').references('id').inTable(TableNamesConfiguration.TIER).onDelete('CASCADE');
    table.foreign('quota_id').references('id').inTable(TableNamesConfiguration.QUOTA).onDelete('CASCADE');

    MigrationUtility.setTimeStamps(table, knex);
  });

  await MigrationUtility.autoUpdateTrigger(TableNamesConfiguration.TIER_QUOTA, knex);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.TIER_QUOTA);
}
