import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.CATALOG_INDEX, async (table) => {
    table.uuid('catalog_id').notNullable().index();
    table.uuid('company_id').notNullable().index();
    table.text('indexed_text').notNullable();

    table.foreign('catalog_id').references('id').inTable(TableNamesConfiguration.CATALOG).onDelete('CASCADE')
      .onUpdate('RESTRICT');

    table.foreign('company_id').references('id').inTable(TableNamesConfiguration.COMPANY).onDelete('CASCADE');

    await knex.raw(`CREATE INDEX catalog_index_indexed_text_trgm_idx ON ${TableNamesConfiguration.CATALOG_INDEX} USING gin (indexed_text gin_trgm_ops)`);
  });

  await knex.raw(`
    CREATE OR REPLACE FUNCTION catalog_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG} AS $$
    BEGIN
      RETURN QUERY SELECT * FROM catalog_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION catalog_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG} AS $$
    BEGIN      
      RETURN QUERY
      WITH ordered_results AS (
        SELECT 
          catalog_id, 
          SUM(similarity(indexed_text, search_term)) AS total_similarity
        FROM ${TableNamesConfiguration.CATALOG_INDEX}
        WHERE indexed_text % search_term
          AND company_id = company_id_for_searching
        GROUP BY catalog_id
        ORDER BY total_similarity DESC
        LIMIT 50
      )
      SELECT 
        catalog.*
      FROM 
        ordered_results
      JOIN 
        catalog ON ordered_results.catalog_id = catalog.id
      WHERE 
        ordered_results.total_similarity > 0.1
      ORDER BY
        ordered_results.total_similarity DESC;
    END;
    $$ LANGUAGE plpgsql STABLE SET pg_trgm.similarity_threshold = 0.05;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.CATALOG_INDEX);
  await knex.raw('DROP FUNCTION IF EXISTS catalog_search');
  await knex.raw('DROP FUNCTION IF EXISTS catalog_search_by_company');
}
