import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import MigrationUtility from '#infrastructure/databases/core/utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.CATALOG_DISCOUNT, (table) => {
    MigrationUtility.setTimeStamps(table, knex);
  });

  await knex.schema.alterTable(TableNamesConfiguration.STORE_DISCOUNT, (table) => {
    MigrationUtility.setTimeStamps(table, knex);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.CATALOG_DISCOUNT, (table) => {
    table.dropColumn('created_at');
    table.dropColumn('updated_at');
  });

  await knex.schema.alterTable(TableNamesConfiguration.STORE_DISCOUNT, (table) => {
    table.dropColumn('created_at');
    table.dropColumn('updated_at');
  });
}
