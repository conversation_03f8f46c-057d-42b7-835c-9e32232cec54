import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.CLIENT, async (table: Knex.CreateTableBuilder) => {
    table.text('notes').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.table(TableNamesConfiguration.CLIENT, (table) => {
    table.dropColumn('notes');
  });
}
