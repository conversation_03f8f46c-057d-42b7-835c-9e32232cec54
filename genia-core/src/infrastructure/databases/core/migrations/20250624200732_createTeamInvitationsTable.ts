import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.TEAM_INVITATION, async (table) => {
    table.uuid('invited_user_id').notNullable().index();
    table.uuid('user_id').notNullable();
    table.uuid('company_id').notNullable();
    table.enum('role', ['ADMIN', 'USER'], {
      useNative: true,
      enumName: 'invitation_role_enum',
    }).notNullable().defaultTo('USER');
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();

    table.primary(['invited_user_id', 'company_id']);

    table.foreign('invited_user_id').references('id').inTable(TableNamesConfiguration.USER).onDelete('CASCADE')
      .onUpdate('CASCADE');

    table.foreign('user_id').references('id').inTable(TableNamesConfiguration.USER).onDelete('CASCADE')
      .onUpdate('CASCADE');

    table.foreign('company_id').references('id').inTable(TableNamesConfiguration.COMPANY).onDelete('CASCADE')
      .onUpdate('CASCADE');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.TEAM_INVITATION);
  await knex.raw('DROP TYPE IF EXISTS invitation_role_enum');
}
