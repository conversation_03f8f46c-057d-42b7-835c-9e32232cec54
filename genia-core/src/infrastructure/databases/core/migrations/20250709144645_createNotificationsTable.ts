import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.NOTIFICATION, (table) => {
    table.uuid('id').primary().index();
    table.uuid('owner_user_id')
      .notNullable()
      .references('id')
      .inTable(TableNamesConfiguration.USER)
      .onDelete('CASCADE');
    table.uuid('company_id')
      .notNullable()
      .references('id')
      .inTable(TableNamesConfiguration.COMPANY)
      .onDelete('CASCADE');
    table.enum('type', ['client_invitation', 'provider_invitation', 'sale_order_created', 'purchase_order_created'], {
      useNative: true,
      enumName: 'notification_type_enum',
    }).notNullable();
    table.jsonb('payload').nullable();
    table.specificType('required_roles', 'TEXT[]');
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.NOTIFICATION);
  await knex.raw('DROP TYPE IF EXISTS notification_type_enum');
}
