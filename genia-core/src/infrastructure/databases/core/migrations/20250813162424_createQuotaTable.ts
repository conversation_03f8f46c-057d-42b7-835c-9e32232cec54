import type { K<PERSON> } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import { QuotaType } from '#infrastructure/databases/core/types';
import MigrationUtility from '#infrastructure/databases/core/utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.QUOTA, async (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.string('name', 80).notNullable();
    table.enum('type', ['perpetual', 'monthly', 'quarterly', 'semiannually'], {
      useNative: true,
      enumName: 'quota_type_enum',
    }).notNullable();

    MigrationUtility.setTimeStamps(table, knex);
  });

  await knex(TableNamesConfiguration.QUOTA).insert([
    { name: 'productItems', type: 'perpetual' as QuotaType },
    { name: 'inventoryItems', type: 'perpetual' as QuotaType },
    { name: 'users', type: 'perpetual' as QuotaType },
    { name: 'serviceItems', type: 'perpetual' as QuotaType },
    { name: 'iaCredits', type: 'monthly' as QuotaType },
  ]);

  await MigrationUtility.autoUpdateTrigger(TableNamesConfiguration.QUOTA, knex);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.QUOTA);
  await knex.raw('DROP TYPE IF EXISTS quota_type_enum');
}
