import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.SUBSCRIPTION_QUOTA_USAGE, async (table) => {
    table.uuid('subscription_id').notNullable().index();
    table.uuid('quota_id').notNullable().index();
    table.decimal('usage', 14, 4).notNullable().defaultTo(0);

    table.primary(['subscription_id', 'quota_id']);

    table
      .foreign('subscription_id')
      .references('id')
      .inTable(TableNamesConfiguration.SUBSCRIPTION)
      .onDelete('CASCADE')
      .onUpdate('RESTRICT');
    table
      .foreign('quota_id')
      .references('id')
      .inTable(TableNamesConfiguration.QUOTA)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.SUBSCRIPTION_QUOTA_USAGE);
}
