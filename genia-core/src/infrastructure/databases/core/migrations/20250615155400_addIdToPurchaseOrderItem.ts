import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

const tableName = TableNamesConfiguration.PURCHASE_ORDER_ITEM;

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.dropPrimary();
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.unique(['purchase_order_id', 'reference_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.dropPrimary();
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.primary(['purchase_order_id', 'reference_id']);
  });
}
