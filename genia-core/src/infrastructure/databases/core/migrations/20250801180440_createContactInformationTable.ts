import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import MigrationUtility from '#infrastructure/databases/core/utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.CONTACT_INFORMATION, async (table) => {
    table.uuid('entity_id').index();
    table.enum('entity', ['company', 'client', 'provider'], {
      useNative: true,
      enumName: 'contact_information_entity_enum',
    }).notNullable().index();
    table.string('billing_email').nullable();
    table.string('billing_phone_number').nullable();
    table.string('billing_whatsapp').nullable();
    table.string('purchases_email').nullable();
    table.string('purchases_phone_number').nullable();
    table.string('purchases_whatsapp').nullable();
    table.string('sales_email').nullable();
    table.string('sales_phone_number').nullable();
    table.string('sales_whatsapp').nullable();
    table.string('shipping_address').nullable();
    table.string('billing_address').nullable();

    table.primary(['entity', 'entity_id']);

    MigrationUtility.setTimeStamps(table, knex);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.CONTACT_INFORMATION);
  await knex.raw('DROP TYPE IF EXISTS contact_information_entity_enum');
}
