import type { K<PERSON> } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

import MigrationUtility from '../utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.QUOTE, async (table: Knex.CreateTableBuilder) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('client_id').notNullable().index().references('id')
      .inTable(TableNamesConfiguration.CLIENT)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
    table.uuid('company_id').notNullable().index().references('id')
      .inTable(TableNamesConfiguration.COMPANY)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
    table.uuid('assigned_user_id').nullable().references('id').inTable(TableNamesConfiguration.USER)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');

    table.enum('status', ['draft', 'sent', 'approved_by_client', 'changes_requested', 'closed'], {
      useNative: true,
      enumName: 'quote_status_enum',
    }).notNullable().defaultTo('draft').index();

    table.string('read_id').notNullable();

    table.unique(['read_id', 'company_id']);

    table.timestamp('promised_delivery_date').nullable();
    table.decimal('subtotal_before_discount', 14, 4).notNullable().defaultTo(0);
    table.decimal('subtotal', 14, 4).notNullable().defaultTo(0);
    table.decimal('total_discount', 14, 4).notNullable().defaultTo(0);
    table.decimal('total_taxes', 14, 4).notNullable().defaultTo(0);
    table.decimal('total', 14, 4).notNullable().defaultTo(0);
    table.decimal('shipping_price', 14, 4).notNullable().defaultTo(0);
    table.string('shipping_address', 200).nullable();
    table.jsonb('taxes').notNullable().defaultTo('[]');

    MigrationUtility.setTimeStamps(table, knex);
  });

  await MigrationUtility.companyNaturalIdTrigger(TableNamesConfiguration.QUOTE, 'read_id', knex);
  await MigrationUtility.autoUpdateTrigger(TableNamesConfiguration.QUOTE, knex);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable(TableNamesConfiguration.QUOTE);
  await knex.raw('DROP TYPE IF EXISTS quote_status_enum');
}
