import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.USER, (table: Knex.AlterTableBuilder) => {
    table.string('name').nullable();
    table.string('lastName').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.USER, (table: Knex.AlterTableBuilder) => {
    table.dropColumn('name');
    table.dropColumn('lastName');
  });
}
