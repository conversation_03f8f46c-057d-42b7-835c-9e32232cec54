import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.COMPANY, (table: Knex.AlterTableBuilder) => {
    table.string('tributaryId').notNullable().defaultTo('');
    table.string('description').nullable();
    table.string('country').references('iso_code')
      .inTable(TableNamesConfiguration.COUNTRY)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT')
      .defaultTo('MX');
    table.timestamp('verifiedAt', { useTz: true }).nullable();
    table.dropColumn('referenceId');
  });

  await knex.schema.alterTable(TableNamesConfiguration.COMPANY, (table: Knex.AlterTableBuilder) => {
    table.string('tributaryId').notNullable().defaultTo(null).alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.COMPANY, (table: Knex.AlterTableBuilder) => {
    table.dropColumn('tributaryId');
    table.dropColumn('description');
    table.dropColumn('country');
  });
}
