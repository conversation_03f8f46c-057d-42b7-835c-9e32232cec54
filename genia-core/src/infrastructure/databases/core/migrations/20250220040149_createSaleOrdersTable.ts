import type { K<PERSON> } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

import MigrationUtility from '../utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.SALE_ORDER, async (table: Knex.CreateTableBuilder) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('client_id').notNullable().index().references('id')
      .inTable(TableNamesConfiguration.CLIENT)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
    table.uuid('company_id').notNullable().index().references('id')
      .inTable(TableNamesConfiguration.COMPANY)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
    table.uuid('assigned_user_id').nullable().references('id').inTable(TableNamesConfiguration.USER)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');

    table.string('read_id').notNullable();

    table.unique(['read_id', 'company_id']);

    table.enum('status', ['client_approval', 'approved_by_client', 'pending', 'processing', 'in_transit', 'completed', 'cancelled', 'in_review'], {
      useNative: true,
      enumName: 'sale_order_status_enum',
    }).notNullable().defaultTo('pending').index();

    table.timestamp('delivery_date').nullable();
    table.timestamp('shipped_at').nullable();
    table.timestamp('review_started_at').nullable();
    table.decimal('subtotal_before_discount', 14, 4).notNullable().defaultTo(0);
    table.decimal('subtotal', 14, 4).notNullable().defaultTo(0);
    table.decimal('total_discount', 14, 4).notNullable().defaultTo(0);
    table.decimal('total_taxes', 14, 4).notNullable().defaultTo(0);
    table.decimal('total', 14, 4).notNullable().defaultTo(0);
    table.decimal('shipping_price', 14, 4).notNullable().defaultTo(0);
    table.string('shipping_address', 200).nullable();
    table.string('notes', 2000).nullable();
    table.jsonb('taxes').notNullable().defaultTo('[]');

    MigrationUtility.setTimeStamps(table, knex);
  });

  await MigrationUtility.companyNaturalIdTrigger(TableNamesConfiguration.SALE_ORDER, 'read_id', knex);
  await MigrationUtility.autoUpdateTrigger(TableNamesConfiguration.SALE_ORDER, knex);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable(TableNamesConfiguration.SALE_ORDER);
  await knex.raw('DROP TYPE IF EXISTS sale_order_status_enum');
}
