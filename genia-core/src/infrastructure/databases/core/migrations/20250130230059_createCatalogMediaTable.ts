import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

import MigrationUtility from '../utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.CATALOG_MEDIA, async (table: Knex.CreateTableBuilder) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('catalog_id').notNullable();
    table.text('url').notNullable();
    table.enu('media_type', ['image', 'video'], {
      useNative: true,
      enumName: 'media_type_enum',
      existingType: true,
    }).notNullable();

    table.foreign('catalog_id').references('id').inTable(TableNamesConfiguration.CATALOG).onDelete('CASCADE')
      .onUpdate('RESTRICT');

    MigrationUtility.setTimeStamps(table, knex);
  });

  await MigrationUtility.autoUpdateTrigger(TableNamesConfiguration.CATALOG_MEDIA, knex);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.CATALOG_MEDIA);
}
