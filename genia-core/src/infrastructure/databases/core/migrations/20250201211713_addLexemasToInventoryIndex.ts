import type { <PERSON><PERSON> } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  // Fake and empty table just for hasura to track specific search data
  await knex.schema.createTable(`${TableNamesConfiguration.INVENTORY}_search_results`, (table: Knex.CreateTableBuilder) => {
    table.inherits(TableNamesConfiguration.INVENTORY);
    table.float('rank', 4, 4).notNullable();
  });

  await knex.raw(`
    ALTER TABLE ${TableNamesConfiguration.INVENTORY_INDEX}
    ADD COLUMN indexed_vector tsvector GENERATED ALWAYS AS (to_tsvector('spanish', indexed_text)) STORED;
  `);

  await knex.schema.alterTable(TableNamesConfiguration.INVENTORY_INDEX, (table: Knex.AlterTableBuilder) => {
    table.index('indexed_vector', 'inventory_index_indexed_vector_idx', 'gin');
  });

  await knex.raw('DROP INDEX IF EXISTS inventory_index_indexed_text_trgm_idx');

  await knex.raw('DROP FUNCTION IF EXISTS inventory_search');
  await knex.raw('DROP FUNCTION IF EXISTS inventory_search_by_company');

  await knex.raw(`
    CREATE OR REPLACE FUNCTION inventory_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.INVENTORY}_search_results AS $$
    BEGIN
      RETURN QUERY SELECT * FROM inventory_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION inventory_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.INVENTORY}_search_results AS $$
    BEGIN      
      RETURN QUERY
      WITH ordered_results AS (
        SELECT 
          inventory_id,
          ts_rank_cd(indexed_vector, plainto_tsquery('spanish', replace(search_term, '-', ' '))) AS rank
        FROM ${TableNamesConfiguration.INVENTORY_INDEX}
        WHERE indexed_vector @@ plainto_tsquery('spanish', replace(search_term, '-', ' '))
          AND company_id = company_id_for_searching
      )
      SELECT 
        inventory.*, ordered_results.rank
      FROM 
        ordered_results
      JOIN 
        ${TableNamesConfiguration.INVENTORY} ON ordered_results.inventory_id = inventory.id;
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw('DROP FUNCTION IF EXISTS inventory_search_by_company');
  await knex.raw('DROP FUNCTION IF EXISTS inventory_search');

  await knex.raw(`
    CREATE OR REPLACE FUNCTION inventory_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.INVENTORY} AS $$
    BEGIN      
      RETURN QUERY
      WITH ordered_results AS (
        SELECT 
          inventory_id, 
          SUM(similarity(indexed_text, search_term)) AS total_similarity
        FROM ${TableNamesConfiguration.INVENTORY_INDEX}
        WHERE indexed_text % search_term
          AND company_id = company_id_for_searching
        GROUP BY inventory_id
        ORDER BY total_similarity DESC
        LIMIT 50
      )
      SELECT 
        inventory.*
      FROM 
        ordered_results
      JOIN 
        inventory ON ordered_results.inventory_id = inventory.id
      WHERE 
        ordered_results.total_similarity > 0.1
      ORDER BY
        ordered_results.total_similarity DESC;
    END;
    $$ LANGUAGE plpgsql STABLE SET pg_trgm.similarity_threshold = 0.05;
  `);

  await knex.raw(`CREATE INDEX inventory_index_indexed_text_trgm_idx ON ${TableNamesConfiguration.INVENTORY_INDEX} USING gin (indexed_text gin_trgm_ops)`);

  await knex.schema.alterTable(TableNamesConfiguration.INVENTORY_INDEX, (table: Knex.AlterTableBuilder) => {
    table.dropIndex(['indexed_vector'], 'inventory_index_indexed_vector_idx');
    table.dropColumn('indexed_vector');
  });

  await knex.schema.dropTable(`${TableNamesConfiguration.INVENTORY}_search_results`);
}
