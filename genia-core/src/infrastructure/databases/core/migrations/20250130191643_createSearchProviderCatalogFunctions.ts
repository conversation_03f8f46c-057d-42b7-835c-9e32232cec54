import type { <PERSON><PERSON> } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`
    CREATE OR REPLACE FUNCTION providers_catalog_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG} AS $$
    BEGIN
      RETURN QUERY SELECT * FROM providers_catalog_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION providers_catalog_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG} AS $$
    BEGIN      
      RETURN QUERY
      WITH company_providers AS (
        SELECT company_id FROM client c WHERE client_company_id = company_id_for_searching
      ), ordered_results AS (
        SELECT 
          catalog_id, 
          SUM(similarity(indexed_text, search_term)) AS total_similarity
        FROM ${TableNamesConfiguration.CATALOG_INDEX} ci
        WHERE indexed_text % search_term
          AND company_id IN (SELECT company_id FROM company_providers)
        GROUP BY catalog_id
        ORDER BY total_similarity DESC
      )
      SELECT 
        catalog.*
      FROM 
        ordered_results
      JOIN 
        catalog ON ordered_results.catalog_id = catalog.id
      WHERE 
        ordered_results.total_similarity > 0.1 AND disabled_at IS NULL
      ORDER BY
        ordered_results.total_similarity DESC
      LIMIT 50;
    END;
    $$ LANGUAGE plpgsql STABLE SET pg_trgm.similarity_threshold = 0.05;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw('DROP FUNCTION IF EXISTS providers_catalog_search');
  await knex.raw('DROP FUNCTION IF EXISTS providers_catalog_search_by_company');
}
