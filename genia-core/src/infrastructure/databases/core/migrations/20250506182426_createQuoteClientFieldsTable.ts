import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

import MigrationUtility from '../utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.QUOTE_CLIENT_FIELDS, async (table: Knex.CreateTableBuilder) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('quote_id').notNullable().index().references('id')
      .inTable(TableNamesConfiguration.QUOTE)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');

    table.uuid('related_purchase_order_id').nullable().index().references('id')
      .inTable(TableNamesConfiguration.SALE_ORDER)
      .onDelete('CASCADE')
      .onUpdate('CASCADE');

    table.string('notes', 2000).nullable();

    MigrationUtility.setTimeStamps(table, knex);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable(TableNamesConfiguration.QUOTE_CLIENT_FIELDS);
}
