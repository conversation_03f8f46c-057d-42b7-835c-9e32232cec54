import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.table(TableNamesConfiguration.PROVIDER, async (table: Knex.AlterTableBuilder) => {
    table.unique(['company_id', 'name']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.table(TableNamesConfiguration.PROVIDER, async (table: Knex.AlterTableBuilder) => {
    table.dropUnique(['company_id', 'name']);
  });
}
