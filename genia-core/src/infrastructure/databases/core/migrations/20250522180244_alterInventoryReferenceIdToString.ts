import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

const tableName = TableNamesConfiguration.PURCHASE_ORDER_ITEM;
const primaryKeyColumns = ['purchase_order_id', 'reference_id'];
const referenceIdColumn = 'reference_id';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.dropPrimary();
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.dropIndex([referenceIdColumn]);
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.string(referenceIdColumn, 36).notNullable().alter();
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.index([referenceIdColumn]);
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.primary(primaryKeyColumns);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.dropPrimary();
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.dropIndex([referenceIdColumn]);
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.uuid(referenceIdColumn).notNullable().alter(); // ¡Importante mantener notNullable!
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.index([referenceIdColumn]);
  });

  await knex.schema.alterTable(tableName, (table: Knex.AlterTableBuilder) => {
    table.primary(primaryKeyColumns);
  });
}
