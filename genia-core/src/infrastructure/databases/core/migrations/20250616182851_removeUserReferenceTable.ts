import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import MigrationUtility from '#infrastructure/databases/core/utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('user_reference');
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.createTable('user_reference', async (table: Knex.CreateTableBuilder) => {
    table.uuid('user_id').notNullable().references('id').inTable(TableNamesConfiguration.USER)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
    table.text('reference_id').notNullable();

    table.primary(['user_id', 'reference_id']);

    MigrationUtility.setCreatedAt(table, knex);
  });
}
