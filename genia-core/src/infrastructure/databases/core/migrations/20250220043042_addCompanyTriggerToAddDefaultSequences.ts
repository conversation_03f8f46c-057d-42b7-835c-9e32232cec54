import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  // Create trigger function to insert default company sequence record when a new company is created
  await knex.raw(`
    CREATE OR REPLACE FUNCTION add_default_company_sequence()
    R<PERSON><PERSON><PERSON> trigger AS $$
    BEGIN
      INSERT INTO ${TableNamesConfiguration.COMPANY_SEQUENCE} (company_id, prefix, entity)
      VALUES 
        (NEW.id, 'CAT', '${TableNamesConfiguration.CATALOG}'),
        (NEW.id, 'SKU', '${TableNamesConfiguration.INVENTORY}'),
        (NEW.id, 'SORD', '${TableNamesConfiguration.SALE_ORDER}'),
        (NEW.id, 'PORD', '${TableNamesConfiguration.PURCHASE_ORDER}'),
        (NEW.id, 'SINV', '${TableNamesConfiguration.SALE_INVOICE}'),
        (NEW.id, 'PINV', '${TableNamesConfiguration.PURCHASE_INVOICE}'),
        (NEW.id, 'SPAY', '${TableNamesConfiguration.SALE_PAYMENT}'),
        (NEW.id, 'PPAY', '${TableNamesConfiguration.PROVIDER_PAYMENT}');
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  `);

  // Create trigger on the company table to call the above function after an insert
  await knex.raw(`
    CREATE TRIGGER company_sequence_trigger
    AFTER INSERT ON ${TableNamesConfiguration.COMPANY}
    FOR EACH ROW
    EXECUTE PROCEDURE add_default_company_sequence();
  `);

  const entityToPrefixMap = {
    [TableNamesConfiguration.CATALOG]: 'CAT',
    [TableNamesConfiguration.INVENTORY]: 'SKU',
    [TableNamesConfiguration.SALE_ORDER]: 'SORD',
    [TableNamesConfiguration.PURCHASE_ORDER]: 'PORD',
    [TableNamesConfiguration.SALE_INVOICE]: 'SINV',
    [TableNamesConfiguration.PURCHASE_INVOICE]: 'PINV',
    [TableNamesConfiguration.SALE_PAYMENT]: 'SPAY',
    [TableNamesConfiguration.PROVIDER_PAYMENT]: 'PPAY',
  };

  const entityToCompanyMap: {[key:string]: Record<string, boolean>} = {
    [TableNamesConfiguration.CATALOG]: {},
    [TableNamesConfiguration.INVENTORY]: {},
    [TableNamesConfiguration.SALE_ORDER]: {},
    [TableNamesConfiguration.PURCHASE_ORDER]: {},
    [TableNamesConfiguration.SALE_INVOICE]: {},
    [TableNamesConfiguration.PURCHASE_INVOICE]: {},
    [TableNamesConfiguration.SALE_PAYMENT]: {},
    [TableNamesConfiguration.PROVIDER_PAYMENT]: {},
  };

  const existingCompanies = await knex(TableNamesConfiguration.COMPANY).select('id');
  const existingSequences = await knex(TableNamesConfiguration.COMPANY_SEQUENCE).whereIn('company_id', existingCompanies.map((company) => company.id));

  existingSequences.forEach((sequence) => {
    entityToCompanyMap[sequence.entity][sequence.companyId] = true;
  });

  await Promise.all(existingCompanies.map(async (company) => {
    await Promise.all(Object.keys(entityToPrefixMap).map(async (entity) => {
      if (!entityToCompanyMap[entity][company.id]) {
        await knex(TableNamesConfiguration.COMPANY_SEQUENCE).insert({
          companyId: company.id,
          prefix: entityToPrefixMap[entity as keyof typeof entityToPrefixMap],
          entity,
          nextValue: 1,
          disabledAt: null,
        });
      }
    }));
  }));
}

export async function down(knex: Knex): Promise<void> {
  // Drop the trigger and the trigger function
  await knex.raw(`
    DROP TRIGGER IF EXISTS company_sequence_trigger ON ${TableNamesConfiguration.COMPANY};
  `);
  await knex.raw(`
    DROP FUNCTION IF EXISTS add_default_company_sequence();
  `);
}
