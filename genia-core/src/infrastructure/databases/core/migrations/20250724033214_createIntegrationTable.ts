import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

import MigrationUtility from '../utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.INTEGRATION, async (table: Knex.CreateTableBuilder) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.text('token').notNullable();
    table.enum('tokenType', ['bearer', 'basic', 'refresh'], { useNative: true, enumName: 'integration_token_type_enum' }).notNullable();
    table.enum('type', [
      'gmail', 'gcalendar', 'whatsapp',
    ], { useNative: true, enumName: 'integration_type_enum' }).notNullable();
    table.jsonb('params').nullable();
    table.uuid('companyId').notNullable().references('id')
      .inTable(TableNamesConfiguration.COMPANY)
      .onDelete('CASCADE')
      .index();

    MigrationUtility.setTimeStamps(table, knex);
  });

  await MigrationUtility.autoUpdateTrigger(TableNamesConfiguration.INTEGRATION, knex);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.INTEGRATION);
  await knex.raw('DROP TYPE IF EXISTS integration_type_enum');
  await knex.raw('DROP TYPE IF EXISTS integration_token_type_enum');
}
