import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

import MigrationUtility from '../utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.SALE_ORDER_ITEM, async (table: Knex.CreateTableBuilder) => {
    table.uuid('sale_order_id').notNullable()
      .index()
      .references('id')
      .inTable(TableNamesConfiguration.SALE_ORDER)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
    table.uuid('catalog_id').notNullable().index();
    table.string('product_id').notNullable().index();
    table.string('name', 200).notNullable();
    table.integer('quantity').notNullable();
    table.decimal('unit_price', 14, 4).notNullable().defaultTo(0);
    table.decimal('unit_price_after_discount', 14, 4).notNullable().defaultTo(0);
    table.decimal('unit_price_after_discount_and_taxes', 14, 4).notNullable().defaultTo(0);
    table.decimal('subtotal', 14, 4).notNullable().defaultTo(0);
    table.decimal('total', 14, 4).notNullable().defaultTo(0);
    table.enum('discount_type', ['percentage', 'amount'], {
      useNative: true,
      enumName: 'sale_order_discount_type_enum',
    }).nullable();
    table.decimal('discount_value', 14, 4).nullable().defaultTo(0);
    table.jsonb('taxes').notNullable().defaultTo('[]');

    table.primary(['sale_order_id', 'catalog_id']);

    MigrationUtility.setTimeStamps(table, knex);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable(TableNamesConfiguration.SALE_ORDER_ITEM);
  await knex.raw('DROP TYPE IF EXISTS sale_order_discount_type_enum');
}
