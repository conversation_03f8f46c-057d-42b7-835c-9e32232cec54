import type { K<PERSON> } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.INVENTORY, (table) => {
    table.integer('restricted_stock').defaultTo(0).notNullable();
  });

  await knex.raw('DROP FUNCTION IF EXISTS inventory_search');
  await knex.raw('DROP FUNCTION IF EXISTS inventory_search_by_company');

  await knex.raw(`
    CREATE OR REPLACE FUNCTION inventory_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.INVENTORY} AS $$
    BEGIN
      RETURN QUERY SELECT * FROM inventory_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION inventory_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.INVENTORY} AS $$
    BEGIN      
      RETURN QUERY
      WITH ordered_results AS (
        SELECT 
          inventory_id, 
          SUM(similarity(indexed_text, search_term)) AS total_similarity
        FROM ${TableNamesConfiguration.INVENTORY_INDEX} 
        WHERE indexed_text % search_term
          AND company_id = company_id_for_searching
        GROUP BY inventory_id
        ORDER BY total_similarity DESC
        LIMIT 50
      )
      SELECT 
        inventory.*
      FROM 
        ordered_results
      JOIN 
        inventory ON ordered_results.inventory_id = inventory.id
      WHERE 
        ordered_results.total_similarity > 0.1
      ORDER BY
        ordered_results.total_similarity DESC;
    END;
    $$ LANGUAGE plpgsql STABLE SET pg_trgm.similarity_threshold = 0.05;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.INVENTORY, (table) => {
    table.dropColumn('restricted_stock');
  });

  await knex.raw('DROP FUNCTION IF EXISTS inventory_search');
  await knex.raw('DROP FUNCTION IF EXISTS inventory_search_by_company');

  await knex.raw(`
    CREATE OR REPLACE FUNCTION inventory_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.INVENTORY} AS $$
    BEGIN
      RETURN QUERY SELECT * FROM inventory_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION inventory_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.INVENTORY} AS $$
    BEGIN      
      RETURN QUERY
      WITH ordered_results AS (
        SELECT 
          inventory_id, 
          SUM(similarity(indexed_text, search_term)) AS total_similarity
        FROM ${TableNamesConfiguration.INVENTORY_INDEX} 
        WHERE indexed_text % search_term
          AND company_id = company_id_for_searching
        GROUP BY inventory_id
        ORDER BY total_similarity DESC
        LIMIT 50
      )
      SELECT 
        inventory.*
      FROM 
        ordered_results
      JOIN 
        inventory ON ordered_results.inventory_id = inventory.id
      WHERE 
        ordered_results.total_similarity > 0.1
      ORDER BY
        ordered_results.total_similarity DESC;
    END;
    $$ LANGUAGE plpgsql STABLE SET pg_trgm.similarity_threshold = 0.05;
  `);
}
