import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import MigrationUtility from '#infrastructure/databases/core/utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.PURCHASE_ORDER_ITEM, async (table: Knex.CreateTableBuilder) => {
    table.uuid('purchase_order_id').notNullable()
      .index()
      .references('id')
      .inTable(TableNamesConfiguration.PURCHASE_ORDER)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
    table.uuid('reference_id').notNullable().index();
    table.string('name', 200).notNullable();
    table.string('product_id', 40).notNullable().index();
    table.integer('quantity').notNullable();
    table.decimal('unit_price', 14, 4).notNullable().defaultTo(0);
    table.decimal('unit_price_after_discount', 14, 4).notNullable().defaultTo(0);
    table.decimal('unit_price_after_discount_and_taxes', 14, 4).notNullable().defaultTo(0);
    table.decimal('subtotal', 14, 4).notNullable().defaultTo(0);
    table.decimal('total', 14, 4).notNullable().defaultTo(0);
    table.enum('discount_type', ['percentage', 'amount'], {
      useNative: true,
      enumName: 'purchase_discount_type',
    }).nullable();
    table.decimal('discount_value', 14, 4).nullable().defaultTo(0);

    table.uuid('inventory_id').nullable()
      .index()
      .references('id')
      .inTable(TableNamesConfiguration.INVENTORY)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');

    table.jsonb('taxes').notNullable().defaultTo('[]');

    table.primary(['purchase_order_id', 'reference_id']);

    MigrationUtility.setTimeStamps(table, knex);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable(TableNamesConfiguration.PURCHASE_ORDER_ITEM);
  await knex.raw('DROP TYPE IF EXISTS purchase_discount_type');
}
