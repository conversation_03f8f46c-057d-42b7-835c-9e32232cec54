import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  // Create the countries table
  await knex.schema.createTable(TableNamesConfiguration.COUNTRY, (table) => {
    table.string('iso_code', 2).primary().notNullable().comment('ISO 3166-1 alpha-2 country code');
    table.string('name', 100).notNullable().comment('Country name');
    table.string('phone_code', 10).nullable().comment('International phone code');
    table.string('flag', 255).nullable().comment('Country flag emoji or URL');
  });

  // Insert countries data with ISO codes, names, phone codes and flags
  await knex(TableNamesConfiguration.COUNTRY).insert([
    {
      iso_code: 'AD', name: 'Andorra', phone_code: '+376', flag: '🇦🇩',
    },
    {
      iso_code: 'AE', name: 'United Arab Emirates', phone_code: '+971', flag: '🇦🇪',
    },
    {
      iso_code: 'AF', name: 'Afghanistan', phone_code: '+93', flag: '🇦🇫',
    },
    {
      iso_code: 'AG', name: 'Antigua and Barbuda', phone_code: '+1', flag: '🇦🇬',
    },
    {
      iso_code: 'AI', name: 'Anguilla', phone_code: '+1', flag: '🇦🇮',
    },
    {
      iso_code: 'AL', name: 'Albania', phone_code: '+355', flag: '🇦🇱',
    },
    {
      iso_code: 'AM', name: 'Armenia', phone_code: '+374', flag: '🇦🇲',
    },
    {
      iso_code: 'AO', name: 'Angola', phone_code: '+244', flag: '🇦🇴',
    },
    {
      iso_code: 'AR', name: 'Argentina', phone_code: '+54', flag: '🇦🇷',
    },
    {
      iso_code: 'AS', name: 'American Samoa', phone_code: '+1', flag: '🇦🇸',
    },
    {
      iso_code: 'AT', name: 'Austria', phone_code: '+43', flag: '🇦🇹',
    },
    {
      iso_code: 'AU', name: 'Australia', phone_code: '+61', flag: '🇦🇺',
    },
    {
      iso_code: 'AW', name: 'Aruba', phone_code: '+297', flag: '🇦🇼',
    },
    {
      iso_code: 'AX', name: 'Åland Islands', phone_code: '+358', flag: '🇦🇽',
    },
    {
      iso_code: 'AZ', name: 'Azerbaijan', phone_code: '+994', flag: '🇦🇿',
    },
    {
      iso_code: 'BA', name: 'Bosnia and Herzegovina', phone_code: '+387', flag: '🇧🇦',
    },
    {
      iso_code: 'BB', name: 'Barbados', phone_code: '+1', flag: '🇧🇧',
    },
    {
      iso_code: 'BD', name: 'Bangladesh', phone_code: '+880', flag: '🇧🇩',
    },
    {
      iso_code: 'BE', name: 'Belgium', phone_code: '+32', flag: '🇧🇪',
    },
    {
      iso_code: 'BF', name: 'Burkina Faso', phone_code: '+226', flag: '🇧🇫',
    },
    {
      iso_code: 'BG', name: 'Bulgaria', phone_code: '+359', flag: '🇧🇬',
    },
    {
      iso_code: 'BH', name: 'Bahrain', phone_code: '+973', flag: '🇧🇭',
    },
    {
      iso_code: 'BI', name: 'Burundi', phone_code: '+257', flag: '🇧🇮',
    },
    {
      iso_code: 'BJ', name: 'Benin', phone_code: '+229', flag: '🇧🇯',
    },
    {
      iso_code: 'BL', name: 'Saint Barthélemy', phone_code: '+590', flag: '🇧🇱',
    },
    {
      iso_code: 'BM', name: 'Bermuda', phone_code: '+1', flag: '🇧🇲',
    },
    {
      iso_code: 'BN', name: 'Brunei Darussalam', phone_code: '+673', flag: '🇧🇳',
    },
    {
      iso_code: 'BO', name: 'Bolivia', phone_code: '+591', flag: '🇧🇴',
    },
    {
      iso_code: 'BQ', name: 'Bonaire, Sint Eustatius and Saba', phone_code: '+599', flag: '🇧🇶',
    },
    {
      iso_code: 'BR', name: 'Brazil', phone_code: '+55', flag: '🇧🇷',
    },
    {
      iso_code: 'BS', name: 'Bahamas', phone_code: '+1', flag: '🇧🇸',
    },
    {
      iso_code: 'BT', name: 'Bhutan', phone_code: '+975', flag: '🇧🇹',
    },
    {
      iso_code: 'BV', name: 'Bouvet Island', phone_code: '+47', flag: '🇧🇻',
    },
    {
      iso_code: 'BW', name: 'Botswana', phone_code: '+267', flag: '🇧🇼',
    },
    {
      iso_code: 'BY', name: 'Belarus', phone_code: '+375', flag: '🇧🇾',
    },
    {
      iso_code: 'BZ', name: 'Belize', phone_code: '+501', flag: '🇧🇿',
    },
    {
      iso_code: 'CA', name: 'Canada', phone_code: '+1', flag: '🇨🇦',
    },
    {
      iso_code: 'CC', name: 'Cocos (Keeling) Islands', phone_code: '+61', flag: '🇨🇨',
    },
    {
      iso_code: 'CD', name: 'Congo, Democratic Republic of the', phone_code: '+243', flag: '🇨🇩',
    },
    {
      iso_code: 'CF', name: 'Central African Republic', phone_code: '+236', flag: '🇨🇫',
    },
    {
      iso_code: 'CG', name: 'Congo', phone_code: '+242', flag: '🇨🇬',
    },
    {
      iso_code: 'CH', name: 'Switzerland', phone_code: '+41', flag: '🇨🇭',
    },
    {
      iso_code: 'CI', name: 'Côte d\'Ivoire', phone_code: '+225', flag: '🇨🇮',
    },
    {
      iso_code: 'CK', name: 'Cook Islands', phone_code: '+682', flag: '🇨🇰',
    },
    {
      iso_code: 'CL', name: 'Chile', phone_code: '+56', flag: '🇨🇱',
    },
    {
      iso_code: 'CM', name: 'Cameroon', phone_code: '+237', flag: '🇨🇲',
    },
    {
      iso_code: 'CN', name: 'China', phone_code: '+86', flag: '🇨🇳',
    },
    {
      iso_code: 'CO', name: 'Colombia', phone_code: '+57', flag: '🇨🇴',
    },
    {
      iso_code: 'CR', name: 'Costa Rica', phone_code: '+506', flag: '🇨🇷',
    },
    {
      iso_code: 'CU', name: 'Cuba', phone_code: '+53', flag: '🇨🇺',
    },
    {
      iso_code: 'CV', name: 'Cape Verde', phone_code: '+238', flag: '🇨🇻',
    },
    {
      iso_code: 'CW', name: 'Curaçao', phone_code: '+599', flag: '🇨🇼',
    },
    {
      iso_code: 'CX', name: 'Christmas Island', phone_code: '+61', flag: '🇨🇽',
    },
    {
      iso_code: 'CY', name: 'Cyprus', phone_code: '+357', flag: '🇨🇾',
    },
    {
      iso_code: 'CZ', name: 'Czech Republic', phone_code: '+420', flag: '🇨🇿',
    },
    {
      iso_code: 'DE', name: 'Germany', phone_code: '+49', flag: '🇩🇪',
    },
    {
      iso_code: 'DJ', name: 'Djibouti', phone_code: '+253', flag: '🇩🇯',
    },
    {
      iso_code: 'DK', name: 'Denmark', phone_code: '+45', flag: '🇩🇰',
    },
    {
      iso_code: 'DM', name: 'Dominica', phone_code: '+1', flag: '🇩🇲',
    },
    {
      iso_code: 'DO', name: 'Dominican Republic', phone_code: '+1', flag: '🇩🇴',
    },
    {
      iso_code: 'DZ', name: 'Algeria', phone_code: '+213', flag: '🇩🇿',
    },
    {
      iso_code: 'EC', name: 'Ecuador', phone_code: '+593', flag: '🇪🇨',
    },
    {
      iso_code: 'EE', name: 'Estonia', phone_code: '+372', flag: '🇪🇪',
    },
    {
      iso_code: 'EG', name: 'Egypt', phone_code: '+20', flag: '🇪🇬',
    },
    {
      iso_code: 'EH', name: 'Western Sahara', phone_code: '+212', flag: '🇪🇭',
    },
    {
      iso_code: 'ER', name: 'Eritrea', phone_code: '+291', flag: '🇪🇷',
    },
    {
      iso_code: 'ES', name: 'Spain', phone_code: '+34', flag: '🇪🇸',
    },
    {
      iso_code: 'ET', name: 'Ethiopia', phone_code: '+251', flag: '🇪🇹',
    },
    {
      iso_code: 'FI', name: 'Finland', phone_code: '+358', flag: '🇫🇮',
    },
    {
      iso_code: 'FJ', name: 'Fiji', phone_code: '+679', flag: '🇫🇯',
    },
    {
      iso_code: 'FK', name: 'Falkland Islands (Malvinas)', phone_code: '+500', flag: '🇫🇰',
    },
    {
      iso_code: 'FM', name: 'Micronesia, Federated States of', phone_code: '+691', flag: '🇫🇲',
    },
    {
      iso_code: 'FO', name: 'Faroe Islands', phone_code: '+298', flag: '🇫🇴',
    },
    {
      iso_code: 'FR', name: 'France', phone_code: '+33', flag: '🇫🇷',
    },
    {
      iso_code: 'GA', name: 'Gabon', phone_code: '+241', flag: '🇬🇦',
    },
    {
      iso_code: 'GB', name: 'United Kingdom', phone_code: '+44', flag: '🇬🇧',
    },
    {
      iso_code: 'GD', name: 'Grenada', phone_code: '+1', flag: '🇬🇩',
    },
    {
      iso_code: 'GE', name: 'Georgia', phone_code: '+995', flag: '🇬🇪',
    },
    {
      iso_code: 'GF', name: 'French Guiana', phone_code: '+594', flag: '🇬🇫',
    },
    {
      iso_code: 'GG', name: 'Guernsey', phone_code: '+44', flag: '🇬🇬',
    },
    {
      iso_code: 'GH', name: 'Ghana', phone_code: '+233', flag: '🇬🇭',
    },
    {
      iso_code: 'GI', name: 'Gibraltar', phone_code: '+350', flag: '🇬🇮',
    },
    {
      iso_code: 'GL', name: 'Greenland', phone_code: '+299', flag: '🇬🇱',
    },
    {
      iso_code: 'GM', name: 'Gambia', phone_code: '+220', flag: '🇬🇲',
    },
    {
      iso_code: 'GN', name: 'Guinea', phone_code: '+224', flag: '🇬🇳',
    },
    {
      iso_code: 'GP', name: 'Guadeloupe', phone_code: '+590', flag: '🇬🇵',
    },
    {
      iso_code: 'GQ', name: 'Equatorial Guinea', phone_code: '+240', flag: '🇬🇶',
    },
    {
      iso_code: 'GR', name: 'Greece', phone_code: '+30', flag: '🇬🇷',
    },
    {
      iso_code: 'GS', name: 'South Georgia and the South Sandwich Islands', phone_code: '+500', flag: '🇬🇸',
    },
    {
      iso_code: 'GT', name: 'Guatemala', phone_code: '+502', flag: '🇬🇹',
    },
    {
      iso_code: 'GU', name: 'Guam', phone_code: '+1', flag: '🇬🇺',
    },
    {
      iso_code: 'GW', name: 'Guinea-Bissau', phone_code: '+245', flag: '🇬🇼',
    },
    {
      iso_code: 'GY', name: 'Guyana', phone_code: '+592', flag: '🇬🇾',
    },
    {
      iso_code: 'HK', name: 'Hong Kong', phone_code: '+852', flag: '🇭🇰',
    },
    {
      iso_code: 'HM', name: 'Heard Island and McDonald Islands', phone_code: '+672', flag: '🇭🇲',
    },
    {
      iso_code: 'HN', name: 'Honduras', phone_code: '+504', flag: '🇭🇳',
    },
    {
      iso_code: 'HR', name: 'Croatia', phone_code: '+385', flag: '🇭🇷',
    },
    {
      iso_code: 'HT', name: 'Haiti', phone_code: '+509', flag: '🇭🇹',
    },
    {
      iso_code: 'HU', name: 'Hungary', phone_code: '+36', flag: '🇭🇺',
    },
    {
      iso_code: 'ID', name: 'Indonesia', phone_code: '+62', flag: '🇮🇩',
    },
    {
      iso_code: 'IE', name: 'Ireland', phone_code: '+353', flag: '🇮🇪',
    },
    {
      iso_code: 'IL', name: 'Israel', phone_code: '+972', flag: '🇮🇱',
    },
    {
      iso_code: 'IM', name: 'Isle of Man', phone_code: '+44', flag: '🇮🇲',
    },
    {
      iso_code: 'IN', name: 'India', phone_code: '+91', flag: '🇮🇳',
    },
    {
      iso_code: 'IO', name: 'British Indian Ocean Territory', phone_code: '+246', flag: '🇮🇴',
    },
    {
      iso_code: 'IQ', name: 'Iraq', phone_code: '+964', flag: '🇮🇶',
    },
    {
      iso_code: 'IR', name: 'Iran, Islamic Republic of', phone_code: '+98', flag: '🇮🇷',
    },
    {
      iso_code: 'IS', name: 'Iceland', phone_code: '+354', flag: '🇮🇸',
    },
    {
      iso_code: 'IT', name: 'Italy', phone_code: '+39', flag: '🇮🇹',
    },
    {
      iso_code: 'JE', name: 'Jersey', phone_code: '+44', flag: '🇯🇪',
    },
    {
      iso_code: 'JM', name: 'Jamaica', phone_code: '+1', flag: '🇯🇲',
    },
    {
      iso_code: 'JO', name: 'Jordan', phone_code: '+962', flag: '🇯🇴',
    },
    {
      iso_code: 'JP', name: 'Japan', phone_code: '+81', flag: '🇯🇵',
    },
    {
      iso_code: 'KE', name: 'Kenya', phone_code: '+254', flag: '🇰🇪',
    },
    {
      iso_code: 'KG', name: 'Kyrgyzstan', phone_code: '+996', flag: '🇰🇬',
    },
    {
      iso_code: 'KH', name: 'Cambodia', phone_code: '+855', flag: '🇰🇭',
    },
    {
      iso_code: 'KI', name: 'Kiribati', phone_code: '+686', flag: '🇰🇮',
    },
    {
      iso_code: 'KM', name: 'Comoros', phone_code: '+269', flag: '🇰🇲',
    },
    {
      iso_code: 'KN', name: 'Saint Kitts and Nevis', phone_code: '+1', flag: '🇰🇳',
    },
    {
      iso_code: 'KP', name: 'Korea, Democratic People\'s Republic of', phone_code: '+850', flag: '🇰🇵',
    },
    {
      iso_code: 'KR', name: 'Korea, Republic of', phone_code: '+82', flag: '🇰🇷',
    },
    {
      iso_code: 'KW', name: 'Kuwait', phone_code: '+965', flag: '🇰🇼',
    },
    {
      iso_code: 'KY', name: 'Cayman Islands', phone_code: '+1', flag: '🇰🇾',
    },
    {
      iso_code: 'KZ', name: 'Kazakhstan', phone_code: '+7', flag: '🇰🇿',
    },
    {
      iso_code: 'LA', name: 'Lao People\'s Democratic Republic', phone_code: '+856', flag: '🇱🇦',
    },
    {
      iso_code: 'LB', name: 'Lebanon', phone_code: '+961', flag: '🇱🇧',
    },
    {
      iso_code: 'LC', name: 'Saint Lucia', phone_code: '+1', flag: '🇱🇨',
    },
    {
      iso_code: 'LI', name: 'Liechtenstein', phone_code: '+423', flag: '🇱🇮',
    },
    {
      iso_code: 'LK', name: 'Sri Lanka', phone_code: '+94', flag: '🇱🇰',
    },
    {
      iso_code: 'LR', name: 'Liberia', phone_code: '+231', flag: '🇱🇷',
    },
    {
      iso_code: 'LS', name: 'Lesotho', phone_code: '+266', flag: '🇱🇸',
    },
    {
      iso_code: 'LT', name: 'Lithuania', phone_code: '+370', flag: '🇱🇹',
    },
    {
      iso_code: 'LU', name: 'Luxembourg', phone_code: '+352', flag: '🇱🇺',
    },
    {
      iso_code: 'LV', name: 'Latvia', phone_code: '+371', flag: '🇱🇻',
    },
    {
      iso_code: 'LY', name: 'Libya', phone_code: '+218', flag: '🇱🇾',
    },
    {
      iso_code: 'MA', name: 'Morocco', phone_code: '+212', flag: '🇲🇦',
    },
    {
      iso_code: 'MC', name: 'Monaco', phone_code: '+377', flag: '🇲🇨',
    },
    {
      iso_code: 'MD', name: 'Moldova', phone_code: '+373', flag: '🇲🇩',
    },
    {
      iso_code: 'ME', name: 'Montenegro', phone_code: '+382', flag: '🇲🇪',
    },
    {
      iso_code: 'MF', name: 'Saint Martin (French part)', phone_code: '+590', flag: '🇲🇫',
    },
    {
      iso_code: 'MG', name: 'Madagascar', phone_code: '+261', flag: '🇲🇬',
    },
    {
      iso_code: 'MH', name: 'Marshall Islands', phone_code: '+692', flag: '🇲🇭',
    },
    {
      iso_code: 'MK', name: 'North Macedonia', phone_code: '+389', flag: '🇲🇰',
    },
    {
      iso_code: 'ML', name: 'Mali', phone_code: '+223', flag: '🇲🇱',
    },
    {
      iso_code: 'MM', name: 'Myanmar', phone_code: '+95', flag: '🇲🇲',
    },
    {
      iso_code: 'MN', name: 'Mongolia', phone_code: '+976', flag: '🇲🇳',
    },
    {
      iso_code: 'MO', name: 'Macao', phone_code: '+853', flag: '🇲🇴',
    },
    {
      iso_code: 'MP', name: 'Northern Mariana Islands', phone_code: '+1', flag: '🇲🇵',
    },
    {
      iso_code: 'MQ', name: 'Martinique', phone_code: '+596', flag: '🇲🇶',
    },
    {
      iso_code: 'MR', name: 'Mauritania', phone_code: '+222', flag: '🇲🇷',
    },
    {
      iso_code: 'MS', name: 'Montserrat', phone_code: '+1', flag: '🇲🇸',
    },
    {
      iso_code: 'MT', name: 'Malta', phone_code: '+356', flag: '🇲🇹',
    },
    {
      iso_code: 'MU', name: 'Mauritius', phone_code: '+230', flag: '🇲🇺',
    },
    {
      iso_code: 'MV', name: 'Maldives', phone_code: '+960', flag: '🇲🇻',
    },
    {
      iso_code: 'MW', name: 'Malawi', phone_code: '+265', flag: '🇲🇼',
    },
    {
      iso_code: 'MX', name: 'Mexico', phone_code: '+52', flag: '🇲🇽',
    },
    {
      iso_code: 'MY', name: 'Malaysia', phone_code: '+60', flag: '🇲🇾',
    },
    {
      iso_code: 'MZ', name: 'Mozambique', phone_code: '+258', flag: '🇲🇿',
    },
    {
      iso_code: 'NA', name: 'Namibia', phone_code: '+264', flag: '🇳🇦',
    },
    {
      iso_code: 'NC', name: 'New Caledonia', phone_code: '+687', flag: '🇳🇨',
    },
    {
      iso_code: 'NE', name: 'Niger', phone_code: '+227', flag: '🇳🇪',
    },
    {
      iso_code: 'NF', name: 'Norfolk Island', phone_code: '+672', flag: '🇳🇫',
    },
    {
      iso_code: 'NG', name: 'Nigeria', phone_code: '+234', flag: '🇳🇬',
    },
    {
      iso_code: 'NI', name: 'Nicaragua', phone_code: '+505', flag: '🇳🇮',
    },
    {
      iso_code: 'NL', name: 'Netherlands', phone_code: '+31', flag: '🇳🇱',
    },
    {
      iso_code: 'NO', name: 'Norway', phone_code: '+47', flag: '🇳🇴',
    },
    {
      iso_code: 'NP', name: 'Nepal', phone_code: '+977', flag: '🇳🇵',
    },
    {
      iso_code: 'NR', name: 'Nauru', phone_code: '+674', flag: '🇳🇷',
    },
    {
      iso_code: 'NU', name: 'Niue', phone_code: '+683', flag: '🇳🇺',
    },
    {
      iso_code: 'NZ', name: 'New Zealand', phone_code: '+64', flag: '🇳🇿',
    },
    {
      iso_code: 'OM', name: 'Oman', phone_code: '+968', flag: '🇴🇲',
    },
    {
      iso_code: 'PA', name: 'Panama', phone_code: '+507', flag: '🇵🇦',
    },
    {
      iso_code: 'PE', name: 'Peru', phone_code: '+51', flag: '🇵🇪',
    },
    {
      iso_code: 'PF', name: 'French Polynesia', phone_code: '+689', flag: '🇵🇫',
    },
    {
      iso_code: 'PG', name: 'Papua New Guinea', phone_code: '+675', flag: '🇵🇬',
    },
    {
      iso_code: 'PH', name: 'Philippines', phone_code: '+63', flag: '🇵🇭',
    },
    {
      iso_code: 'PK', name: 'Pakistan', phone_code: '+92', flag: '🇵🇰',
    },
    {
      iso_code: 'PL', name: 'Poland', phone_code: '+48', flag: '🇵🇱',
    },
    {
      iso_code: 'PM', name: 'Saint Pierre and Miquelon', phone_code: '+508', flag: '🇵🇲',
    },
    {
      iso_code: 'PN', name: 'Pitcairn', phone_code: '+64', flag: '🇵🇳',
    },
    {
      iso_code: 'PR', name: 'Puerto Rico', phone_code: '+1', flag: '🇵🇷',
    },
    {
      iso_code: 'PS', name: 'Palestine, State of', phone_code: '+970', flag: '🇵🇸',
    },
    {
      iso_code: 'PT', name: 'Portugal', phone_code: '+351', flag: '🇵🇹',
    },
    {
      iso_code: 'PW', name: 'Palau', phone_code: '+680', flag: '🇵🇼',
    },
    {
      iso_code: 'PY', name: 'Paraguay', phone_code: '+595', flag: '🇵🇾',
    },
    {
      iso_code: 'QA', name: 'Qatar', phone_code: '+974', flag: '🇶🇦',
    },
    {
      iso_code: 'RE', name: 'Réunion', phone_code: '+262', flag: '🇷🇪',
    },
    {
      iso_code: 'RO', name: 'Romania', phone_code: '+40', flag: '🇷🇴',
    },
    {
      iso_code: 'RS', name: 'Serbia', phone_code: '+381', flag: '🇷🇸',
    },
    {
      iso_code: 'RU', name: 'Russian Federation', phone_code: '+7', flag: '🇷🇺',
    },
    {
      iso_code: 'RW', name: 'Rwanda', phone_code: '+250', flag: '🇷🇼',
    },
    {
      iso_code: 'SA', name: 'Saudi Arabia', phone_code: '+966', flag: '🇸🇦',
    },
    {
      iso_code: 'SB', name: 'Solomon Islands', phone_code: '+677', flag: '🇸🇧',
    },
    {
      iso_code: 'SC', name: 'Seychelles', phone_code: '+248', flag: '🇸🇨',
    },
    {
      iso_code: 'SD', name: 'Sudan', phone_code: '+249', flag: '🇸🇩',
    },
    {
      iso_code: 'SE', name: 'Sweden', phone_code: '+46', flag: '🇸🇪',
    },
    {
      iso_code: 'SG', name: 'Singapore', phone_code: '+65', flag: '🇸🇬',
    },
    {
      iso_code: 'SH', name: 'Saint Helena, Ascension and Tristan da Cunha', phone_code: '+290', flag: '🇸🇭',
    },
    {
      iso_code: 'SI', name: 'Slovenia', phone_code: '+386', flag: '🇸🇮',
    },
    {
      iso_code: 'SJ', name: 'Svalbard and Jan Mayen', phone_code: '+47', flag: '🇸🇯',
    },
    {
      iso_code: 'SK', name: 'Slovakia', phone_code: '+421', flag: '🇸🇰',
    },
    {
      iso_code: 'SL', name: 'Sierra Leone', phone_code: '+232', flag: '🇸🇱',
    },
    {
      iso_code: 'SM', name: 'San Marino', phone_code: '+378', flag: '🇸🇲',
    },
    {
      iso_code: 'SN', name: 'Senegal', phone_code: '+221', flag: '🇸🇳',
    },
    {
      iso_code: 'SO', name: 'Somalia', phone_code: '+252', flag: '🇸🇴',
    },
    {
      iso_code: 'SR', name: 'Suriname', phone_code: '+597', flag: '🇸🇷',
    },
    {
      iso_code: 'SS', name: 'South Sudan', phone_code: '+211', flag: '🇸🇸',
    },
    {
      iso_code: 'ST', name: 'Sao Tome and Principe', phone_code: '+239', flag: '🇸🇹',
    },
    {
      iso_code: 'SV', name: 'El Salvador', phone_code: '+503', flag: '🇸🇻',
    },
    {
      iso_code: 'SX', name: 'Sint Maarten (Dutch part)', phone_code: '+1', flag: '🇸🇽',
    },
    {
      iso_code: 'SY', name: 'Syrian Arab Republic', phone_code: '+963', flag: '🇸🇾',
    },
    {
      iso_code: 'SZ', name: 'Eswatini', phone_code: '+268', flag: '🇸🇿',
    },
    {
      iso_code: 'TC', name: 'Turks and Caicos Islands', phone_code: '+1', flag: '🇹🇨',
    },
    {
      iso_code: 'TD', name: 'Chad', phone_code: '+235', flag: '🇹🇩',
    },
    {
      iso_code: 'TF', name: 'French Southern Territories', phone_code: '+262', flag: '🇹🇫',
    },
    {
      iso_code: 'TG', name: 'Togo', phone_code: '+228', flag: '🇹🇬',
    },
    {
      iso_code: 'TH', name: 'Thailand', phone_code: '+66', flag: '🇹🇭',
    },
    {
      iso_code: 'TJ', name: 'Tajikistan', phone_code: '+992', flag: '🇹🇯',
    },
    {
      iso_code: 'TK', name: 'Tokelau', phone_code: '+690', flag: '🇹🇰',
    },
    {
      iso_code: 'TL', name: 'Timor-Leste', phone_code: '+670', flag: '🇹🇱',
    },
    {
      iso_code: 'TM', name: 'Turkmenistan', phone_code: '+993', flag: '🇹🇲',
    },
    {
      iso_code: 'TN', name: 'Tunisia', phone_code: '+216', flag: '🇹🇳',
    },
    {
      iso_code: 'TO', name: 'Tonga', phone_code: '+676', flag: '🇹🇴',
    },
    {
      iso_code: 'TR', name: 'Turkey', phone_code: '+90', flag: '🇹🇷',
    },
    {
      iso_code: 'TT', name: 'Trinidad and Tobago', phone_code: '+1', flag: '🇹🇹',
    },
    {
      iso_code: 'TV', name: 'Tuvalu', phone_code: '+688', flag: '🇹🇻',
    },
    {
      iso_code: 'TW', name: 'Taiwan', phone_code: '+886', flag: '🇹🇼',
    },
    {
      iso_code: 'TZ', name: 'Tanzania, United Republic of', phone_code: '+255', flag: '🇹🇿',
    },
    {
      iso_code: 'UA', name: 'Ukraine', phone_code: '+380', flag: '🇺🇦',
    },
    {
      iso_code: 'UG', name: 'Uganda', phone_code: '+256', flag: '🇺🇬',
    },
    {
      iso_code: 'UM', name: 'United States Minor Outlying Islands', phone_code: '+1', flag: '🇺🇲',
    },
    {
      iso_code: 'US', name: 'United States', phone_code: '+1', flag: '🇺🇸',
    },
    {
      iso_code: 'UY', name: 'Uruguay', phone_code: '+598', flag: '🇺🇾',
    },
    {
      iso_code: 'UZ', name: 'Uzbekistan', phone_code: '+998', flag: '🇺🇿',
    },
    {
      iso_code: 'VA', name: 'Holy See (Vatican City State)', phone_code: '+39', flag: '🇻🇦',
    },
    {
      iso_code: 'VC', name: 'Saint Vincent and the Grenadines', phone_code: '+1', flag: '🇻🇨',
    },
    {
      iso_code: 'VE', name: 'Venezuela', phone_code: '+58', flag: '🇻🇪',
    },
    {
      iso_code: 'VG', name: 'Virgin Islands, British', phone_code: '+1', flag: '🇻🇬',
    },
    {
      iso_code: 'VI', name: 'Virgin Islands, U.S.', phone_code: '+1', flag: '🇻🇮',
    },
    {
      iso_code: 'VN', name: 'Vietnam', phone_code: '+84', flag: '🇻🇳',
    },
    {
      iso_code: 'VU', name: 'Vanuatu', phone_code: '+678', flag: '🇻🇺',
    },
    {
      iso_code: 'WF', name: 'Wallis and Futuna', phone_code: '+681', flag: '🇼🇫',
    },
    {
      iso_code: 'WS', name: 'Samoa', phone_code: '+685', flag: '🇼🇸',
    },
    {
      iso_code: 'YE', name: 'Yemen', phone_code: '+967', flag: '🇾🇪',
    },
    {
      iso_code: 'YT', name: 'Mayotte', phone_code: '+262', flag: '🇾🇹',
    },
    {
      iso_code: 'ZA', name: 'South Africa', phone_code: '+27', flag: '🇿🇦',
    },
    {
      iso_code: 'ZM', name: 'Zambia', phone_code: '+260', flag: '🇿🇲',
    },
    {
      iso_code: 'ZW', name: 'Zimbabwe', phone_code: '+263', flag: '🇿🇼',
    },
  ]);
}

export async function down(knex: Knex): Promise<void> {
  // Drop the countries table
  await knex.schema.dropTableIfExists(TableNamesConfiguration.COUNTRY);
}
