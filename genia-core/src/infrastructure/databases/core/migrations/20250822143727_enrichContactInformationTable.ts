import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.CONTACT_INFORMATION, async (table: Knex.CreateTableBuilder) => {
    table.string('main_email').nullable();
    table.string('main_phone_number').nullable();
    table.string('main_whatsapp').nullable();
    table.string('main_address').nullable();
    table.string('representative_name').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.table(TableNamesConfiguration.CONTACT_INFORMATION, (table) => {
    table.dropColumn('main_email');
    table.dropColumn('main_phone_number');
    table.dropColumn('main_whatsapp');
    table.dropColumn('main_address');
    table.dropColumn('representative_name');
  });
}
