import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import MigrationUtility from '#infrastructure/databases/core/utilites/Migration.Utility';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.CONNECTION_INVITATION, async (table) => {
    table.uuid('id').primary().index();
    table.uuid('company_id').notNullable().references('id').inTable(TableNamesConfiguration.COMPANY)
      .onDelete('CASCADE');
    table.uuid('invited_company_id').nullable().references('id').inTable(TableNamesConfiguration.COMPANY)
      .onDelete('CASCADE');
    table.string('email').nullable();
    table.string('phone_number').nullable();
    table.enum('type', ['provider', 'client'], { useNative: true, enumName: 'invitation_type_enum' }).notNullable();
    table.enum('state', ['accepted', 'rejected', 'pending'], { useNative: true, enumName: 'invitation_state_enum' }).notNullable().defaultTo('pending');

    MigrationUtility.setTimeStamps(table, knex);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.CONNECTION_INVITATION);
  await knex.raw('DROP TYPE IF EXISTS invitation_type_enum');
  await knex.raw('DROP TYPE IF EXISTS invitation_state_enum');
}
