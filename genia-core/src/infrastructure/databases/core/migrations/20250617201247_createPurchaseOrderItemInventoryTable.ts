import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import { dbInventoryPurchaseOrderItem } from '#infrastructure/databases/core/types';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.PURCHASE_ORDER_ITEM_INVENTORY, async (table: Knex.CreateTableBuilder) => {
    table.uuid('purchase_order_item_id').notNullable().index().references('id')
      .inTable(TableNamesConfiguration.PURCHASE_ORDER_ITEM)
      .onDelete('CASCADE')
      .onUpdate('CASCADE');
    table.uuid('inventory_id').notNullable().index().references('id')
      .inTable(TableNamesConfiguration.INVENTORY)
      .onDelete('CASCADE')
      .onUpdate('CASCADE');
    table.primary(['purchase_order_item_id', 'inventory_id']);
  });

  const purchaseOrderItemRows = await knex(TableNamesConfiguration.PURCHASE_ORDER_ITEM).select('id', 'inventory_id');

  const inventoryPurchaseOrderItems = purchaseOrderItemRows.map<dbInventoryPurchaseOrderItem>(({ id, inventoryId }) => ({
    inventoryId, purchaseOrderItemId: id,
  }));

  if (inventoryPurchaseOrderItems.length) {
    await knex(TableNamesConfiguration.PURCHASE_ORDER_ITEM_INVENTORY).insert(inventoryPurchaseOrderItems);
  }

  await knex.schema.alterTable(TableNamesConfiguration.PURCHASE_ORDER_ITEM, (table) => {
    table.dropForeign('inventory_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.PURCHASE_ORDER_ITEM, (table) => {
    table.foreign('inventory_id')
      .references('id')
      .inTable(TableNamesConfiguration.INVENTORY)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
  });

  await knex.schema.dropTableIfExists(TableNamesConfiguration.PURCHASE_ORDER_ITEM_INVENTORY);
}
