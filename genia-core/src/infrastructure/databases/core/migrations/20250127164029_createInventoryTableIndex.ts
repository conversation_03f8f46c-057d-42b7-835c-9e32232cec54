import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TableNamesConfiguration.INVENTORY_INDEX, async (table) => {
    table.uuid('inventory_id').notNullable().index();
    table.uuid('company_id').notNullable().index();
    table.text('indexed_text').notNullable();

    table.foreign('inventory_id').references('id').inTable(TableNamesConfiguration.INVENTORY).onDelete('CASCADE')
      .onUpdate('RESTRICT');

    table.foreign('company_id').references('id').inTable(TableNamesConfiguration.COMPANY).onDelete('CASCADE');

    await knex.raw(`CREATE INDEX inventory_index_indexed_text_trgm_idx ON ${TableNamesConfiguration.INVENTORY_INDEX} USING gin (indexed_text gin_trgm_ops)`);
  });

  await knex.raw(`
    CREATE OR REPLACE FUNCTION inventory_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.INVENTORY} AS $$
    BEGIN
      RETURN QUERY SELECT * FROM inventory_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION inventory_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.INVENTORY} AS $$
    BEGIN      
      RETURN QUERY
      WITH ordered_results AS (
        SELECT 
          inventory_id, 
          SUM(similarity(indexed_text, search_term)) AS total_similarity
        FROM ${TableNamesConfiguration.INVENTORY_INDEX} 
        WHERE indexed_text % search_term
          AND company_id = company_id_for_searching
        GROUP BY inventory_id
        ORDER BY total_similarity DESC
        LIMIT 50
      )
      SELECT 
        inventory.*
      FROM 
        ordered_results
      JOIN 
        inventory ON ordered_results.inventory_id = inventory.id
      WHERE 
        ordered_results.total_similarity > 0.1
      ORDER BY
        ordered_results.total_similarity DESC;
    END;
    $$ LANGUAGE plpgsql STABLE SET pg_trgm.similarity_threshold = 0.05;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('inventory_index');
  await knex.raw('DROP FUNCTION IF EXISTS inventory_search_by_company');
  await knex.raw('DROP FUNCTION IF EXISTS inventory_search');
}
