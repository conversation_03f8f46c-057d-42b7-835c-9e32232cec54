import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.SALE_ORDER, (table: Knex.AlterTableBuilder) => {
    table.uuid('related_purchase_order_id').nullable()
      .index()
      .references('id')
      .inTable(TableNamesConfiguration.PURCHASE_ORDER)
      .onDelete('RESTRICT')
      .onUpdate('RESTRICT');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.SALE_ORDER, (table: Knex.AlterTableBuilder) => {
    table.dropColumn('related_purchase_order_id');
  });
}
