import type { Knex } from 'knex';

import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  knex(TableNamesConfiguration.TAX).insert({
    name: 'IVA',
    value: 0.16,
    countryCode: 'MX',
    type: TaxType.PERCENTAGE,
    disabledAt: null,
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex(TableNamesConfiguration.TAX).where('name', 'IVA').del();
}
