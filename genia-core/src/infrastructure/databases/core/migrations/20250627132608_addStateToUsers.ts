import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.USER, async (table: Knex.CreateTableBuilder) => {
    table.enum('state', ['ACTIVE', 'DISABLED'], {
      useNative: true,
      enumName: 'user_state_enum',
    }).notNullable().defaultTo('ACTIVE');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.table(TableNamesConfiguration.USER, (table) => {
    table.dropColumn('state');
  });

  await knex.raw('DROP TYPE IF EXISTS user_state_enum');
}
