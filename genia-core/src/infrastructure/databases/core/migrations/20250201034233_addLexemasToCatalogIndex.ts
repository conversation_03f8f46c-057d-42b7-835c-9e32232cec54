import type { <PERSON><PERSON> } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  // Fake and empty table just for hasura to track specific search data
  await knex.schema.createTable(`${TableNamesConfiguration.CATALOG}_search_results`, (table: Knex.CreateTableBuilder) => {
    table.inherits(TableNamesConfiguration.CATALOG);
    table.float('rank', 4, 4).notNullable();
  });

  await knex.raw(`
    ALTER TABLE catalog_index
    ADD COLUMN indexed_vector tsvector GENERATED ALWAYS AS (to_tsvector('spanish', indexed_text)) STORED;
  `);

  await knex.schema.alterTable('catalog_index', (table: Knex.AlterTableBuilder) => {
    table.primary(['catalog_id', 'company_id'], { constraintName: 'catalog_index_catalog_id_company_id_pkey' });
    table.index('indexed_vector', 'catalog_index_indexed_vector_idx', 'gin');
  });

  await knex.raw('DROP INDEX IF EXISTS catalog_index_indexed_text_trgm_idx');

  await knex.raw('DROP FUNCTION IF EXISTS providers_catalog_search');
  await knex.raw('DROP FUNCTION IF EXISTS providers_catalog_search_by_company');
  await knex.raw('DROP FUNCTION IF EXISTS catalog_search_by_company');
  await knex.raw('DROP FUNCTION IF EXISTS catalog_search');

  await knex.raw(`
    CREATE OR REPLACE FUNCTION providers_catalog_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG}_search_results AS $$
    BEGIN
      RETURN QUERY SELECT * FROM providers_catalog_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION providers_catalog_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG}_search_results AS $$
    BEGIN      
      RETURN QUERY
      WITH ordered_results AS (
        SELECT 
          catalog_id,
          ts_rank_cd(indexed_vector, plainto_tsquery('spanish', replace(search_term, '-', ' '))) AS rank
        FROM ${TableNamesConfiguration.CATALOG_INDEX} ci
        JOIN ${TableNamesConfiguration.CLIENT} c ON ci.company_id = c.company_id
        WHERE c.client_company_id = company_id_for_searching
          AND ci.indexed_vector @@ plainto_tsquery('spanish', replace(search_term, '-', ' '))
      )
      SELECT 
        catalog.*, ordered_results.rank
      FROM 
        ordered_results
      JOIN 
        ${TableNamesConfiguration.CATALOG} ON ordered_results.catalog_id = catalog.id;
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION catalog_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG}_search_results AS $$
    BEGIN
      RETURN QUERY SELECT * FROM catalog_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION catalog_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG}_search_results AS $$
    BEGIN      
      RETURN QUERY
      WITH ordered_results AS (
        SELECT 
          catalog_id,
          ts_rank_cd(indexed_vector, plainto_tsquery('spanish', replace(search_term, '-', ' '))) AS rank
        FROM ${TableNamesConfiguration.CATALOG_INDEX} ci
        WHERE indexed_vector @@ plainto_tsquery('spanish', replace(search_term, '-', ' '))
          AND company_id = company_id_for_searching
      )
      SELECT 
        catalog.*, ordered_results.rank
      FROM
        ordered_results
      JOIN
        ${TableNamesConfiguration.CATALOG} ON ordered_results.catalog_id = catalog.id;
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw('DROP FUNCTION IF EXISTS providers_catalog_search_by_company');
  await knex.raw('DROP FUNCTION IF EXISTS catalog_search_by_company');
  await knex.raw('DROP FUNCTION IF EXISTS providers_catalog_search');
  await knex.raw('DROP FUNCTION IF EXISTS catalog_search');

  await knex.raw(`
    CREATE OR REPLACE FUNCTION catalog_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG} AS $$
    BEGIN
      RETURN QUERY SELECT * FROM catalog_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END;
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION catalog_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG} AS $$
    BEGIN      
      RETURN QUERY
      WITH ordered_results AS (
        SELECT 
          catalog_id, 
          SUM(similarity(indexed_text, search_term)) AS total_similarity
        FROM ${TableNamesConfiguration.CATALOG_INDEX}
        WHERE indexed_text % search_term
          AND company_id = company_id_for_searching
        GROUP BY catalog_id
        ORDER BY total_similarity DESC
        LIMIT 50
      )
      SELECT 
        catalog.*
      FROM 
        ordered_results
      JOIN 
        catalog ON ordered_results.catalog_id = catalog.id
      WHERE 
        ordered_results.total_similarity > 0.1
      ORDER BY
        ordered_results.total_similarity DESC;
    END;
    $$ LANGUAGE plpgsql STABLE SET pg_trgm.similarity_threshold = 0.05;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION providers_catalog_search(
      search_term TEXT, 
      hasura_session json
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG} AS $$
    BEGIN
      RETURN QUERY SELECT * FROM providers_catalog_search_by_company(search_term, (hasura_session ->> 'x-hasura-company-id')::uuid);
    END; 
    $$ LANGUAGE plpgsql STABLE;
  `);

  await knex.raw(`
    CREATE OR REPLACE FUNCTION providers_catalog_search_by_company(
      search_term TEXT, 
      company_id_for_searching UUID
    )
    RETURNS SETOF ${TableNamesConfiguration.CATALOG} AS $$
    BEGIN      
      RETURN QUERY
      WITH company_providers AS (
        SELECT company_id FROM client c WHERE client_company_id = company_id_for_searching
      ), ordered_results AS (
        SELECT 
          catalog_id, 
          SUM(similarity(indexed_text, search_term)) AS total_similarity
        FROM ${TableNamesConfiguration.CATALOG_INDEX} ci
        WHERE indexed_text % search_term
          AND company_id IN (SELECT company_id FROM company_providers)
        GROUP BY catalog_id
        ORDER BY total_similarity DESC
      )
      SELECT 
        catalog.*
      FROM 
        ordered_results
      JOIN 
        catalog ON ordered_results.catalog_id = catalog.id
      WHERE 
        ordered_results.total_similarity > 0.1 AND disabled_at IS NULL
      ORDER BY
        ordered_results.total_similarity DESC
      LIMIT 50;
    END;
    $$ LANGUAGE plpgsql STABLE SET pg_trgm.similarity_threshold = 0.05;
  `);

  await knex.raw(`CREATE INDEX catalog_index_indexed_text_trgm_idx ON ${TableNamesConfiguration.CATALOG_INDEX} USING gin (indexed_text gin_trgm_ops)`);

  await knex.schema.alterTable(TableNamesConfiguration.CATALOG_INDEX, (table: Knex.AlterTableBuilder) => {
    table.dropPrimary('catalog_index_catalog_id_company_id_pkey');
    table.dropColumn('indexed_vector');
  });

  await knex.schema.dropTable(`${TableNamesConfiguration.CATALOG}_search_results`);
}
