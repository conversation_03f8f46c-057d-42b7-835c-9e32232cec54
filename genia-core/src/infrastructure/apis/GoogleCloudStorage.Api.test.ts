import { ApiError, Storage } from '@google-cloud/storage';

import GoogleCloudStorage, { ContentType, EntityType, MediaKind } from '#infrastructure/apis/GoogleCloudStorage.Api';
import EnvConfiguration from '#infrastructure/configurations/Env.configuration';

const {
  GCS_PRIVATE_BUCKET_NAME,
  GCS_PUBLIC_BUCKET_NAME,
} = EnvConfiguration.getEnv();

jest.unmock('#infrastructure/apis/GoogleCloudStorage.Api');

describe('GoogleCloudStorage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateSignedUrl', () => {
    it('should generate a signed URL with the correct parameters', async () => {
      const command = {
        kind: MediaKind.MEDIA,
        entity: EntityType.CATALOG,
        entityId: '12345',
        contentType: ContentType.JPG,
      };

      const signedUrlResponse = await GoogleCloudStorage.generateSignedUrl(command);

      expect(signedUrlResponse).toHaveProperty('signedUrl');
      expect(signedUrlResponse).toHaveProperty('fullGcsUri');
    });

    it('should throw an error if its an invalid kind for provided media', async () => {
      const command = {
        kind: MediaKind.ATTACHMENTS,
        entity: EntityType.CATALOG,
        entityId: '12345',
        contentType: ContentType.JPG,
      };

      expect(GoogleCloudStorage.generateSignedUrl(command)).rejects.toThrow('Invalid kind for the entity');
    });
  });

  describe('deleteFromStorage', () => {
    const entity = 'catalog';
    const entityId = '12345';
    const mediaId = '67890';

    const filePathPrefix = `media/${entity}/${entityId}/${mediaId}`;

    const storage = new Storage();

    it('should delete the files from Google Cloud Storage', async () => {
      await GoogleCloudStorage.deleteFromStorage(entity, entityId, mediaId);

      expect(storage.bucket(GCS_PUBLIC_BUCKET_NAME).file(`${filePathPrefix}.webp`).delete).toHaveBeenCalled();
      expect(storage.bucket(GCS_PRIVATE_BUCKET_NAME).getFiles).toHaveBeenCalledWith({ prefix: filePathPrefix });
    });

    it('should not throw error if the file was not found', async () => {
      const notFoundError = new Error('Simulated 404 Not Found') as ApiError;
      notFoundError.code = 404;

      (storage.bucket(GCS_PUBLIC_BUCKET_NAME).file(`${filePathPrefix}.webp`).delete as jest.Mock).mockRejectedValueOnce(notFoundError);

      expect(GoogleCloudStorage.deleteFromStorage(entity, entityId, mediaId)).resolves.toBeUndefined();
    });

    it('should throw error if google responds with code different to 404', async () => {
      const serverError = new Error('Simulated 500') as ApiError;
      serverError.code = 500;

      (storage.bucket(GCS_PUBLIC_BUCKET_NAME).file(`${filePathPrefix}.webp`).delete as jest.Mock).mockRejectedValueOnce(serverError);

      await expect(GoogleCloudStorage.deleteFromStorage(entity, entityId, mediaId)).rejects.toThrow('Simulated 500');
    });
  });
});
