import { createTransport } from 'nodemailer';

import EnvConfiguration from '#infrastructure/configurations/Env.configuration';

import EmailApi from './Email.Api';

jest.unmock('./Email.Api');

describe('EmailApi', () => {
  beforeAll(() => {
    jest.clearAllMocks();
  });

  const { SENDER_EMAIL, BIIZTRAL_WEB_URL, BIIZTRAL_LOGO_URL } = EnvConfiguration.getEnv();

  describe('sendInvitation', () => {
    it('should send email successfully with correct parameters', async () => {
      const inviter = 'John <PERSON>';
      const company = 'Acme Corp';
      const recipientEmail = '<EMAIL>';
      const invitationId = 'inv-123';

      await expect(EmailApi.sendInvitation(inviter, company, recipientEmail, invitationId)).resolves.toBeUndefined();

      expect(createTransport().sendMail).toHaveBeenCalledWith({
        from: `Equipo <PERSON> <${SENDER_EMAIL}>`,
        to: recipientEmail,
        subject: '¡Te han invitado a unirte a Biiztral!',
        html: `
      <p>Hola!</p>
      <p>El usuario <b>${inviter}</b> de la compañía <b>${company}</b> te ha invitado a unirte a Biiztral.</p>
      <p>Biiztral es la solución donde las empresas pueden gestionar sus procesos de compras, ventas e inventario de forma eficiente.</p>
      <p>Haz <a href="${BIIZTRAL_WEB_URL}?invitationId=${invitationId}">clic aquí</a> para aceptar.</p>
      <p>¡Esperamos verte pronto en Biiztral!</p>
      <br>
      <img src="${BIIZTRAL_LOGO_URL}" alt="Biiztral Logo" style="width: 150px; height: auto;">
    `,
      });
    });

    it('should send email successfully with correct parameters without invitationId', async () => {
      const inviter = 'John Doe';
      const company = 'Acme Corp';
      const recipientEmail = '<EMAIL>';

      await expect(EmailApi.sendInvitation(inviter, company, recipientEmail)).resolves.toBeUndefined();

      expect(createTransport().sendMail).toHaveBeenCalledWith({
        from: `Equipo Biiztral <${SENDER_EMAIL}>`,
        to: recipientEmail,
        subject: '¡Te han invitado a unirte a Biiztral!',
        html: `
      <p>Hola!</p>
      <p>El usuario <b>${inviter}</b> de la compañía <b>${company}</b> te ha invitado a unirte a Biiztral.</p>
      <p>Biiztral es la solución donde las empresas pueden gestionar sus procesos de compras, ventas e inventario de forma eficiente.</p>
      <p>Haz <a href="${BIIZTRAL_WEB_URL}">clic aquí</a> para aceptar.</p>
      <p>¡Esperamos verte pronto en Biiztral!</p>
      <br>
      <img src="${BIIZTRAL_LOGO_URL}" alt="Biiztral Logo" style="width: 150px; height: auto;">
    `,
      });
    });
  });
});
