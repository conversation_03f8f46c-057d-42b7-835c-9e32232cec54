import { GetSignedUrlConfig, Storage } from '@google-cloud/storage';
import { v4 } from 'uuid';

import EnvConfiguration from '#infrastructure/configurations/Env.configuration';

const {
  GCS_PRIVATE_BUCKET_NAME,
  GCS_PUBLIC_BUCKET_NAME,
} = EnvConfiguration.getEnv();

const SIGNED_URL_EXPIRATION_MS = 15 * 60 * 1000; // 15 minutes

const storage = new Storage();

export enum EntityType {
  CATALOG = 'catalog',
  INVENTORY = 'inventory',
}

export enum MediaKind {
  ATTACHMENTS = 'attachments',
  MEDIA = 'media',
}

const allowedKindsForEntity: { [key in EntityType]: MediaKind[] } = {
  [EntityType.CATALOG]: [MediaKind.MEDIA],
  [EntityType.INVENTORY]: [MediaKind.ATTACHMENTS, MediaKind.MEDIA],
};

export const ERROR_CODES = {
  INVALID_KIND: 'SIGNED_URL_INVALID_KIND',
};

export interface SignedUrlResponse {
  signedUrl: string;
  fullGcsUri: string;
}

export enum ContentType {
  JPG = 'image/jpeg',
  PNG = 'image/png',
  WEBP = 'image/webp',
  PDF = 'application/pdf',
}

const contentTypeToExtension: { [key in ContentType]: string } = {
  [ContentType.JPG]: 'jpg',
  [ContentType.PNG]: 'png',
  [ContentType.WEBP]: 'webp',
  [ContentType.PDF]: 'pdf',
};

export interface GenerateSignedUrlCommand {
  kind: MediaKind;
  entity: EntityType;
  entityId: string;
  contentType: ContentType;
}

export async function generateSignedUrl(command: GenerateSignedUrlCommand): Promise<SignedUrlResponse> {
  const {
    kind, entity, entityId, contentType,
  } = command;

  if (!allowedKindsForEntity[entity].includes(kind)) {
    throw new Error('Invalid kind for the entity', { cause: ERROR_CODES.INVALID_KIND });
  }

  const uniqueId = v4();
  const objectName = `${kind}/${entity}/${entityId}/${uniqueId}.${contentTypeToExtension[contentType]}`;

  const options: GetSignedUrlConfig = {
    version: 'v4' as const,
    action: 'write' as const,
    expires: Date.now() + SIGNED_URL_EXPIRATION_MS,
    contentType,
  };

  const [signedUrl] = await storage
    .bucket(GCS_PRIVATE_BUCKET_NAME)
    .file(objectName)
    .getSignedUrl(options);

  const filename = `${objectName.split('.')[0]}.webp`;

  const fullGcsUri = `https://storage.googleapis.com/${GCS_PUBLIC_BUCKET_NAME}/${filename}`;

  return {
    signedUrl,
    fullGcsUri,
  };
}

export async function deleteFromStorage(entity: string, entityId: string, mediaId: string): Promise<undefined> {
  const filePathPrefix = `media/${entity}/${entityId}/${mediaId}`;

  const publicFilePath = `${filePathPrefix}.webp`;

  try {
    await storage.bucket(GCS_PUBLIC_BUCKET_NAME).file(publicFilePath).delete();
  } catch (error: unknown) {
    if (typeof error === 'object' && error !== null && 'code' in error && (error as { code: unknown }).code === 404) {
      console.log(`Public file not found, skipping: gs://${GCS_PUBLIC_BUCKET_NAME}/${publicFilePath}`);
    } else {
      throw error;
    }
  }

  const [privateFiles] = await storage.bucket(GCS_PRIVATE_BUCKET_NAME).getFiles({ prefix: filePathPrefix });

  const deletePromises = privateFiles.map((file) => file.delete());

  await Promise.all(deletePromises);
}

const GoogleCloudStorage = {
  generateSignedUrl,
  deleteFromStorage,
};

export default GoogleCloudStorage;
