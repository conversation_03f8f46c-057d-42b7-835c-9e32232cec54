import axios from 'axios';

import EnvConfiguration from '#infrastructure/configurations/Env.configuration';

const { WHATSAPP_GRAPH_API_TOKEN, SENDER_PHONE_NUMBER } = EnvConfiguration.getEnv();

async function sendInvitation(inviter: string, company: string, phoneNumber: string, invitationId?: string): Promise<undefined> {
  await axios({
    method: 'POST',
    url: `https://graph.facebook.com/v20.0/${SENDER_PHONE_NUMBER}/messages`,
    headers: {
      Authorization: `Bearer ${WHATSAPP_GRAPH_API_TOKEN}`,
    },
    data: {
      messaging_product: 'whatsapp',
      to: phoneNumber,
      type: 'template',
      template: {
        name: 'welcome',
        language: { code: 'es' },
        components: [
          {
            type: 'body',
            parameters: [
              { type: 'text', text: inviter },
              { type: 'text', text: company },
            ],
          },
          {
            type: 'button',
            sub_type: 'url',
            index: '0',
            parameters: [
              {
                type: 'text',
                text: invitationId,
              },
            ],
          },
        ],
      },
    },
  });
}

export default {
  sendInvitation,
};
