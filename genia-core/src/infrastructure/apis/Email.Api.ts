import { createTransport } from 'nodemailer';

import EnvConfiguration from '#infrastructure/configurations/Env.configuration';

const {
  SENDER_EMAIL,
  SENDER_EMAIL_APP_PASSWORD,
  BIIZTRAL_WEB_URL,
  BIIZTRAL_LOGO_URL,
} = EnvConfiguration.getEnv();

const transporter = createTransport({
  host: 'smtp.gmail.com',
  port: 587,
  secure: false,
  auth: {
    user: SENDER_EMAIL,
    pass: SENDER_EMAIL_APP_PASSWORD,
  },
});

async function sendInvitation(inviter: string, company: string, email: string, invitationId?: string): Promise<undefined> {
  const url = invitationId ? `${BIIZTRAL_WEB_URL}?invitationId=${invitationId}` : BIIZTRAL_WEB_URL;
  const mailOptions = {
    from: `Equipo Biiztral <${SENDER_EMAIL}>`,
    to: email,
    subject: '¡Te han invitado a unirte a Biiztral!',
    html: `
      <p>Hola!</p>
      <p>El usuario <b>${inviter}</b> de la compañía <b>${company}</b> te ha invitado a unirte a Biiztral.</p>
      <p>Biiztral es la solución donde las empresas pueden gestionar sus procesos de compras, ventas e inventario de forma eficiente.</p>
      <p>Haz <a href="${url}">clic aquí</a> para aceptar.</p>
      <p>¡Esperamos verte pronto en Biiztral!</p>
      <br>
      <img src="${BIIZTRAL_LOGO_URL}" alt="Biiztral Logo" style="width: 150px; height: auto;">
    `,
  };

  await transporter.sendMail(mailOptions);
}

const Email = {
  sendInvitation,
};

export default Email;
