import { OAuth2Client } from 'google-auth-library';

import GoogleOAuthApi from '#infrastructure/apis/GoogleOAuth.Api';

jest.unmock('#infrastructure/apis/GoogleOAuth.Api');

describe('GoogleOAuthApi', () => {
  let mockOAuth2ClientInstance: jest.Mocked<OAuth2Client>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockOAuth2ClientInstance = new OAuth2Client() as jest.Mocked<OAuth2Client>;
  });

  describe('getToken', () => {
    it('should return access token and refresh token when successful', async () => {
      const authCode = 'test-auth-code';
      const mockTokens = {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
      };

      (mockOAuth2ClientInstance.getToken as jest.Mock).mockResolvedValueOnce(({ tokens: mockTokens }));

      const result = await GoogleOAuthApi.getToken(authCode);

      expect(mockOAuth2ClientInstance.getToken).toHaveBeenCalledWith(authCode);
      expect(result).toEqual({
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
      });
    });

    it('should throw error when access token is missing', async () => {
      const authCode = 'test-auth-code';
      const mockTokens = {
        refresh_token: 'test-refresh-token',
      };

      (mockOAuth2ClientInstance.getToken as jest.Mock).mockResolvedValueOnce({ tokens: mockTokens });

      await expect(GoogleOAuthApi.getToken(authCode)).rejects.toThrow('Failed to retrieve access token');
    });

    it('should throw error when refresh token is missing', async () => {
      const authCode = 'test-auth-code';
      const mockTokens = {
        access_token: 'test-access-token',
      };

      (mockOAuth2ClientInstance.getToken as jest.Mock).mockResolvedValueOnce({ tokens: mockTokens });

      await expect(GoogleOAuthApi.getToken(authCode)).rejects.toThrow('Failed to retrieve access token');
    });

    it('should throw error when both tokens are missing', async () => {
      const authCode = 'test-auth-code';
      const mockTokens = {};

      (mockOAuth2ClientInstance.getToken as jest.Mock).mockResolvedValueOnce({ tokens: mockTokens });

      await expect(GoogleOAuthApi.getToken(authCode)).rejects.toThrow('Failed to retrieve access token');
    });

    it('should throw error when getToken fails', async () => {
      const authCode = 'test-auth-code';
      const error = new Error('OAuth2 error');

      (mockOAuth2ClientInstance.getToken as jest.Mock).mockRejectedValueOnce(error);

      await expect(GoogleOAuthApi.getToken(authCode)).rejects.toThrow('OAuth2 error');
    });
  });

  describe('refreshToken', () => {
    it('should return new access token when refresh is successful', async () => {
      const refreshTokenValue = 'test-refresh-token';
      const mockCredentials = {
        access_token: 'new-access-token',
      };

      (mockOAuth2ClientInstance.refreshAccessToken as jest.Mock).mockResolvedValueOnce({ credentials: mockCredentials });

      const result = await GoogleOAuthApi.refreshToken(refreshTokenValue);

      expect(mockOAuth2ClientInstance.setCredentials).toHaveBeenCalledWith({ refresh_token: refreshTokenValue });
      expect(mockOAuth2ClientInstance.refreshAccessToken).toHaveBeenCalled();
      expect(result).toEqual({
        accessToken: 'new-access-token',
      });
    });

    it('should throw error when access token is missing from refresh response', async () => {
      const refreshTokenValue = 'test-refresh-token';
      const mockCredentials = {};

      (mockOAuth2ClientInstance.refreshAccessToken as jest.Mock).mockResolvedValueOnce({ credentials: mockCredentials });

      await expect(GoogleOAuthApi.refreshToken(refreshTokenValue)).rejects.toThrow('Failed to refresh access token');
    });

    it('should throw error when refreshAccessToken fails', async () => {
      const refreshTokenValue = 'test-refresh-token';
      const error = new Error('Refresh token error');

      (mockOAuth2ClientInstance.refreshAccessToken as jest.Mock).mockRejectedValueOnce(error);

      await expect(GoogleOAuthApi.refreshToken(refreshTokenValue)).rejects.toThrow('Refresh token error');
    });

    it('should set credentials before attempting to refresh', async () => {
      const refreshTokenValue = 'test-refresh-token';
      const mockCredentials = {
        access_token: 'new-access-token',
      };

      (mockOAuth2ClientInstance.refreshAccessToken as jest.Mock).mockResolvedValueOnce({ credentials: mockCredentials });

      await GoogleOAuthApi.refreshToken(refreshTokenValue);

      expect(mockOAuth2ClientInstance.setCredentials).toHaveBeenCalledWith({ refresh_token: refreshTokenValue });
      expect(mockOAuth2ClientInstance.refreshAccessToken).toHaveBeenCalled();

      // Verify that setCredentials was called before refreshAccessToken
      const setCredentialsCall = (mockOAuth2ClientInstance.setCredentials as jest.Mock).mock.invocationCallOrder[0];
      const refreshCall = (mockOAuth2ClientInstance.refreshAccessToken as jest.Mock).mock.invocationCallOrder[0];
      expect(setCredentialsCall).toBeLessThan(refreshCall);
    });
  });
});
