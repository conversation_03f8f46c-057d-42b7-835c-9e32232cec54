import axios from 'axios';

import WhatsappApi from '#infrastructure/apis/Whatsapp.Api';
import EnvConfiguration from '#infrastructure/configurations/Env.configuration';

jest.unmock('./Whatsapp.Api');

describe('Whatsapp.Api', () => {
  const { WHATSAPP_GRAPH_API_TOKEN, SENDER_PHONE_NUMBER } = EnvConfiguration.getEnv();

  const phoneNumber = '3126686868';
  const inviter = '<PERSON>';
  const company = 'Suplif.ai';
  const invitationId = 'inv-123';

  describe('sendMessage', () => {
    it('should send a message correctly', async () => {
      await WhatsappApi.sendInvitation(inviter, company, phoneNumber, invitationId);

      expect(axios).toHaveBeenCalledWith({
        method: 'POST',
        url: `https://graph.facebook.com/v20.0/${SENDER_PHONE_NUMBER}/messages`,
        headers: {
          Authorization: `Bearer ${WHATSAPP_GRAPH_API_TOKEN}`,
        },
        data: {
          messaging_product: 'whatsapp',
          to: phoneNumber,
          type: 'template',
          template: {
            name: 'welcome',
            language: { code: 'es' },
            components: [
              {
                type: 'body',
                parameters: [
                  { type: 'text', text: inviter },
                  { type: 'text', text: company },
                ],
              },
              {
                type: 'button',
                sub_type: 'url',
                index: '0',
                parameters: [
                  {
                    type: 'text',
                    text: invitationId,
                  },
                ],
              },
            ],
          },
        },
      });
    });

    it('should send a message correctly without invitationId', async () => {
      await WhatsappApi.sendInvitation(inviter, company, phoneNumber);

      expect(axios).toHaveBeenCalledWith({
        method: 'POST',
        url: `https://graph.facebook.com/v20.0/${SENDER_PHONE_NUMBER}/messages`,
        headers: {
          Authorization: `Bearer ${WHATSAPP_GRAPH_API_TOKEN}`,
        },
        data: {
          messaging_product: 'whatsapp',
          to: phoneNumber,
          type: 'template',
          template: {
            name: 'welcome',
            language: { code: 'es' },
            components: [
              {
                type: 'body',
                parameters: [
                  { type: 'text', text: inviter },
                  { type: 'text', text: company },
                ],
              },
              {
                type: 'button',
                sub_type: 'url',
                index: '0',
                parameters: [
                  {
                    type: 'text',
                    text: undefined,
                  },
                ],
              },
            ],
          },
        },
      });
    });
  });
});
