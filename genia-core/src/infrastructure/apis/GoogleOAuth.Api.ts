import { OAuth2Client } from 'google-auth-library';

import EnvConfiguration from '#infrastructure/configurations/Env.configuration';

const { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_REDIRECT_URI } = EnvConfiguration.getEnv();

const client = new OAuth2Client(
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  GOOGLE_REDIRECT_URI,
);

async function getToken(authCode: string): Promise<{accessToken: string, refreshToken?: string | null}> {
  const { tokens } = await client.getToken(authCode);
  if (!tokens.access_token || !tokens.refresh_token) {
    throw new Error('Failed to retrieve access token');
  }

  return {
    accessToken: tokens.access_token,
    refreshToken: tokens.refresh_token,
  };
}

async function refreshToken(rToken: string): Promise<{accessToken: string}> {
  client.setCredentials({ refresh_token: rToken });
  const { credentials } = await client.refreshAccessToken();
  if (!credentials.access_token) {
    throw new Error('Failed to refresh access token');
  }
  return {
    accessToken: credentials.access_token,
  };
}

const GoogleOAuthApi = {
  getToken,
  refreshToken,
};

export default GoogleOAuthApi;
