import { Request, Response } from 'express';

import CatalogUseCase from '#application/catalog/useCases/Catalog.UseCase';
import ClientUseCase from '#application/client/useCases/Client.UseCase';
import Logger from '#composition/Logger';
import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import ClientController from '#infrastructure/ports/http/resources/client/controllers/Client.Controller';
import { clientResponseSchema } from '#infrastructure/ports/http/resources/client/schemas/ClientResponse.Schema';
import CreateClientEntrySchema, { createClientEntrySchema } from '#infrastructure/ports/http/resources/client/schemas/CreateClientEntry.Schema';
import UpdateClientEntrySchema from '#infrastructure/ports/http/resources/client/schemas/UpdateClientEntry.Schema';
import { PaginatedQuerySchema } from '#infrastructure/ports/http/schemas/PaginatedQuery.Schema';
import SchemasValidatorUtility from '#infrastructure/utilities/SchemasValidator.Utility';

jest.unmock('#infrastructure/ports/http/resources/client/controllers/Client.Controller');

describe('ClientController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  describe('postClient', () => {
    beforeEach(() => {
      mockRequest = {
        body: [
          {
            name: 'China Parts On Fire',
            tributaryId: '*********',
            clientCompanyId: '*********',
            contactInformation: {},
          },
        ],
        authorization: {
          companyId: 'comp123',
          userId: 'user123',
        },
      };
      mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
      };
      jest.clearAllMocks();
    });

    it('should create client entries successfully', async () => {
      const mockResult: clientResponseSchema[] = [
        {
          id: 'cat123',
          name: 'product',
          tributaryId: '*********',
          companyId: 'comp_123',
          clientCompanyId: 'comp_124',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      (SchemasValidatorUtility.schemasValidation as jest.Mock).mockReturnValueOnce(true);
      (ClientUseCase.createClientEntries as jest.Mock).mockResolvedValueOnce(mockResult);
      (HandlersHttp.responseAdapter as jest.Mock).mockReturnValue(mockResult);

      await ClientController.postClient(mockRequest as Request, mockResponse as Response);

      const expectedEntries = (mockRequest.body as createClientEntrySchema[]).map((entry) => ({
        ...entry,
        contactInformation: {
          ...entry.contactInformation,
          billingAddress: null,
          billingEmail: null,
          billingPhoneNumber: null,
          billingWhatsapp: null,
          mainWhatsapp: null,
          mainAddress: null,
          representativeName: null,
          mainEmail: null,
          mainPhoneNumber: null,
          purchasesEmail: null,
          purchasesPhoneNumber: null,
          purchasesWhatsapp: null,
          salesEmail: null,
          salesPhoneNumber: null,
          salesWhatsapp: null,
          shippingAddress: null,
        },
      }));

      expect(HandlersHttp.responseAdapter).toHaveBeenCalledWith(mockResult);
      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(CreateClientEntrySchema, mockRequest.body);
      expect(ClientUseCase.createClientEntries).toHaveBeenCalledWith({
        entries: expectedEntries,
        companyId: 'comp123',
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResult);
    });

    it('should create client entries successfully without contact information', async () => {
      const mockResult: clientResponseSchema[] = [
        {
          id: 'cat123',
          name: 'product',
          tributaryId: '*********',
          companyId: 'comp_123',
          clientCompanyId: 'comp_124',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      (SchemasValidatorUtility.schemasValidation as jest.Mock).mockReturnValueOnce(true);
      (ClientUseCase.createClientEntries as jest.Mock).mockResolvedValueOnce(mockResult);
      (HandlersHttp.responseAdapter as jest.Mock).mockReturnValue(mockResult);

      mockRequest.body = [{ ...mockRequest.body[0], contactInformation: null }];

      await ClientController.postClient(mockRequest as Request, mockResponse as Response);

      const expectedEntries = (mockRequest.body as createClientEntrySchema[]).map((entry) => ({
        ...entry,
        contactInformation: null,
      }));

      expect(HandlersHttp.responseAdapter).toHaveBeenCalledWith(mockResult);
      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(CreateClientEntrySchema, mockRequest.body);
      expect(ClientUseCase.createClientEntries).toHaveBeenCalledWith({
        entries: expectedEntries,
        companyId: 'comp123',
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResult);
    });
  });

  describe('patchClient', () => {
    it('should update a client entry successfully', async () => {
      const mockBody = {
        name: 'China Parts On Fire',
      };

      mockRequest.body = mockBody;
      mockRequest.params = { clientId: 'cli123' };
      mockRequest.authorization = {
        companyId: 'comp123',
        userId: 'user123',
      };

      const mockResult = {
        id: 'cli123',
        name: mockBody.name,
        tributaryId: '*********',
        companyId: 'comp123',
        clientCompanyId: null,
      };

      (SchemasValidatorUtility.schemasValidation as jest.Mock).mockReturnValueOnce(true);
      (ClientUseCase.updateClientEntry as jest.Mock).mockResolvedValueOnce(mockResult);
      (HandlersHttp.responseAdapter as jest.Mock).mockReturnValueOnce(mockResult);

      await ClientController.patchClient(mockRequest as Request<{clientId: string}>, mockResponse as Response);

      expect(HandlersHttp.responseAdapter).toHaveBeenCalledWith(mockResult);
      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(UpdateClientEntrySchema, mockRequest.body);
      expect(ClientUseCase.updateClientEntry).toHaveBeenCalledWith({
        ...mockRequest.body,
        id: mockRequest.params.clientId,
        companyId: mockRequest.authorization.companyId,
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResult);
    });
  });

  describe('getCatalogForClient', () => {
    type req = Request<{clientId: string}, unknown, unknown, PaginatedQuerySchema>;

    beforeEach(() => {
      mockRequest = {
        authorization: { companyId: 'comp123', userId: 'user' },
        params: { clientId: 'cli456' },
        query: { orderBy: 'createdAt', orderDirection: 'DESC', searchTerm: 'term' },
      };
      mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
      };
      jest.clearAllMocks();
    });

    it('should return catalog for client successfully', async () => {
      const catalogData = [{ id: 'item1', name: 'Catalog Item' }];
      const mockResult = { data: catalogData, count: 1 };

      (SchemasValidatorUtility.schemasValidation as jest.Mock).mockImplementation(() => {});
      (HandlersHttp.responseAdapter as jest.Mock).mockReturnValue(catalogData);
      (CatalogUseCase.findCatalogForClient as jest.Mock).mockResolvedValueOnce(mockResult);

      await ClientController.getCatalogForClient(mockRequest as unknown as req, mockResponse as Response);

      expect(CatalogUseCase.findCatalogForClient).toHaveBeenCalledWith({
        companyId: 'comp123',
        clientId: 'cli456',
        query: {
          page: 1,
          pageSize: 20,
          orderBy: { [mockRequest.query?.orderBy as string]: mockRequest.query?.orderDirection as string },
          searchTerm: 'term',
        },
      });
      expect(HandlersHttp.responseAdapter).toHaveBeenCalledWith(mockResult.data);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({ data: catalogData, count: mockResult.count });
    });

    it('Should log error when schema validation fails', async () => {
      const error = new Error('Validation error');
      (SchemasValidatorUtility.schemasValidation as jest.Mock).mockImplementation(() => {
        throw error;
      });

      const catalogData = [{ id: 'item1', name: 'Catalog Item' }];
      const mockResult = { data: catalogData, count: 1 };

      (HandlersHttp.responseAdapter as jest.Mock).mockReturnValue(catalogData);
      (CatalogUseCase.findCatalogForClient as jest.Mock).mockResolvedValueOnce(mockResult);

      await ClientController.getCatalogForClient(mockRequest as unknown as req, mockResponse as Response);

      expect(Logger.getLogger().error).toHaveBeenCalledWith(error);
    });

    it('Should handle default orderBy and orderDirection', async () => {
      mockRequest.query = {};

      const catalogData = [{ id: 'item1', name: 'Catalog Item' }];
      const mockResult = { data: catalogData, count: 1 };

      (SchemasValidatorUtility.schemasValidation as jest.Mock).mockImplementation(() => {});
      (HandlersHttp.responseAdapter as jest.Mock).mockReturnValue(catalogData);
      (CatalogUseCase.findCatalogForClient as jest.Mock).mockResolvedValueOnce(mockResult);

      await ClientController.getCatalogForClient(mockRequest as unknown as req, mockResponse as Response);

      expect(CatalogUseCase.findCatalogForClient).toHaveBeenCalledWith({
        companyId: 'comp123',
        clientId: 'cli456',
        query: {
          page: 1,
          pageSize: 20,
          orderBy: { createdAt: 'DESC' },
          searchTerm: undefined,
        },
      });
    });
  });
});
