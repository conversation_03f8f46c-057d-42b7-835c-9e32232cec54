import { Request, Response } from 'express';

import CatalogUseCase from '#application/catalog/useCases/Catalog.UseCase';
import ClientUseCase from '#application/client/useCases/Client.UseCase';
import { PaginatedQueryWithSearchAndFilters } from '#application/Common.Type';
import Logger from '#composition/Logger';
import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import { PaginatedPublicCatalogSchema, publicCatalogSchema } from '#infrastructure/ports/http/resources/catalog/schemas/PublicCatalog.Schema';
import { clientResponseSchema } from '#infrastructure/ports/http/resources/client/schemas/ClientResponse.Schema';
import CreateClientEntrySchema, { createClientEntrySchema } from '#infrastructure/ports/http/resources/client/schemas/CreateClientEntry.Schema';
import UpdateClientEntrySchema, { updateClientEntrySchema } from '#infrastructure/ports/http/resources/client/schemas/UpdateClientEntry.Schema';
import { PaginatedQuerySchema } from '#infrastructure/ports/http/schemas/PaginatedQuery.Schema';
import SchemasValidatorUtility from '#infrastructure/utilities/SchemasValidator.Utility';

const LoggerInstance = Logger.getLogger().child({ location: 'Client.Controller' });

/**
 * @openapi
 * /client:
 *   post:
 *     description: Create multiple client entries
 *     tags:
 *       - Client
 *     summary: Create multiple client entries
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               $ref: "#/components/schemas/CreateClientEntrySchema"
 *     responses:
 *       "201":
 *         description: Client entries created
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: "#/components/schemas/ClientResponseSchema"
 *       "400":
 *         $ref: "#/components/responses/4XX"
 *       "5XX":
 *         $ref: "#/components/responses/5XX"
 */
async function postClient(req: Request<unknown, unknown, createClientEntrySchema[]>, res: Response<clientResponseSchema[]>): Promise<void> {
  const { body, authorization: { companyId } } = req;
  SchemasValidatorUtility.schemasValidation(CreateClientEntrySchema, body);

  const entries = body.map((entry) => {
    if (entry.contactInformation) {
      const {
        billingAddress = null, billingEmail = null, billingPhoneNumber = null, billingWhatsapp = null,
        purchasesEmail = null, purchasesPhoneNumber = null, purchasesWhatsapp = null,
        mainAddress = null, mainEmail = null, mainPhoneNumber = null, mainWhatsapp = null,
        salesEmail = null, salesPhoneNumber = null, salesWhatsapp = null, shippingAddress = null,
        representativeName = null,
      } = entry.contactInformation;

      return {
        ...entry,
        contactInformation: {
          mainEmail,
          mainPhoneNumber,
          mainWhatsapp,
          billingEmail,
          billingPhoneNumber,
          billingWhatsapp,
          purchasesEmail,
          purchasesPhoneNumber,
          purchasesWhatsapp,
          salesEmail,
          salesPhoneNumber,
          salesWhatsapp,
          shippingAddress,
          billingAddress,
          mainAddress,
          representativeName,
        },
      };
    }

    return {
      ...entry,
      contactInformation: null,
    };
  });

  const results = await ClientUseCase.createClientEntries({ entries, companyId });

  const response = HandlersHttp.responseAdapter(results);

  res.status(201).json(response);
}

/**
 * @openapi
 * /clients/{id}:
 *   patch:
 *     description: Updates a client entry, companyId and clientCompanyId are not updatable, the storeDiscount
 *     tags:
 *       - Client
 *     summary: Updates a client entry
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: "#/components/schemas/UpdateClientEntrySchema"
 *     responses:
 *       "200":
 *         description: Client entry updated
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: "#/components/schemas/ClientResponseSchema"
 *       "400":
 *         $ref: "#/components/responses/4XX"
 *       "5XX":
 *         $ref: "#/components/responses/5XX"
 */
async function patchClient(req: Request<{clientId: string}, unknown, updateClientEntrySchema[]>, res: Response<clientResponseSchema>): Promise<void> {
  const { body, authorization: { companyId }, params: { clientId } } = req;
  SchemasValidatorUtility.schemasValidation(UpdateClientEntrySchema, body);

  const result = await ClientUseCase.updateClientEntry({ ...body, companyId, id: clientId });

  const response = HandlersHttp.responseAdapter(result);

  res.status(200).json(response);
}

/**
 * @openapi
 * /clients/{id}/catalog:
 *   get:
 *     description: Get the catalog for a client
 *     tags:
 *       - Client
 *     summary: Get the catalog for a client
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The client id
 *         schema:
 *           type: string
 *       - in: query
 *         name: pageQuery
 *         schema:
 *           $ref: "#/components/schemas/PaginatedQuerySchema"
 *     responses:
 *       "200":
 *         description: Catalog for the client
 *         content:
 *           application/json:
 *             schema:
 *               $ref: "#/components/schemas/PaginatedPublicCatalogSchema"
 *       "400":
 *         $ref: "#/components/responses/4XX"
 *       "5XX":
 *         $ref: "#/components/responses/5XX"
 */
async function getCatalogForClient(
  req: Request<{clientId: string}, unknown, unknown, PaginatedQuerySchema>,
  res: Response<PaginatedPublicCatalogSchema >,
): Promise<void> {
  const logger = LoggerInstance.child({ method: 'getCatalogForClient' });

  const { authorization: { companyId }, params: { clientId }, query } = req;

  const useCaseQuery: PaginatedQueryWithSearchAndFilters = {
    page: 1,
    pageSize: 20,
    orderBy: {
      [query.orderBy || 'createdAt']: query.orderDirection || 'DESC',
    },
    searchTerm: query.searchTerm,
  };

  const results = await CatalogUseCase.findCatalogForClient({ companyId, clientId, query: useCaseQuery });

  const data = HandlersHttp.responseAdapter(results.data);

  try {
    SchemasValidatorUtility.schemasValidation(publicCatalogSchema, data);
  } catch (error) {
    logger.error(error);
  }

  res.status(200).json({
    data,
    count: results.count,
  });
}

const ClientController = {
  postClient,
  patchClient,
  getCatalogForClient,
};

export default ClientController;
