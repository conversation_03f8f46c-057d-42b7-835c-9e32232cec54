import { JSONSchemaType } from 'ajv';

import ContactInformationSchema, { ContacInformationSchemaShape } from '#infrastructure/ports/http/schemas/ContactInformationSchema';

export interface createClientEntrySchema {
  name: string;
  tributaryId?: string;
  clientCompanyId?: string;
  storeDiscounts?: string[]
  contactInformation?: ContacInformationSchemaShape | null;
  notes?: string | null;
}

const CreateClientEntrySchema: JSONSchemaType<createClientEntrySchema[]> = {
  type: 'array',
  items: {
    type: 'object',
    required: ['name'],
    properties: {
      name: { type: 'string', minLength: 1, maxLength: 80 },
      tributaryId: {
        type: 'string', pattern: '^[^\\s]+$', minLength: 1, nullable: true,
      },
      clientCompanyId: {
        type: 'string', format: 'uuid', nullable: true,
      },
      storeDiscounts: {
        type: 'array',
        items: { type: 'string', format: 'uuid' },
        uniqueItems: true,
        nullable: true,
      },
      contactInformation: {
        ...ContactInformationSchema,
        nullable: true,
      },
      notes: {
        type: 'string', minLength: 1, maxLength: 500, nullable: true,
      },
    },
    additionalProperties: false,
  },
  minItems: 1,
  maxItems: 20,
};

export default CreateClientEntrySchema;
