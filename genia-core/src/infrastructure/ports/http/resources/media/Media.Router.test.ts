import express from 'express';

import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import { uuidRegExp } from '#infrastructure/ports/http/RegExp';
import ApiKeyMiddleWare from '#infrastructure/ports/http/middlewares/ApiKey.Middleware.Http';
import MediaRouter from '#infrastructure/ports/http/resources/media/Media.Router';
import MediaController from '#infrastructure/ports/http/resources/media/controllers/Media.Controller';

jest.unmock('#infrastructure/ports/http/resources/media/Media.Router');

jest.mock('express', () => ({
  Router: jest.fn().mockReturnValue({}),
}));

jest.mock('#infrastructure/ports/http/Handlers.Http', () => ({
  handleAndCatch: jest.fn((c) => c),
}));

describe('MediaRouter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should correctly set up  media routes', () => {
    express.Router().post = jest.fn();
    express.Router().patch = jest.fn();
    express.Router().delete = jest.fn();

    MediaRouter.getRoutes();

    expect(express.Router().post).toHaveBeenCalledWith(
      `/:entity(catalog|inventory)/:entityId(${uuidRegExp})`,
      HandlersHttp.handleAndCatch(MediaController.postMedia),
    );
    expect(express.Router().patch).toHaveBeenCalledWith(
      `/:mediaId(${uuidRegExp})`,
      ApiKeyMiddleWare.validateAccess,
      HandlersHttp.handleAndCatch(MediaController.patchMedia),
    );
    expect(express.Router().delete).toHaveBeenCalledWith(
      `/:entity(catalog|inventory)/:entityId(${uuidRegExp})`,
      HandlersHttp.handleAndCatch(MediaController.deleteMedia),
    );
  });
});
