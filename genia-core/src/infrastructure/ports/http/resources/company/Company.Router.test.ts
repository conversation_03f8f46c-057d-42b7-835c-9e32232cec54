import express from 'express';

import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import { uuidRegExp } from '#infrastructure/ports/http/RegExp';
import CompanyRouter from '#infrastructure/ports/http/resources/company/Company.Router';
import CompanyController from '#infrastructure/ports/http/resources/company/controllers/Company.Controller';

jest.unmock('#infrastructure/ports/http/resources/company/Company.Router');

jest.mock('express', () => ({
  Router: jest.fn().mockReturnValue({}),
}));

jest.mock('#infrastructure/ports/http/Handlers.Http', () => ({
  handleAndCatch: jest.fn((c) => c),
}));

describe('CompanyRouter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should correctly set up  company routes', () => {
    express.Router().patch = jest.fn();

    CompanyRouter.getRoutes();

    expect(express.Router().patch).toHaveBeenCalledWith(
      `/:companyId(${uuidRegExp})`,
      HandlersHttp.handleAndCatch(CompanyController.patchCompany),
    );
  });
});
