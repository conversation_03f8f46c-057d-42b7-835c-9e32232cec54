import { Request, Response } from 'express';

import UpdateCompanyUseCase from '#application/company/useCases/UpdateCompany.UseCase';
import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import PatchCompanySchema, { PatchCompanySchemaShape } from '#infrastructure/ports/http/resources/company/schemas/PatchCompany.Schema';
import SchemasValidatorUtility from '#infrastructure/utilities/SchemasValidator.Utility';

import CompanyController from './Company.Controller';

jest.unmock('./Company.Controller');

describe('CompanyController.patchCompany', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    mockRequest = {
      body: {
        name: 'Updated Company',
        tributaryId: 'TAX123',
        description: 'Updated description',
      },
      params: {
        companyId: 'company-1',
      },
      authorization: {
        userId: 'user-1',
        companyId: 'company-1',
      },
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  it('should validate input, update company and return 200', async () => {
    const mockUpdatedCompany = {
      id: 'company-1',
      name: 'Updated Company',
      tributaryId: 'TAX123',
      description: 'Updated description',
    };

    (SchemasValidatorUtility.schemasValidation as jest.Mock).mockReturnValue(true);
    (UpdateCompanyUseCase.apply as jest.Mock).mockResolvedValue(mockUpdatedCompany);
    (HandlersHttp.responseAdapter as jest.Mock).mockReturnValue(mockUpdatedCompany);

    await CompanyController.patchCompany(
      mockRequest as Request<{ companyId: string }, unknown, PatchCompanySchemaShape>,
      mockResponse as Response,
    );

    expect(SchemasValidatorUtility.schemasValidation)
      .toHaveBeenCalledWith(PatchCompanySchema, mockRequest.body);

    expect(UpdateCompanyUseCase.apply).toHaveBeenCalledWith({
      ...mockRequest.body,
      id: 'company-1',
      userId: 'user-1',
    });

    expect(HandlersHttp.responseAdapter).toHaveBeenCalledWith(mockUpdatedCompany);
    expect(mockResponse.status).toHaveBeenCalledWith(200);
    expect(mockResponse.json).toHaveBeenCalledWith(mockUpdatedCompany);
  });

  it('should throw if schema validation fails', async () => {
    (SchemasValidatorUtility.schemasValidation as jest.Mock)
      .mockImplementation(() => {
        throw new Error('Invalid schema');
      });

    await expect(
      CompanyController.patchCompany(
        mockRequest as Request<{ companyId: string }, unknown, PatchCompanySchemaShape>,
        mockResponse as Response,
      ),
    ).rejects.toThrow('Invalid schema');

    expect(UpdateCompanyUseCase.apply).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
  });

  it('should propagate errors from use case', async () => {
    (SchemasValidatorUtility.schemasValidation as jest.Mock).mockReturnValue(true);
    (UpdateCompanyUseCase.apply as jest.Mock)
      .mockRejectedValue(new Error('Use case error'));

    await expect(
      CompanyController.patchCompany(
        mockRequest as Request<{ companyId: string }, unknown, PatchCompanySchemaShape>,
        mockResponse as Response,
      ),
    ).rejects.toThrow('Use case error');

    expect(mockResponse.json).not.toHaveBeenCalled();
  });
});
