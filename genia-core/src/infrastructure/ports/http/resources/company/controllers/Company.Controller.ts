import { Request, Response } from 'express';

import UpdateCompanyUseCase from '#application/company/useCases/UpdateCompany.UseCase';
import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import { CompanySchemaShape } from '#infrastructure/ports/http/resources/company/schemas/Company.Schema';
import PatchCompanySchema, { PatchCompanySchemaShape } from '#infrastructure/ports/http/resources/company/schemas/PatchCompany.Schema';
import SchemasValidatorUtility from '#infrastructure/utilities/SchemasValidator.Utility';

/**
 * @openapi
 * /company/{id}:
 *   patch:
 *     description: Update company information
 *     tags:
 *       - Company
 *     summary: Update company information
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Company ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: "#/components/schemas/PatchCompanySchema"
 *     responses:
 *       "200":
 *         description: Company updated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: "#/components/schemas/CompanySchema"
 *       "400":
 *         $ref: "#/components/responses/4XX"
 *       "5XX":
 *         $ref: "#/components/responses/5XX"
 */
async function patchCompany(req: Request<{ companyId: string }, unknown, PatchCompanySchemaShape>, res: Response<CompanySchemaShape>): Promise<void> {
  const { body, params: { companyId }, authorization: { userId } } = req;

  SchemasValidatorUtility.schemasValidation(PatchCompanySchema, body);

  const result = await UpdateCompanyUseCase.apply({ ...body, id: companyId, userId });

  const response = HandlersHttp.responseAdapter(result);

  res.status(200).json(response);
}

const CompanyController = { patchCompany };

export default CompanyController;
