import { Router } from 'express';

import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import { uuidRegExp } from '#infrastructure/ports/http/RegExp';

import CompanyController from './controllers/Company.Controller';

function getRoutes(): Router {
  const companyRoutes = Router();

  companyRoutes.patch(`/:companyId(${uuidRegExp})`, HandlersHttp.handleAndCatch(CompanyController.patchCompany));

  return companyRoutes;
}

const CompanyRouter = {
  getRoutes,
};

export default CompanyRouter;
