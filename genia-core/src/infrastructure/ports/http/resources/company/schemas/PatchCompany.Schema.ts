import { JSONSchemaType } from 'ajv';

import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';
import ContactInformationSchema from '#infrastructure/ports/http/schemas/ContactInformationSchema';

export interface PatchCompanySchemaShape {
  name?: string;
  description?: string;
  tributaryId?: string;
  contactInformation?: Partial<ContactInformationValueObject> | null;
}

const PatchCompanySchema: JSONSchemaType<PatchCompanySchemaShape> = {
  type: 'object',
  properties: {
    name: {
      type: 'string', minLength: 1, maxLength: 100, nullable: true,
    },
    description: {
      type: 'string', minLength: 1, maxLength: 500, nullable: true,
    },
    tributaryId: {
      type: 'string', minLength: 1, maxLength: 50, nullable: true,
    },
    contactInformation: {
      ...ContactInformationSchema,
      nullable: true,
    },
  },
  minProperties: 1,
};

export default PatchCompanySchema;
