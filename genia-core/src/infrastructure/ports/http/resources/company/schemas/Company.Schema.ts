import { JSONSchemaType } from 'ajv';

import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';
import ContactInformationSchema from '#infrastructure/ports/http/schemas/ContactInformationSchema';

export interface CompanySchemaShape {
  id: string
  name: string;
  description?: string;
  tributaryId: string;
  country: string;
  contactInformation?: Partial<ContactInformationValueObject> | null;
}

const CompanySchema: JSONSchemaType<CompanySchemaShape> = {
  type: 'object',
  properties: {
    id: { type: 'string' },
    name: { type: 'string' },
    description: { type: 'string', nullable: true },
    tributaryId: { type: 'string' },
    country: { type: 'string' },
    contactInformation: {
      ...ContactInformationSchema,
      nullable: true,
    },
  },
  required: ['id', 'name', 'tributaryId', 'country'],
  additionalProperties: false,
};

export default CompanySchema;
