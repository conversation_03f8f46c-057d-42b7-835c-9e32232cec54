import { Request, Response } from 'express';

import RegisterCommand from '#application/user/commands/Register.Command';
import GetUserIdByEmailUseCase from '#application/user/useCases/GetUserIdByEmail.UseCase';
import InviteUsersToCompanyUseCase from '#application/user/useCases/InviteUsersToCompany.UseCase';
import UpdateUserUseCase from '#application/user/useCases/UpdateUser.UseCase';
import UserUseCase from '#application/user/useCases/User.UseCase';
import EnvConfiguration from '#infrastructure/configurations/Env.configuration';
import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import { ClaimsResponseSchemaShape } from '#infrastructure/ports/http/resources/users/schemas/ClaimsResponse.Schema';
import GetUserByEmailSchema from '#infrastructure/ports/http/resources/users/schemas/GetUserByEmail.Schema';
import PatchUserSchema, { PatchUserSchemaShape } from '#infrastructure/ports/http/resources/users/schemas/PatchUser.Schema';
import PostCompanyUsersSchema, { PostCompanyUsersSchemaShape } from '#infrastructure/ports/http/resources/users/schemas/PostCompanyUsers.Schema';
import RegisterSchema, { registerSchema } from '#infrastructure/ports/http/resources/users/schemas/Register.Schema';
import { registerResponseSchema } from '#infrastructure/ports/http/resources/users/schemas/RegisterResponse.Schema';
import { UserIdSchemaShape } from '#infrastructure/ports/http/resources/users/schemas/UserIdResponse.Schema';
import { UserResponseSchemaShape } from '#infrastructure/ports/http/resources/users/schemas/UserResponse.Schema';
import SchemasValidatorUtility from '#infrastructure/utilities/SchemasValidator.Utility';

const { HASURA_DEFAULT_ROLE, CLAIMS_NAMESPACE } = EnvConfiguration.getEnv();

const rfcRegex = /^([A-ZÑ&]{3,4})\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])[A-Z\d]{2}[A\d]$/;
const nitReges = /^\d{5,10}-\d{1}$/;
const cpfOuCnpjRegex = /^(\d{3}\.\d{3}\.\d{3}-\d{2}|\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2})$/;
const cuitCuilRegex = /^\d{2}-\d{8}-\d{1}$/;
const rutRegex = /^(\d{1,2}\.?\d{3}\.?\d{3}-[\dkK])$/;

const countryTID: Record<string, RegExp> = {
  MX: rfcRegex,
  CO: nitReges,
  BR: cpfOuCnpjRegex,
  AR: cuitCuilRegex,
  CL: rutRegex,
};

/**
   * @openapi
   * /users/register:
   *   post:
   *     description: Register a new user, this endpoint is private and only available for admins or to be used by auth third parties
   *     tags:
   *       - Users
   *     summary: Register a new user relating an external id and an email with our domain user
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: "#/components/schemas/RegisterSchema"
   *     responses:
   *       "200":
   *         description: User created
   *         content:
   *           application/json:
   *             schema:
   *               $ref: "#/components/schemas/RegisterResponseSchema"
   *       "400":
   *         description: Invalid input, review the schema.
   *         $ref: "#/components/responses/4XX"
   *       "409":
   *         description: User already exist.
   *         $ref: "#/components/responses/4XX"
   *       "5XX":
   *         $ref: "#/components/responses/5XX"
 */
async function register(req: Request<unknown, unknown, registerSchema>, res: Response<registerResponseSchema>): Promise<Response<registerResponseSchema>> {
  const { body } = req;

  SchemasValidatorUtility.schemasValidation(RegisterSchema, body);

  const {
    user: {
      email, name, lastName, phoneNumber,
    },
    company: {
      name: companyName, description, tributaryId, country, contactInformation = null,
    },
  } = body;

  if (!countryTID[country]) throw new Error('Invalid country', { cause: 'BAD_REQUEST' });

  if (!countryTID[country].test(tributaryId)) {
    throw new Error(`Invalid tributary ID for country ${country}`, { cause: 'BAD_REQUEST' });
  }

  const command: RegisterCommand = {
    user: {
      email,
      name,
      lastName,
      phoneNumber,
    },
    company: {
      name: companyName,
      description,
      tributaryId,
      country,
      contactInformation: contactInformation ? {
        billingAddress: contactInformation.billingAddress || null,
        billingEmail: contactInformation.billingEmail || null,
        billingPhoneNumber: contactInformation.billingPhoneNumber || null,
        billingWhatsapp: contactInformation.billingWhatsapp || null,
        purchasesEmail: contactInformation.purchasesEmail || null,
        mainAddress: contactInformation.mainAddress || null,
        mainEmail: contactInformation.mainEmail || null,
        mainPhoneNumber: contactInformation.mainPhoneNumber || null,
        mainWhatsapp: contactInformation.mainWhatsapp || null,
        purchasesPhoneNumber: contactInformation.purchasesPhoneNumber || null,
        purchasesWhatsapp: contactInformation.purchasesWhatsapp || null,
        salesEmail: contactInformation.salesEmail || null,
        salesPhoneNumber: contactInformation.salesPhoneNumber || null,
        salesWhatsapp: contactInformation.salesWhatsapp || null,
        shippingAddress: contactInformation.shippingAddress || null,
        representativeName: contactInformation.representativeName || null,
      } : null,
    },
  };

  const { company, user } = await UserUseCase.register(command);

  return res.send({
    user,
    company,
  });
}

/**
   * @openapi
   * /users/claims:
   *   post:
   *     description: Get claims for a user, this endpoint returns the metadata claims required for authentication
   *     tags:
   *       - Users
   *     summary: Get authentication claims for a registered user
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               userId:
   *                 type: string
   *               companyId:
   *                 type: string
   *             required:
   *               - userId
   *               - companyId
   *     responses:
   *       "200":
   *         description: Claims retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: "#/components/schemas/ClaimsResponseSchema"
   *       "400":
   *         description: Invalid input.
   *         $ref: "#/components/responses/4XX"
   *       "404":
   *         description: User not found.
   *         $ref: "#/components/responses/4XX"
   *       "5XX":
   *         $ref: "#/components/responses/5XX"
 */
async function getClaims(
  req: Request<{userId: string}, unknown, unknown>,
  res: Response<ClaimsResponseSchemaShape>,
): Promise<Response<ClaimsResponseSchemaShape>> {
  const { params: { userId } } = req;

  const { id, companies: [companyId] } = await UserUseCase.retrieveUser({ userId });

  return res.send({
    metadata: {
      claims: {
        [CLAIMS_NAMESPACE]: {
          'user-id': id,
          'company-id': companyId,
        },
        'https://hasura.io/jwt/claims': {
          'x-hasura-allowed-roles': [HASURA_DEFAULT_ROLE],
          'x-hasura-default-role': HASURA_DEFAULT_ROLE,
          'x-hasura-user-id': id,
          'x-hasura-company-id': companyId,
        },
      },
    },
  });
}

/**
 * @openapi
 * /users:
 *   post:
 *     description: >
 *       Creates users, associates them to the company and creates the invitations, this endpoint is used to register a user
 *     tags:
 *       - Users
 *     summary: Create and invite users to a company
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: "#/components/schemas/PostCompanyUsersSchema"
 *     responses:
 *       "200":
 *         description: The created Users
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: "#/components/schemas/UsersResponseSchema"
 *       "400":
 *         $ref: "#/components/responses/4XX"
 *       "5XX":
 *         $ref: "#/components/responses/5XX"
 */
async function postUsers(
  req: Request<unknown, unknown, PostCompanyUsersSchemaShape[]>,
  res: Response<UserResponseSchemaShape[]>,
): Promise<void> {
  const { body, authorization: { userId, companyId } } = req;
  SchemasValidatorUtility.schemasValidation(PostCompanyUsersSchema, body);

  const result = await InviteUsersToCompanyUseCase.apply({
    companyId,
    userId,
    invitedUsers: body.map((invitedUser) => ({
      email: invitedUser.email,
      name: invitedUser.name,
      lastName: invitedUser.lastName,
      phoneNumber: invitedUser.phoneNumber,
      role: invitedUser.role,
    })),
  });

  const response = HandlersHttp.responseAdapter(result) as UserResponseSchemaShape[];

  res.status(200).json(response);
}

/**
 * @openapi
 * /users/{id}:
 *   patch:
 *     description: >
 *       Updates an user, role and state can be updated by admin, the other basic properties by self.
 *     tags:
 *       - Users
 *     summary: Updates an user
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the user to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: "#/components/schemas/PatchUserSchema"
 *     responses:
 *       "200":
 *         description: The updated user
 *         content:
 *           application/json:
 *             schema:
 *               $ref: "#/components/schemas/UserResponseSchema"
 *       "400":
 *         $ref: "#/components/responses/4XX"
 *       "5XX":
 *         $ref: "#/components/responses/5XX"
 */
async function patchUsers(
  req: Request<{ userId: string }, unknown, PatchUserSchemaShape>,
  res: Response<UserResponseSchemaShape>,
): Promise<void> {
  const { body, authorization: { userId: updaterUserId }, params: { userId } } = req;
  SchemasValidatorUtility.schemasValidation(PatchUserSchema, body);

  const result = await UpdateUserUseCase.apply({
    updaterUserId,
    userId,
    updates: body,
  });

  const response = HandlersHttp.responseAdapter(result) as UserResponseSchemaShape;

  res.status(200).json(response);
}

/**
 * @openapi
 * /users:
 *   get:
 *     description: Get a user by email address.
 *     tags:
 *       - Users
 *     summary: Retrieve a user by email
 *     parameters:
 *       - in: query
 *         name: email
 *         schema:
 *           type: string
 *           format: email
 *         required: true
 *         description: The email of the user to retrieve.
 *     responses:
 *       "200":
 *         description: The user object
 *         content:
 *           application/json:
 *             schema:
 *               $ref: "#/components/schemas/UserIdResponseSchema"
 *       "400":
 *         description: Invalid input.
 *         $ref: "#/components/responses/4XX"
 *       "404":
 *         description: User not found.
 *         $ref: "#/components/responses/4XX"
 *       "5XX":
 *         $ref: "#/components/responses/5XX"
 */
async function getUserIdByEmail(
  req: Request<unknown, unknown, unknown, { email: string }>,
  res: Response<UserIdSchemaShape>,
): Promise<void> {
  const { query } = req;

  SchemasValidatorUtility.schemasValidation(GetUserByEmailSchema, query);

  const userId = await GetUserIdByEmailUseCase.apply(query);

  const response: UserIdSchemaShape = { userId };

  res.status(200).json(response);
}

const UserController = {
  register,
  getClaims,
  postUsers,
  patchUsers,
  getUserIdByEmail,
};

export default UserController;
