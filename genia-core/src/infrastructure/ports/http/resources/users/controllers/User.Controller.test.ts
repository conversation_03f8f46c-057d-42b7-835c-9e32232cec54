import { Request, Response } from 'express';

import GetUserIdByEmailUseCase from '#application/user/useCases/GetUserIdByEmail.UseCase';
import InviteUsersToCompanyUseCase from '#application/user/useCases/InviteUsersToCompany.UseCase';
import UpdateUserUseCase from '#application/user/useCases/UpdateUser.UseCase';
import UserUseCase from '#application/user/useCases/User.UseCase';
import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import UserEntity, { UserRole, UserState } from '#domain/aggregates/user/User.Entity';
import EnvConfiguration from '#infrastructure/configurations/Env.configuration';
import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import UserController from '#infrastructure/ports/http/resources/users/controllers/User.Controller';
import GetUserByEmailSchema from '#infrastructure/ports/http/resources/users/schemas/GetUserByEmail.Schema';
import RegisterSchema from '#infrastructure/ports/http/resources/users/schemas/Register.Schema';
import { UserResponseSchemaShape } from '#infrastructure/ports/http/resources/users/schemas/UserResponse.Schema';
import SchemasValidatorUtility from '#infrastructure/utilities/SchemasValidator.Utility';

jest.unmock('#infrastructure/ports/http/resources/users/controllers/User.Controller');
jest.unmock('#infrastructure/ports/http/resources/users/schemas/Register.Schema');
jest.unmock('#infrastructure/ports/http/Errors.Http');

const { CLAIMS_NAMESPACE, HASURA_DEFAULT_ROLE } = EnvConfiguration.getEnv();

describe('UserController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis(),
    };
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('should register a user successfully', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
        },
      };

      mockRequest.body = mockBody;

      const mockUser: UserEntity = {
        id: 'user-id-123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company-id-123'],
        role: UserRole.ADMIN,
        state: UserState.ACTIVE,
      };

      const mockCompany: CompanyEntity = {
        id: 'company-id-123',
        name: 'Test Company',
        description: 'A test company',
        tributaryId: 'XAXX010101000',
        country: 'MX',
        contactInformation: null,
      };

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);
      jest.spyOn(UserUseCase, 'register').mockResolvedValue({ user: mockUser, company: mockCompany });

      await UserController.register(mockRequest as Request, mockResponse as Response);

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(UserUseCase.register).toHaveBeenCalledWith({
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
          contactInformation: null,
        },
      });
      expect(mockResponse.send).toHaveBeenCalledWith({
        user: mockUser,
        company: mockCompany,
      });
    });

    it('should register a user successfully with null contact information', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
          contactInformation: null,
        },
      };

      mockRequest.body = mockBody;

      const mockUser: UserEntity = {
        id: 'user-id-123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company-id-123'],
        role: UserRole.ADMIN,
        state: UserState.ACTIVE,
      };

      const mockCompany: CompanyEntity = {
        id: 'company-id-123',
        name: 'Test Company',
        description: 'A test company',
        tributaryId: 'XAXX010101000',
        country: 'MX',
        contactInformation: null,
      };

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);
      jest.spyOn(UserUseCase, 'register').mockResolvedValue({ user: mockUser, company: mockCompany });

      await UserController.register(mockRequest as Request, mockResponse as Response);

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(UserUseCase.register).toHaveBeenCalledWith({
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
          contactInformation: null,
        },
      });
      expect(mockResponse.send).toHaveBeenCalledWith({
        user: mockUser,
        company: mockCompany,
      });
    });

    it('should register a user successfully with contact information', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
          contactInformation: {
            mainAddress: 'mainAddress',
            representativeName: 'representativeName',
            mainEmail: 'mainEmail',
            mainPhoneNumber: 'mainPhoneNumber',
            mainWhatsapp: 'mainWhatsapp',
            billingAddress: 'billingAddress',
            billingEmail: 'billingEmail',
            billingPhoneNumber: 'billingPhoneNumber',
            billingWhatsapp: 'billingWhatsapp',
            purchasesEmail: 'purchasesEmail',
            purchasesPhoneNumber: 'purchasesPhoneNumber',
            purchasesWhatsapp: 'purchasesWhatsapp',
            salesEmail: 'salesEmail',
            salesPhoneNumber: 'salesPhoneNumber',
            salesWhatsapp: 'salesWhatsapp',
            shippingAddress: 'shippingAddress',
          },
        },
      };

      mockRequest.body = mockBody;

      const mockUser: UserEntity = {
        id: 'user-id-123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company-id-123'],
        role: UserRole.ADMIN,
        state: UserState.ACTIVE,
      };

      const mockCompany: CompanyEntity = {
        id: 'company-id-123',
        name: 'Test Company',
        description: 'A test company',
        tributaryId: 'XAXX010101000',
        country: 'MX',
        contactInformation: null,
      };

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);
      jest.spyOn(UserUseCase, 'register').mockResolvedValue({ user: mockUser, company: mockCompany });

      await UserController.register(mockRequest as Request, mockResponse as Response);

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(UserUseCase.register).toHaveBeenCalledWith({
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
          contactInformation: {
            billingAddress: 'billingAddress',
            billingEmail: 'billingEmail',
            billingPhoneNumber: 'billingPhoneNumber',
            billingWhatsapp: 'billingWhatsapp',
            mainAddress: 'mainAddress',
            representativeName: 'representativeName',
            mainEmail: 'mainEmail',
            mainPhoneNumber: 'mainPhoneNumber',
            mainWhatsapp: 'mainWhatsapp',
            purchasesEmail: 'purchasesEmail',
            purchasesPhoneNumber: 'purchasesPhoneNumber',
            purchasesWhatsapp: 'purchasesWhatsapp',
            salesEmail: 'salesEmail',
            salesPhoneNumber: 'salesPhoneNumber',
            salesWhatsapp: 'salesWhatsapp',
            shippingAddress: 'shippingAddress',
          },
        },
      });
      expect(mockResponse.send).toHaveBeenCalledWith({
        user: mockUser,
        company: mockCompany,
      });
    });

    it('should register a user successfully with default values for contact information', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
          contactInformation: {},
        },
      };

      mockRequest.body = mockBody;

      const mockUser: UserEntity = {
        id: 'user-id-123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company-id-123'],
        role: UserRole.ADMIN,
        state: UserState.ACTIVE,
      };

      const mockCompany: CompanyEntity = {
        id: 'company-id-123',
        name: 'Test Company',
        description: 'A test company',
        tributaryId: 'XAXX010101000',
        country: 'MX',
        contactInformation: null,
      };

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);
      jest.spyOn(UserUseCase, 'register').mockResolvedValue({ user: mockUser, company: mockCompany });

      await UserController.register(mockRequest as Request, mockResponse as Response);

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(UserUseCase.register).toHaveBeenCalledWith({
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
          contactInformation: {
            mainAddress: null,
            representativeName: null,
            mainEmail: null,
            mainPhoneNumber: null,
            mainWhatsapp: null,
            billingAddress: null,
            billingEmail: null,
            billingPhoneNumber: null,
            billingWhatsapp: null,
            purchasesEmail: null,
            purchasesPhoneNumber: null,
            purchasesWhatsapp: null,
            salesEmail: null,
            salesPhoneNumber: null,
            salesWhatsapp: null,
            shippingAddress: null,
          },
        },
      });
      expect(mockResponse.send).toHaveBeenCalledWith({
        user: mockUser,
        company: mockCompany,
      });
    });

    it('should return 400 if schema validation fails', async () => {
      const mockBody = {
        user: {
          email: 'invalid-email',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
        },
      };

      mockRequest.body = mockBody;

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => {
        throw new Error('Invalid schema');
      });

      await expect(UserController.register(mockRequest as Request, mockResponse as Response))
        .rejects.toThrow('Invalid schema');

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(mockResponse.send).not.toHaveBeenCalled();
    });

    it('should return 400 for invalid country', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: '12345678901',
          country: 'INVALID',
        },
      };

      mockRequest.body = mockBody;

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);

      await expect(UserController.register(mockRequest as Request, mockResponse as Response))
        .rejects.toThrow('Invalid country');

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(mockResponse.send).not.toHaveBeenCalled();
    });

    it('should return 400 for invalid tributary ID format', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'invalid-rfc',
          country: 'MX',
        },
      };

      mockRequest.body = mockBody;

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);

      await expect(UserController.register(mockRequest as Request, mockResponse as Response))
        .rejects.toThrow('Invalid tributary ID for country MX');

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(mockResponse.send).not.toHaveBeenCalled();
    });

    it('should return 409 if user already exists', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
        },
      };

      mockRequest.body = mockBody;

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);
      jest.spyOn(UserUseCase, 'register').mockRejectedValue(new Error('Email already exists'));

      await expect(UserController.register(mockRequest as Request, mockResponse as Response))
        .rejects.toThrow('Email already exists');

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(mockResponse.send).not.toHaveBeenCalled();
    });
  });

  describe('getClaims', () => {
    it('should return claims for a valid user', async () => {
      const userId = 'user-id-123';
      mockRequest.params = { userId };

      const mockUser: UserEntity = {
        id: userId,
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company-id-123'],
        role: UserRole.ADMIN,
        state: UserState.ACTIVE,
      };

      jest.spyOn(UserUseCase, 'retrieveUser').mockResolvedValue(mockUser);

      await UserController.getClaims(mockRequest as Request<{userId: string}>, mockResponse as Response);

      expect(UserUseCase.retrieveUser).toHaveBeenCalledWith({ userId });
      expect(mockResponse.send).toHaveBeenCalledWith({
        metadata: {
          claims: {
            [CLAIMS_NAMESPACE]: {
              'user-id': userId,
              'company-id': 'company-id-123',
            },
            'https://hasura.io/jwt/claims': {
              'x-hasura-allowed-roles': [HASURA_DEFAULT_ROLE],
              'x-hasura-default-role': HASURA_DEFAULT_ROLE,
              'x-hasura-user-id': userId,
              'x-hasura-company-id': 'company-id-123',
            },
          },
        },
      });
    });

    it('should throw error if user is not found', async () => {
      const userId = 'non-existent-user';
      mockRequest.params = { userId };

      jest.spyOn(UserUseCase, 'retrieveUser').mockRejectedValue(new Error('User not found'));

      await expect(UserController.getClaims(mockRequest as Request<{userId: string}>, mockResponse as Response))
        .rejects.toThrow('User not found');

      expect(UserUseCase.retrieveUser).toHaveBeenCalledWith({ userId });
      expect(mockResponse.send).not.toHaveBeenCalled();
    });
  });

  describe('postUsers', () => {
    beforeEach(() => {
      mockRequest = {
        body: [
          {
            email: '<EMAIL>',
            name: 'User1',
            lastName: 'One',
            phoneNumber: '1234567890',
            role: UserRole.USER,
          },
          {
            email: '<EMAIL>',
            name: 'User2',
            lastName: 'Two',
            phoneNumber: '0987654321',
            role: UserRole.ADMIN,
          },
        ],
        authorization: {
          userId: 'admin-user-id',
          companyId: 'company-1',
        },
        params: {
          companyId: 'company-1',
        },
      };
      mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
      };
    });

    it('should invite users and return 200 with response', async () => {
      const mockResult: UserResponseSchemaShape[] = [
        {
          id: 'user-id-1',
          email: '<EMAIL>',
          name: 'User1',
          lastName: 'One',
          phoneNumber: '1234567890',
          companies: ['company-1'],
          role: UserRole.USER,
          state: UserState.ACTIVE,
        },
        {
          id: 'user-id-2',
          email: '<EMAIL>',
          name: 'User2',
          lastName: 'Two',
          phoneNumber: '0987654321',
          companies: ['company-1'],
          role: UserRole.ADMIN,
          state: UserState.ACTIVE,
        },
      ];

      (InviteUsersToCompanyUseCase.apply as jest.Mock).mockResolvedValue(mockResult);
      (HandlersHttp.responseAdapter as jest.Mock).mockImplementation((data) => data);

      await UserController.postUsers(
        mockRequest as Request<{ companyId: string }, unknown, typeof mockRequest.body>,
        mockResponse as Response<UserResponseSchemaShape[]>,
      );

      expect(InviteUsersToCompanyUseCase.apply).toHaveBeenCalledWith({
        companyId: 'company-1',
        userId: 'admin-user-id',
        invitedUsers: [
          {
            email: '<EMAIL>',
            name: 'User1',
            lastName: 'One',
            phoneNumber: '1234567890',
            role: UserRole.USER,
          },
          {
            email: '<EMAIL>',
            name: 'User2',
            lastName: 'Two',
            phoneNumber: '0987654321',
            role: UserRole.ADMIN,
          },
        ],
      });
      expect(HandlersHttp.responseAdapter).toHaveBeenCalledWith(mockResult);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResult);
    });

    it('should throw if schema validation fails', async () => {
      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementationOnce(() => {
        throw new Error('Invalid schema');
      });

      await expect(UserController.postUsers(
        mockRequest as Request<{ companyId: string }, unknown, typeof mockRequest.body>,
        mockResponse as Response<UserResponseSchemaShape[]>,
      )).rejects.toThrow('Invalid schema');

      expect(InviteUsersToCompanyUseCase.apply).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
    });

    it('should propagate errors from InviteUsersToCompanyUseCase', async () => {
      (InviteUsersToCompanyUseCase.apply as jest.Mock).mockRejectedValue(new Error('Use case error'));

      await expect(UserController.postUsers(
        mockRequest as Request<{ companyId: string }, unknown, typeof mockRequest.body>,
        mockResponse as Response<UserResponseSchemaShape[]>,
      )).rejects.toThrow('Use case error');

      expect(mockResponse.json).not.toHaveBeenCalled();
    });
  });

  describe('patchUsers', () => {
    beforeEach(() => {
      mockRequest = {
        body: {
          email: '<EMAIL>',
          name: 'User1',
          lastName: 'One',
          phoneNumber: '1234567890',
          role: UserRole.USER,
        },
        authorization: {
          userId: 'admin-user-id',
          companyId: 'company-1',
        },
        params: {
          userId: 'user-1',
        },
      };
      mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
      };
    });

    it('should update users and return 200 with response', async () => {
      const mockResult: UserResponseSchemaShape = {
        id: 'user-id-1',
        email: '<EMAIL>',
        name: 'User1',
        lastName: 'One',
        phoneNumber: '1234567890',
        companies: ['company-1'],
        role: UserRole.USER,
        state: UserState.ACTIVE,
      };

      (UpdateUserUseCase.apply as jest.Mock).mockResolvedValue(mockResult);
      (HandlersHttp.responseAdapter as jest.Mock).mockImplementation((data) => data);

      await UserController.patchUsers(
        mockRequest as Request<{ userId: string }, unknown, typeof mockRequest.body>,
        mockResponse as Response<UserResponseSchemaShape>,
      );

      expect(UpdateUserUseCase.apply).toHaveBeenCalledWith({
        userId: 'user-1',
        updaterUserId: 'admin-user-id',
        updates: {
          email: '<EMAIL>',
          name: 'User1',
          lastName: 'One',
          phoneNumber: '1234567890',
          role: UserRole.USER,
        },
      });
      expect(HandlersHttp.responseAdapter).toHaveBeenCalledWith(mockResult);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResult);
    });

    it('should throw if schema validation fails', async () => {
      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementationOnce(() => {
        throw new Error('Invalid schema');
      });

      await expect(UserController.patchUsers(
        mockRequest as Request<{ userId: string }, unknown, typeof mockRequest.body>,
        mockResponse as Response<UserResponseSchemaShape>,
      )).rejects.toThrow('Invalid schema');

      expect(UpdateUserUseCase.apply).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
    });

    it('should propagate errors from UpdateUserUseCase.apply', async () => {
      (UpdateUserUseCase.apply as jest.Mock).mockRejectedValue(new Error('Use case error'));

      await expect(UserController.patchUsers(
        mockRequest as Request<{ userId: string }, unknown, typeof mockRequest.body>,
        mockResponse as Response<UserResponseSchemaShape>,
      )).rejects.toThrow('Use case error');

      expect(mockResponse.json).not.toHaveBeenCalled();
    });
  });

  describe('getUserIdByEmail', () => {
    beforeEach(() => {
      mockRequest = {
        query: {
          email: '<EMAIL>',
        },
      };
      mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
      };
      jest.clearAllMocks();
    });

    it('should validate input and return user id', async () => {
      (GetUserIdByEmailUseCase.apply as jest.Mock).mockResolvedValue('user-123');

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementationOnce(() => true);

      await UserController.getUserIdByEmail(
        mockRequest as Request<unknown, unknown, unknown, { email: string }>,
        mockResponse as Response<{ userId: string }>,
      );

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(GetUserByEmailSchema, { email: '<EMAIL>' });
      expect(GetUserIdByEmailUseCase.apply).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({ userId: 'user-123' });
    });

    it('should throw if schema validation fails', async () => {
      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementationOnce(() => {
        throw new Error('Invalid schema');
      });

      await expect(UserController.getUserIdByEmail(
        mockRequest as Request<unknown, unknown, unknown, { email: string }>,
        mockResponse as Response<{ userId: string }>,
      )).rejects.toThrow('Invalid schema');

      expect(GetUserIdByEmailUseCase.apply).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
    });

    it('should propagate errors from use case', async () => {
      (GetUserIdByEmailUseCase.apply as jest.Mock).mockRejectedValue(new Error('User not found'));

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementationOnce(() => true);

      await expect(UserController.getUserIdByEmail(
        mockRequest as Request<unknown, unknown, unknown, { email: string }>,
        mockResponse as Response<{ userId: string }>,
      )).rejects.toThrow('User not found');

      expect(mockResponse.json).not.toHaveBeenCalled();
    });
  });
});
