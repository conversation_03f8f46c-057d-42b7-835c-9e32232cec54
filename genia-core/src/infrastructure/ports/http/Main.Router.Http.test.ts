import express from 'express';

import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import MainRouterHttp from '#infrastructure/ports/http/Main.Router.Http';
import CatalogRouter from '#infrastructure/ports/http/resources/catalog/Catalog.Router';
import CatalogDiscountRouter from '#infrastructure/ports/http/resources/catalogDiscount/CatalogDiscount.Router';
import ClientRouter from '#infrastructure/ports/http/resources/client/Client.Router';
import CompanyRouter from '#infrastructure/ports/http/resources/company/Company.Router';
import IntegrationRouter from '#infrastructure/ports/http/resources/integration/Integration.Router';
import InventoryRouter from '#infrastructure/ports/http/resources/inventory/Inventory.Router';
import InvitationRouter from '#infrastructure/ports/http/resources/invitation/Invitation.Router';
import MediaRouter from '#infrastructure/ports/http/resources/media/Media.Router';
import ProviderRouter from '#infrastructure/ports/http/resources/provider/Provider.Router';
import PurchaseOrderRouter from '#infrastructure/ports/http/resources/purchaseOrders/PurchaseOrder.Router';
import QuoteRouter from '#infrastructure/ports/http/resources/quote/Quote.Router';
import SaleOrdersRouter from '#infrastructure/ports/http/resources/saleOrders/SaleOrder.Router';
import StorageController from '#infrastructure/ports/http/resources/storage/controllers/Storage.Controller';
import StoreDiscountRouter from '#infrastructure/ports/http/resources/storeDiscount/StoreDiscount.Router';
import UsersRouter from '#infrastructure/ports/http/resources/users/User.Router';

jest.unmock('#infrastructure/ports/http/Main.Router.Http');

const useMokck = jest.fn();

jest.mock('express', () => ({
  Router: jest.fn(() => ({
    use: useMokck,
    post: useMokck,
  })),
}));

describe('MainRouterHttp', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call the correct routes', () => {
    MainRouterHttp.getRouter();

    expect(express.Router).toHaveBeenCalled();
    expect(express.Router().use).toHaveBeenCalledWith('/users', UsersRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/catalog', CatalogRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/inventory', InventoryRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/providers', ProviderRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/clients', ClientRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/catalog-discount', CatalogDiscountRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/store-discount', StoreDiscountRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/purchase-orders', PurchaseOrderRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/sale-orders', SaleOrdersRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/quotes', QuoteRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/media', MediaRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/invitations', InvitationRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/integrations', IntegrationRouter.getRoutes());
    expect(express.Router().use).toHaveBeenCalledWith('/company', CompanyRouter.getRoutes());
    expect(express.Router().post).toHaveBeenCalledWith('/signed-url', HandlersHttp.handleAndCatch(StorageController.postSignedUrl));
  });
});
