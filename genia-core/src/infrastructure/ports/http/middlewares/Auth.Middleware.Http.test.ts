import { Request, Response } from 'express';
import { auth } from 'express-oauth2-jwt-bearer';

import EnvConfiguration from '#infrastructure/configurations/Env.configuration';
import ErrorsHttp from '#infrastructure/ports/http/Errors.Http';
import AuthMiddlewareHttp from '#infrastructure/ports/http/middlewares/Auth.Middleware.Http';

jest.unmock('#infrastructure/ports/http/middlewares/Auth.Middleware.Http');
jest.unmock('#infrastructure/ports/http/Errors.Http');

jest.mock('#infrastructure/configurations/Env.configuration', () => ({
  getEnv: jest.fn(() => ({ JWT_SECRET: 'fake_jwt_secret', CLAIMS_NAMESPACE: 'https://claims.io' })),
}));

const { CLAIMS_NAMESPACE } = EnvConfiguration.getEnv();
describe('AuthMiddlewareHttp', () => {
  let mockRequest: Request;
  const mockResponse = {} as Response;
  const mockNext = jest.fn();

  beforeEach(() => {
    mockRequest = {
      headers: {
        authorization: 'Bearer validtoken',
      },
      auth: {
        payload: {
          [CLAIMS_NAMESPACE]: {
            'company-id': 'fake-company-id',
            'user-id': 'fake-user-id',
          },
        },
      },
    } as Request;
    jest.clearAllMocks();
  });

  describe('validateAccess', () => {
    it('Should call next and return if route in whitelist', () => {
      (mockRequest as unknown as {path: string}).path = '/users/register';
      AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext);
      expect(mockNext).toHaveBeenCalledWith();
      expect(auth()).not.toHaveBeenCalled();
    });

    it('should call next with no arguments if token is valid', () => {
      AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext);
      expect(auth()).toHaveBeenCalledWith(mockRequest, mockResponse, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should throw an error if authorization header is not present', () => {
      mockRequest.headers.authorization = '';

      expect(() => AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext)).toThrow(new ErrorsHttp.Unauthorized());
    });

    it('should call next with an error if token is invalid', () => {
      mockRequest.headers.authorization = 'Bearer invalidtoken';
      const error = new Error('Invalid token');

      error.name = 'JsonWebTokenError';

      (auth() as jest.Mock).mockImplementation((req, res, next) => next(error));

      AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new ErrorsHttp.Unauthorized(error.name));
    });

    it('Should throw error on jwtcheck error', () => {
      const error = new Error('Invalid token');

      (auth() as jest.Mock).mockImplementation(() => {
        throw error;
      });

      expect(() => AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext)).toThrow(new ErrorsHttp.Unauthorized(error.message));
    });

    it('should whitelist /media/:id endpoints', () => {
      (mockRequest as unknown as { path: string }).path = '/media/abc123';
      AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext);
      expect(mockNext).toHaveBeenCalledWith();
      expect(auth()).not.toHaveBeenCalled();
    });

    it('should not whitelist /media or /media/ (no id)', () => {
      (mockRequest as unknown as { path: string }).path = '/media';
      expect(() => AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext)).toThrow(ErrorsHttp.Unauthorized);

      (mockRequest as unknown as { path: string }).path = '/media/';
      expect(() => AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext)).toThrow(ErrorsHttp.Unauthorized);
    });

    it('should not whitelist /media/abc/def (extra path segments)', () => {
      (mockRequest as unknown as { path: string }).path = '/media/abc/def';
      expect(() => AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext)).toThrow(ErrorsHttp.Unauthorized);
    });

    it('should whitelist /users/:id with GET method', () => {
      (mockRequest as unknown as { path: string, method: string }).path = '/users?email=<EMAIL>';
      mockRequest.method = 'GET';
      AuthMiddlewareHttp.validateAccess(mockRequest, mockResponse, mockNext);
      expect(mockNext).toHaveBeenCalledWith();
      expect(auth()).not.toHaveBeenCalled();
    });
  });
});
