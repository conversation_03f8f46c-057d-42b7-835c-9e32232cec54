import { Router } from 'express';

import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import CatalogRouter from '#infrastructure/ports/http/resources/catalog/Catalog.Router';
import CatalogDiscountRouter from '#infrastructure/ports/http/resources/catalogDiscount/CatalogDiscount.Router';
import ClientRouter from '#infrastructure/ports/http/resources/client/Client.Router';
import CompanyRouter from '#infrastructure/ports/http/resources/company/Company.Router';
import IntegrationRouter from '#infrastructure/ports/http/resources/integration/Integration.Router';
import InventoryRouter from '#infrastructure/ports/http/resources/inventory/Inventory.Router';
import InvitationRouter from '#infrastructure/ports/http/resources/invitation/Invitation.Router';
import MediaRouter from '#infrastructure/ports/http/resources/media/Media.Router';
import ProviderRouter from '#infrastructure/ports/http/resources/provider/Provider.Router';
import PurchaseOrderRouter from '#infrastructure/ports/http/resources/purchaseOrders/PurchaseOrder.Router';
import QuoteRouter from '#infrastructure/ports/http/resources/quote/Quote.Router';
import SaleOrdersRouter from '#infrastructure/ports/http/resources/saleOrders/SaleOrder.Router';
import StorageController from '#infrastructure/ports/http/resources/storage/controllers/Storage.Controller';
import StoreDiscountRouter from '#infrastructure/ports/http/resources/storeDiscount/StoreDiscount.Router';
import UsersRouter from '#infrastructure/ports/http/resources/users/User.Router';

function getRouter(): Router {
  const router = Router();

  router.use('/users', UsersRouter.getRoutes());
  router.use('/inventory', InventoryRouter.getRoutes());
  router.use('/catalog', CatalogRouter.getRoutes());
  router.use('/company', CompanyRouter.getRoutes());
  router.use('/providers', ProviderRouter.getRoutes());
  router.use('/clients', ClientRouter.getRoutes());
  router.use('/catalog-discount', CatalogDiscountRouter.getRoutes());
  router.use('/store-discount', StoreDiscountRouter.getRoutes());
  router.use('/purchase-orders', PurchaseOrderRouter.getRoutes());
  router.use('/sale-orders', SaleOrdersRouter.getRoutes());
  router.use('/quotes', QuoteRouter.getRoutes());
  router.use('/media', MediaRouter.getRoutes());
  router.use('/invitations', InvitationRouter.getRoutes());
  router.use('/integrations', IntegrationRouter.getRoutes());

  router.post('/signed-url', HandlersHttp.handleAndCatch(StorageController.postSignedUrl));

  return router;
}

export default {
  getRouter,
};
