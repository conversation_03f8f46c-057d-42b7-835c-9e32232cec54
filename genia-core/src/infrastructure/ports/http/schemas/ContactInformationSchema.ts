import { JSONSchemaType } from 'ajv';

export interface ContacInformationSchemaShape {
  billingEmail?: string | null;
  billingPhoneNumber?: string | null;
  billingWhatsapp?: string | null;
  purchasesEmail?: string | null;
  purchasesPhoneNumber?: string | null;
  purchasesWhatsapp?: string | null;
  salesEmail?: string | null;
  salesPhoneNumber?: string | null;
  salesWhatsapp?: string | null;
  shippingAddress?: string | null;
  billingAddress?: string | null;
  mainEmail?: string | null;
  mainPhoneNumber?: string | null;
  mainWhatsapp?: string | null;
  mainAddress?: string | null;
  representativeName?: string | null;
}

const ContactInformationSchema: JSONSchemaType<ContacInformationSchemaShape> = {
  type: 'object',
  properties: {
    billingEmail: { type: 'string', format: 'email', nullable: true },
    billingPhoneNumber: { type: 'string', pattern: '^\\+?[0-9]*$', nullable: true },
    billingWhatsapp: { type: 'string', pattern: '^\\+?[0-9]*$', nullable: true },
    purchasesEmail: { type: 'string', format: 'email', nullable: true },
    purchasesPhoneNumber: { type: 'string', pattern: '^\\+?[0-9]*$', nullable: true },
    purchasesWhatsapp: { type: 'string', pattern: '^\\+?[0-9]*$', nullable: true },
    salesEmail: { type: 'string', format: 'email', nullable: true },
    salesPhoneNumber: { type: 'string', pattern: '^\\+?[0-9]*$', nullable: true },
    salesWhatsapp: { type: 'string', pattern: '^\\+?[0-9]*$', nullable: true },
    shippingAddress: {
      type: 'string', minLength: 1, maxLength: 255, nullable: true,
    },
    mainEmail: { type: 'string', format: 'email', nullable: true },
    mainPhoneNumber: { type: 'string', pattern: '^\\+?[0-9]*$', nullable: true },
    mainWhatsapp: { type: 'string', pattern: '^\\+?[0-9]*$', nullable: true },
    mainAddress: {
      type: 'string', minLength: 1, maxLength: 255, nullable: true,
    },
    billingAddress: {
      type: 'string', minLength: 1, maxLength: 255, nullable: true,
    },
    representativeName: {
      type: 'string', minLength: 1, maxLength: 80, nullable: true,
    },
  },
  required: [],
  additionalProperties: false,
};

export default ContactInformationSchema;
