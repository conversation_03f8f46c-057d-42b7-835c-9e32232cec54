import CatalogResponseSchema from '#infrastructure/ports/http/resources/catalog/schemas/CatalogResponse.Schema';
import CreateCatalogEntrySchema from '#infrastructure/ports/http/resources/catalog/schemas/CreateCatalogEntry.Schema';
import { paginatedPublicCatalogSchema } from '#infrastructure/ports/http/resources/catalog/schemas/PublicCatalog.Schema';
import CatalogDiscountResponseSchema from '#infrastructure/ports/http/resources/catalogDiscount/schemas/CatalogDiscountResponse.Schema';
import CreateCatalogDiscountEntriesSchema from '#infrastructure/ports/http/resources/catalogDiscount/schemas/CreateCatalogDiscountEntries.Schema';
import ObjectWithCatalogIdsSchema from '#infrastructure/ports/http/resources/catalogDiscount/schemas/ObjectWithCatalogIdsSchema';
import ObjectWithClientIdsSchema from '#infrastructure/ports/http/resources/catalogDiscount/schemas/ObjectWithClientIdsSchema';
import ClientResponseSchema from '#infrastructure/ports/http/resources/client/schemas/ClientResponse.Schema';
import CreateClientEntrySchema from '#infrastructure/ports/http/resources/client/schemas/CreateClientEntry.Schema';
import UpdateClientEntrySchema from '#infrastructure/ports/http/resources/client/schemas/UpdateClientEntry.Schema';
import CompanySchema from '#infrastructure/ports/http/resources/company/schemas/Company.Schema';
import PatchCompanySchema from '#infrastructure/ports/http/resources/company/schemas/PatchCompany.Schema';
import CreateIntegrationsSchema from '#infrastructure/ports/http/resources/integration/schemas/CreateIntegrationEntry.Schema';
import CreateIntegrationResponseSchema from '#infrastructure/ports/http/resources/integration/schemas/CreateIntegrationResponse.Schema';
import CreateInventoryEntrySchema from '#infrastructure/ports/http/resources/inventory/schemas/CreateInventoryEntry.Schema';
import InventoryResponseSchema from '#infrastructure/ports/http/resources/inventory/schemas/InventoryResponse.Schema';
import UpdateInventorySchema from '#infrastructure/ports/http/resources/inventory/schemas/UpdateInventoryEntry.Schema';
import InvitationSchema from '#infrastructure/ports/http/resources/invitation/schemas/Invitation.Schema';
import PatchInvitationSchema from '#infrastructure/ports/http/resources/invitation/schemas/PatchInvitation.Schema';
import PostInvitationSchema from '#infrastructure/ports/http/resources/invitation/schemas/PostInvitation.Schema';
import MediaSchema from '#infrastructure/ports/http/resources/media/schemas/Media.Schema';
import PatchMediaSchema from '#infrastructure/ports/http/resources/media/schemas/PatchMedia.Schema';
import PostMediaSchema from '#infrastructure/ports/http/resources/media/schemas/PostMedia.Schema';
import CreateProviderEntrySchema from '#infrastructure/ports/http/resources/provider/schemas/CreateProviderEntry.Schema';
import ProviderResponseSchema from '#infrastructure/ports/http/resources/provider/schemas/ProviderResponse.Schema';
import UpdateProviderEntrySchema from '#infrastructure/ports/http/resources/provider/schemas/UpdateProviderEntry.Schema';
import PostPurchaseOrderSchema from '#infrastructure/ports/http/resources/purchaseOrders/schemas/PostPurchaseOrder.Schema';
import PostPurchaseOrderResponseSchema from '#infrastructure/ports/http/resources/purchaseOrders/schemas/PostPurchaseOrderResponse.Schema';
import PurchaseOrderConditionsSchema from '#infrastructure/ports/http/resources/purchaseOrders/schemas/PurchaseOrderConditions.Schema';
import PurchaseOrderConditionsResponseSchema from '#infrastructure/ports/http/resources/purchaseOrders/schemas/PurchaseOrderConditionsResponse.Schema';
import PurchaseOrderInventorySuggestionsSchema from '#infrastructure/ports/http/resources/purchaseOrders/schemas/PurchaseOrderInventorySuggestions.Schema';
// eslint-disable-next-line max-len
import PurchaseOrderInventorySuggestionsResponseSchema from '#infrastructure/ports/http/resources/purchaseOrders/schemas/PurchaseOrderInventorySuggestionsResponse.Schema';
import PostQuoteSchema from '#infrastructure/ports/http/resources/quote/schemas/PostQuote.Schema';
import QuoteSchema from '#infrastructure/ports/http/resources/quote/schemas/Quote.Schema';
import PostSaleOrderSchema from '#infrastructure/ports/http/resources/saleOrders/schemas/PostSaleOrder.Schema';
import PostSaleOrderResponseSchema from '#infrastructure/ports/http/resources/saleOrders/schemas/PostSaleOrderResponse.Schema';
import SaleOrderConditionsSchema from '#infrastructure/ports/http/resources/saleOrders/schemas/SaleOrderConditions.Schema';
import SaleOrderConditionsResponseSchema from '#infrastructure/ports/http/resources/saleOrders/schemas/SaleOrderConditionsResponse.Schema';
import SignedUrlSchema from '#infrastructure/ports/http/resources/storage/schemas/SignedUrl.Schema';
import SignedUrlRequestSchema from '#infrastructure/ports/http/resources/storage/schemas/SignedUrlRequest.Schema';
import CreateStoreDiscountEntriesSchema from '#infrastructure/ports/http/resources/storeDiscount/schemas/CreateStoreDiscountEntries.Schema';
import StoreDiscountResponseSchema from '#infrastructure/ports/http/resources/storeDiscount/schemas/StoreDiscountResponse.Schema';
import PatchUserSchema from '#infrastructure/ports/http/resources/users/schemas/PatchUser.Schema';
import PostCompanyUsersSchema from '#infrastructure/ports/http/resources/users/schemas/PostCompanyUsers.Schema';
import RegisterSchema from '#infrastructure/ports/http/resources/users/schemas/Register.Schema';
import RegisterResponseSchema from '#infrastructure/ports/http/resources/users/schemas/RegisterResponse.Schema';
import UserResponseSchema from '#infrastructure/ports/http/resources/users/schemas/UserResponse.Schema';
import PaginatedQuerySchema from '#infrastructure/ports/http/schemas/PaginatedQuery.Schema';

import HttpErrorSchema from '../http/schemas/HttpError.Schema';

const CommonDefinition = {
  components: {
    schemas: {
      HttpErrorSchema,
      RegisterSchema,
      RegisterResponseSchema,
      CreateInventoryEntrySchema,
      InventoryResponseSchema,
      UpdateInventorySchema,
      CreateCatalogEntrySchema,
      CatalogResponseSchema,
      CreateProviderEntrySchema,
      UpdateProviderEntrySchema,
      ProviderResponseSchema,
      CreateClientEntrySchema,
      ClientResponseSchema,
      UpdateClientEntrySchema,
      MediaSchema,
      PostMediaSchema,
      CreateCatalogDiscountEntriesSchema,
      CatalogDiscountResponseSchema,
      ObjectWithClientIdsSchema,
      ObjectWithCatalogIdsSchema,
      PaginatedPublicCatalogSchema: paginatedPublicCatalogSchema,
      PaginatedQuerySchema,
      PurchaseOrderConditionsSchema,
      PurchaseOrderConditionsResponseSchema,
      PurchaseOrderInventorySuggestionsSchema,
      PurchaseOrderInventorySuggestionsResponseSchema,
      PostPurchaseOrderSchema,
      PostPurchaseOrderResponseSchema,
      PostSaleOrderSchema,
      PostSaleOrderResponseSchema,
      SaleOrderConditionsSchema,
      SaleOrderConditionsResponseSchema,
      StoreDiscountResponseSchema,
      CreateStoreDiscountEntriesSchema,
      PostQuoteSchema,
      QuoteSchema,
      SignedUrlRequestSchema,
      SignedUrlSchema,
      PatchMediaSchema,
      UserResponseSchema,
      PatchUserSchema,
      PostCompanyUsersSchema,
      InvitationSchema,
      PostInvitationSchema,
      PatchInvitationSchema,
      CreateIntegrationsSchema,
      CreateIntegrationResponseSchema,
      PatchCompanySchema,
      CompanySchema,
    },
    responses: {
      '4XX': {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/HttpErrorSchema',
            },
          },
        },
      },
      '5XX': {
        description: '5xx Errors, if it is thrown inside the application it will have this shape, however could be empty.',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/HttpErrorSchema',
            },
          },
        },
      },
      default: {
        description: 'Unexpected error It could have any shape.',
      },
    },
  },
};

export default CommonDefinition;
