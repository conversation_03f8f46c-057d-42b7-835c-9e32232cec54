import { v4 } from 'uuid';

import ClientEntity from '#domain/aggregates/client/Client.Entity';
import { seedCompanies } from '#infrastructure/databases/core/seeds/02_Company';
import { seedClients } from '#infrastructure/databases/core/seeds/14_Client';
import { seedStoreDiscount } from '#infrastructure/databases/core/seeds/17_StoreDiscount';
import { seedStoreDiscountClient } from '#infrastructure/databases/core/seeds/19_StoreDiscountClient';
import CoreClientRepository from '#infrastructure/implementations/client/repositories/CoreClient.Repository';
import DatabaseTesting from '#infrastructure/testing/Database.Testing';

jest.unmock('pg');
jest.unmock('uuid');

jest.unmock('#domain/aggregates/client/Client.Operator');
jest.unmock('#infrastructure/testing/Database.Testing');
jest.unmock('#infrastructure/implementations/client/repositories/CoreClient.Repository');
jest.unmock('#infrastructure/databases/core/seeds/02_Company');
jest.unmock('#infrastructure/databases/core/seeds/14_Client');
jest.unmock('#infrastructure/databases/core/seeds/17_StoreDiscount');
jest.unmock('#infrastructure/databases/core/seeds/19_StoreDiscountClient');
jest.unmock('#domain/common/aggregates/contactInformation/ContactInformation.Operator');

describe('CoreClientRepository', () => {
  const mockDate = new Date('2023-01-01');
  jest.useFakeTimers();
  jest.setSystemTime(mockDate);

  beforeEach(async () => {
    await DatabaseTesting.setUpDatabase();
  });

  afterEach(async () => {
    await DatabaseTesting.tearDownDatabase();
  });

  describe('saveMany', () => {
    const client: ClientEntity = {
      id: v4(),
      name: 'kavak',
      tributaryId: '*********',
      clientCompanyId: null,
      companyId: seedCompanies[0].id,
      storeDiscounts: [],
      contactInformation: null,
      notes: null,
      createdAt: mockDate,
      updatedAt: mockDate,
    };

    it('Should save multiple records without storeDiscountClients', async () => {
      const got = await CoreClientRepository.saveMany([client]);

      expect(got).toEqual([client]);
    });

    it('Should save multiple records with storeDiscountClients', async () => {
      const clients: ClientEntity[] = [{
        ...client,
        storeDiscounts: [seedStoreDiscount[0].id],
      }];

      const got = await CoreClientRepository.saveMany(clients);

      expect(got).toEqual(clients);
    });

    it('Should save multiple records with contacInformation', async () => {
      const clients: ClientEntity[] = [{
        ...client,
        contactInformation: {
          salesEmail: null,
          salesPhoneNumber: '573126686868',
          salesWhatsapp: '57668686868',
          purchasesEmail: null,
          purchasesPhoneNumber: '57668686868',
          purchasesWhatsapp: '57668686868',
          billingEmail: '<EMAIL>',
          billingPhoneNumber: '57668686868',
          billingWhatsapp: '57668686868',
          billingAddress: '123 Main St',
          mainEmail: '<EMAIL>',
          mainPhoneNumber: '57668686868',
          mainWhatsapp: '57668686868',
          mainAddress: '123 Main St',
          representativeName: 'John Doe',
          shippingAddress: '123 Main St',
        },
      }];

      const got = await CoreClientRepository.saveMany(clients);

      expect(got).toEqual(clients);
    });
  });

  describe('save', () => {
    const client: ClientEntity = {
      id: v4(),
      name: 'kavak',
      tributaryId: '*********',
      clientCompanyId: null,
      companyId: seedCompanies[0].id,
      storeDiscounts: [],
      contactInformation: null,
      notes: null,
      createdAt: mockDate,
      updatedAt: mockDate,
    };

    it('Should save a record without storeDiscountClients', async () => {
      const got = await CoreClientRepository.save(client);

      expect(got).toEqual(client);
    });

    it('Should save a record with storeDiscountClients', async () => {
      const expectedClient = { ...client, storeDiscounts: [seedStoreDiscount[0].id] };

      const got = await CoreClientRepository.save(expectedClient);

      expect(got).toEqual(expectedClient);
    });

    it('Should save a record with contactInformation', async () => {
      const expectedClient: ClientEntity = {
        ...client,
        contactInformation: {
          salesEmail: null,
          salesPhoneNumber: '573126686868',
          salesWhatsapp: '57668686868',
          purchasesEmail: null,
          purchasesPhoneNumber: '57668686868',
          purchasesWhatsapp: '57668686868',
          billingEmail: '<EMAIL>',
          billingPhoneNumber: '57668686868',
          billingWhatsapp: '57668686868',
          billingAddress: '123 Main St',
          mainEmail: '<EMAIL>',
          mainPhoneNumber: '57668686868',
          mainWhatsapp: '57668686868',
          mainAddress: '123 Main St',
          representativeName: 'John Doe',
          shippingAddress: '123 Main St',
        },
      };

      const got = await CoreClientRepository.save(expectedClient);

      expect(got).toEqual(expectedClient);
    });
  });

  describe('findOneById', () => {
    it('should return undefined if the client is not fount', async () => {
      const found = await CoreClientRepository.findOneById(v4());

      expect(found).toBeUndefined();
    });

    it('should return the client correctly', async () => {
      const storeDiscounts = seedStoreDiscountClient.filter(({ clientId }) => clientId === seedClients[0].id).map(({ storeDiscountId }) => storeDiscountId);

      const expected: ClientEntity = {
        id: seedClients[0].id,
        name: seedClients[0].name,
        tributaryId: seedClients[0].tributaryId,
        clientCompanyId: seedClients[0].clientCompanyId,
        contactInformation: null,
        companyId: seedClients[0].companyId,
        storeDiscounts,
        notes: null,
        createdAt: mockDate,
        updatedAt: mockDate,
      };
      const found = await CoreClientRepository.findOneById(seedClients[0].id);

      expect(found).toEqual(expected);
    });
  });

  describe('findManyByIds', () => {
    it('Should return multiple records', async () => {
      const got = await CoreClientRepository.findManyByIds([seedClients[0].id]);

      const expected: ClientEntity = {
        id: seedClients[0].id,
        name: seedClients[0].name,
        tributaryId: seedClients[0].tributaryId,
        clientCompanyId: seedClients[0].clientCompanyId,
        companyId: seedClients[0].companyId,
        storeDiscounts: [],
        contactInformation: null,
        notes: null,
        createdAt: mockDate,
        updatedAt: mockDate,
      };

      expect(got).toEqual([expected]);
    });
  });

  describe('findManyByClientCompanyIds', () => {
    it('Should return empty array when no clients match the provided company IDs', async () => {
      const got = await CoreClientRepository.findManyByClientCompanyIds([v4()]);

      expect(got).toEqual([]);
    });

    it('Should return multiple records that match the provided client company IDs', async () => {
      // Find clients with clientCompanyId property set (not null)
      const clientsWithCompanyId = seedClients.filter((client) => client.clientCompanyId !== null);

      if (clientsWithCompanyId.length > 0) {
        // Use the existing client company IDs from seed data
        const clientCompanyIds = [...new Set(clientsWithCompanyId.map((client) => client.clientCompanyId))];

        const got = await CoreClientRepository.findManyByClientCompanyIds(clientCompanyIds);

        // Create expected result
        const expected = clientsWithCompanyId.map<ClientEntity>((client) => ({
          id: client.id,
          name: client.name,
          tributaryId: client.tributaryId,
          clientCompanyId: client.clientCompanyId,
          companyId: client.companyId,
          storeDiscounts: [],
          contactInformation: null,
          notes: null,
          createdAt: mockDate,
          updatedAt: mockDate,
        }));

        expect(got).toEqual(expected);
      } else {
        // If there are no clients with clientCompanyId in seeds, create and save one for testing
        const testCompanyId = v4();
        const testClient: ClientEntity = {
          id: v4(),
          name: 'test client',
          tributaryId: '*********',
          clientCompanyId: testCompanyId,
          companyId: seedCompanies[0].id,
          contactInformation: null,
          storeDiscounts: [],
          notes: null,
          createdAt: mockDate,
          updatedAt: mockDate,
        };

        await CoreClientRepository.save(testClient);

        const got = await CoreClientRepository.findManyByClientCompanyIds([testCompanyId]);

        expect(got).toEqual([testClient]);
      }
    });
  });
});
