import { Knex } from 'knex';

import ProviderRepository from '#application/provider/repositories/Provider.Repository';
import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import ProviderOperator from '#domain/aggregates/provider/Provider.Operator';
import { ContactInformationEntity } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';
import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import CoreDatabase from '#infrastructure/databases/core/Core.Database';
import { dbProvider } from '#infrastructure/databases/core/types';

const providerSelect: Array<keyof dbProvider> = ['id', 'name', 'tributaryId', 'providerCompanyId', 'companyId'];

async function saveMany(providers: ProviderEntity[]): Promise<ProviderEntity[]> {
  const db = CoreDatabase.getClient();

  return db.transaction<ProviderEntity[]>(async (trx: Knex.Transaction) => {
    const providersToSave = providers.map(({ contactInformation, ...provider }) => (provider));
    const providerRecords = await trx(TableNamesConfiguration.PROVIDER).insert(providersToSave).onConflict('id').merge()
      .returning(providerSelect);

    const providerIds = providers.map(({ id }) => id);
    await trx(TableNamesConfiguration.CONTACT_INFORMATION).whereIn('entityId', providerIds).del();

    const providersWithContactInformation = providers.filter(({ contactInformation }) => (contactInformation));
    const contactInformationsToSave = providersWithContactInformation.map(({ contactInformation, id }) => ({
      ...contactInformation!,
      entity: ContactInformationEntity.PROVIDER,
      entityId: id,
    }));

    if (contactInformationsToSave.length) await trx(TableNamesConfiguration.CONTACT_INFORMATION).insert(contactInformationsToSave);

    const contactInformationMap = new Map(providers.map(({ id, contactInformation }) => [id, contactInformation]));

    return providerRecords.map(({ createdAt, updatedAt, ...provider }) => ProviderOperator.build(
      { ...provider, contactInformation: contactInformationMap.get(provider.id) },
    ));
  });
}

async function deleteById(id: string): Promise<undefined> {
  const db = CoreDatabase.getClient();

  return db(TableNamesConfiguration.PROVIDER).where({ id }).del();
}

async function save(provider: ProviderEntity): Promise<ProviderEntity> {
  const db = CoreDatabase.getClient();

  return db.transaction<ProviderEntity>(async (trx: Knex.Transaction) => {
    const { contactInformation, ...providerToSave } = provider;

    const record = (await trx(TableNamesConfiguration.PROVIDER).insert(providerToSave).onConflict('id').merge()
      .returning(providerSelect))[0];

    await trx(TableNamesConfiguration.CONTACT_INFORMATION).where({ entity: ContactInformationEntity.PROVIDER, entityId: record.id }).del();

    if (contactInformation) {
      await trx(TableNamesConfiguration.CONTACT_INFORMATION).insert({ ...contactInformation, entity: ContactInformationEntity.PROVIDER, entityId: record.id })
        .onConflict(['entity', 'entityId']).merge();
    }

    return ProviderOperator.build({ ...record, contactInformation });
  });
}

async function findOneById(id: string): Promise<ProviderEntity | undefined> {
  const db = CoreDatabase.getClient();

  const record = await db(TableNamesConfiguration.PROVIDER).select(providerSelect).where({ id }).first();

  if (!record) return undefined;

  return ProviderOperator.build(record);
}

async function findOneByNameAndCompanyId(name: string, companyId: string): Promise<ProviderEntity | undefined> {
  const db = CoreDatabase.getClient();

  const record = await db(TableNamesConfiguration.PROVIDER).select(providerSelect).where({ name, companyId }).first();

  if (!record) return undefined;

  return ProviderOperator.build(record);
}

async function findManyByNamesAndCompanyId(names: string[], companyId: string): Promise<ProviderEntity[]> {
  const db = CoreDatabase.getClient();

  const records = await db(TableNamesConfiguration.PROVIDER).select(providerSelect).whereIn('name', names).andWhere({ companyId });

  return records.map((record) => ProviderOperator.build(record));
}

async function findProvidersForCompany(companyId: string): Promise<ProviderEntity[]> {
  const db = CoreDatabase.getClient();

  const records = await db(TableNamesConfiguration.PROVIDER).select(providerSelect).where({ companyId });

  return records.map((record) => ProviderOperator.build(record));
}

async function findProviderForCompanyByProviderCompanyId(companyId: string, providerCompanyId: string): Promise<ProviderEntity | undefined> {
  const db = CoreDatabase.getClient();

  const record = await db(TableNamesConfiguration.PROVIDER).select(providerSelect).where({ companyId, providerCompanyId }).first();

  if (!record) return undefined;

  return ProviderOperator.build(record);
}

const CoreProviderRepository: ProviderRepository = {
  saveMany,
  save,
  findOneById,
  findOneByNameAndCompanyId,
  findManyByNamesAndCompanyId,
  deleteById,
  findProvidersForCompany,
  findProviderForCompanyByProviderCompanyId,
};

export default CoreProviderRepository;
