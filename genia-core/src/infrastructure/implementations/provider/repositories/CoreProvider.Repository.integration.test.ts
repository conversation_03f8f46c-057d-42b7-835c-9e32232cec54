import { v4 } from 'uuid';

import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import { seedCompanies } from '#infrastructure/databases/core/seeds/02_Company';
import { seedProviders } from '#infrastructure/databases/core/seeds/21_Provider';
import CoreProviderRepository from '#infrastructure/implementations/provider/repositories/CoreProvider.Repository';
import DatabaseTesting from '#infrastructure/testing/Database.Testing';

jest.unmock('uuid');
jest.unmock('pg');

jest.unmock('#infrastructure/testing/Database.Testing');
jest.unmock('#domain/aggregates/provider/Provider.Operator');
jest.unmock('#infrastructure/databases/core/seeds/02_Company');
jest.unmock('#infrastructure/databases/core/seeds/21_Provider');
jest.unmock('#domain/common/aggregates/contactInformation/ContactInformation.Operator');

jest.unmock('./CoreProvider.Repository');

describe('CoreProviderRepository', () => {
  beforeEach(async () => {
    await DatabaseTesting.setUpDatabase();
  });

  afterEach(async () => {
    await DatabaseTesting.tearDownDatabase();
  });

  describe('saveMany', () => {
    it('should save multiple provider items', async () => {
      const providers: ProviderEntity[] = [
        {
          id: v4(),
          name: 'China Parts On Fire',
          companyId: seedCompanies[0].id,
          providerCompanyId: seedCompanies[0].id,
          tributaryId: '*********',
          contactInformation: null,
        },
        {
          id: v4(),
          name: 'Pitsdepot',
          companyId: seedCompanies[0].id,
          providerCompanyId: seedCompanies[0].id,
          tributaryId: '*********',
          contactInformation: {
            billingAddress: 'billingAddress',
            shippingAddress: 'shippingAddress',
            billingEmail: 'billingEmail',
            billingPhoneNumber: 'billingPhoneNumber',
            billingWhatsapp: 'billingWhatsapp',
            purchasesEmail: 'purchasesEmail',
            purchasesPhoneNumber: 'purchasesPhoneNumber',
            purchasesWhatsapp: 'purchasesWhatsapp',
            mainAddress: 'mainAddress',
            representativeName: 'representativeName',
            mainEmail: 'mainEmail',
            mainPhoneNumber: 'mainPhoneNumber',
            mainWhatsapp: 'mainWhatsapp',
            salesEmail: 'salesEmail',
            salesPhoneNumber: 'salesPhoneNumber',
            salesWhatsapp: 'salesWhatsapp',
          },
        },
      ];

      const saved = await CoreProviderRepository.saveMany(providers);

      expect(saved).toEqual(expect.arrayContaining(providers));
    });
  });

  describe('deleteById', () => {
    it('should delete a provider record by its id', async () => {
      const [{ id }] = await CoreProviderRepository.saveMany([{
        id: v4(), name: 'China Parts On Fire', companyId: seedCompanies[0].id, tributaryId: null, providerCompanyId: null, contactInformation: null,
      }]);

      await CoreProviderRepository.deleteById(id);

      const found = await CoreProviderRepository.findOneById(id);

      expect(found).toBeUndefined();
    });
  });

  describe('save', () => {
    it('should save a provider', async () => {
      const provider: ProviderEntity = {
        id: v4(),
        name: 'China Parts On Fire',
        companyId: seedCompanies[0].id,
        providerCompanyId: seedCompanies[0].id,
        tributaryId: '*********',
        contactInformation: {
          billingAddress: 'billingAddress',
          shippingAddress: 'shippingAddress',
          billingEmail: 'billingEmail',
          billingPhoneNumber: 'billingPhoneNumber',
          billingWhatsapp: 'billingWhatsapp',
          purchasesEmail: 'purchasesEmail',
          purchasesPhoneNumber: 'purchasesPhoneNumber',
          purchasesWhatsapp: 'purchasesWhatsapp',
          salesEmail: 'salesEmail',
          salesPhoneNumber: 'salesPhoneNumber',
          salesWhatsapp: 'salesWhatsapp',
          mainAddress: 'mainAddress',
          representativeName: 'representativeName',
          mainEmail: 'mainEmail',
          mainPhoneNumber: 'mainPhoneNumber',
          mainWhatsapp: 'mainWhatsapp',
        },
      };

      const saved = await CoreProviderRepository.save(provider);

      expect(saved).toEqual(provider);
    });

    it('should save a provider with null contact information', async () => {
      const provider: ProviderEntity = {
        id: v4(),
        name: 'China Parts On Fire',
        companyId: seedCompanies[0].id,
        providerCompanyId: seedCompanies[0].id,
        tributaryId: '*********',
        contactInformation: {
          billingAddress: 'billingAddress',
          shippingAddress: 'shippingAddress',
          billingEmail: 'billingEmail',
          billingPhoneNumber: 'billingPhoneNumber',
          billingWhatsapp: 'billingWhatsapp',
          mainAddress: 'mainAddress',
          representativeName: 'representativeName',
          mainEmail: 'mainEmail',
          mainPhoneNumber: 'mainPhoneNumber',
          mainWhatsapp: 'mainWhatsapp',
          purchasesEmail: 'purchasesEmail',
          purchasesPhoneNumber: 'purchasesPhoneNumber',
          purchasesWhatsapp: 'purchasesWhatsapp',
          salesEmail: 'salesEmail',
          salesPhoneNumber: 'salesPhoneNumber',
          salesWhatsapp: 'salesWhatsapp',
        },
      };

      const saved = await CoreProviderRepository.save(provider);

      expect(saved).toEqual(provider);
    });
  });

  describe('findOneById', () => {
    it('should find a provider', async () => {
      const provider: ProviderEntity = {
        id: seedProviders[0].id,
        name: seedProviders[0].name,
        companyId: seedProviders[0].companyId,
        providerCompanyId: seedProviders[0].providerCompanyId,
        tributaryId: seedProviders[0].tributaryId,
        contactInformation: null,
      };

      const found = await CoreProviderRepository.findOneById(provider.id);

      expect(found).toEqual(provider);
    });

    it('should return undefined when the provider was not found', async () => {
      const found = await CoreProviderRepository.findOneById(v4());

      expect(found).toBeUndefined();
    });
  });

  describe('findOneByNameAndCompanyId', () => {
    it('should find a provider', async () => {
      const provider: ProviderEntity = {
        id: seedProviders[0].id,
        name: seedProviders[0].name,
        companyId: seedProviders[0].companyId,
        providerCompanyId: seedProviders[0].providerCompanyId,
        tributaryId: seedProviders[0].tributaryId,
        contactInformation: null,
      };

      const found = await CoreProviderRepository.findOneByNameAndCompanyId(provider.name, provider.companyId);

      expect(found).toEqual(provider);
    });

    it('should return undefined when the provider was not found', async () => {
      const found = await CoreProviderRepository.findOneByNameAndCompanyId('notfound', v4());

      expect(found).toBeUndefined();
    });
  });

  describe('findManyByNamesAndCompanyId', () => {
    it('should find the providers', async () => {
      const providers: ProviderEntity[] = [{
        id: seedProviders[0].id,
        name: seedProviders[0].name,
        companyId: seedProviders[0].companyId,
        providerCompanyId: seedProviders[0].providerCompanyId,
        tributaryId: seedProviders[0].tributaryId,
        contactInformation: null,
      }];

      const found = await CoreProviderRepository.findManyByNamesAndCompanyId([providers[0].name], providers[0].companyId);

      expect(found).toEqual(providers);
    });
  });

  describe('findProvidersForCompany', () => {
    it('should find all providers for a specific company', async () => {
      // Create a few providers for different companies to test filtering
      const companyId1 = seedCompanies[0].id;
      const companyId2 = seedCompanies[1].id;

      const additionalProviders: ProviderEntity[] = [
        {
          id: v4(),
          name: 'Provider For Company 1',
          companyId: companyId1,
          providerCompanyId: null,
          tributaryId: '*********',
          contactInformation: null,
        },
        {
          id: v4(),
          name: 'Another Provider For Company 1',
          companyId: companyId1,
          providerCompanyId: null,
          tributaryId: '*********',
          contactInformation: null,
        },
        {
          id: v4(),
          name: 'Provider For Company 2',
          companyId: companyId2,
          providerCompanyId: null,
          tributaryId: '*********',
          contactInformation: null,
        },
      ];

      await CoreProviderRepository.saveMany(additionalProviders);

      // Find all providers for company 1
      const providersForCompany1 = await CoreProviderRepository.findProvidersForCompany(companyId1);

      // Count existing seed providers for company 1
      const seedProvidersForCompany1 = seedProviders.filter((p) => p.companyId === companyId1);
      const expectedProviderCount = seedProvidersForCompany1.length + 2; // 2 new providers for company 1

      // Verify we get the right number of providers
      expect(providersForCompany1.length).toBe(expectedProviderCount);

      // Verify all returned providers belong to company 1
      providersForCompany1.forEach((provider) => {
        expect(provider.companyId).toBe(companyId1);
      });

      // Verify both our new providers are included
      const newProviderNames = additionalProviders
        .filter((p) => p.companyId === companyId1)
        .map((p) => p.name);

      const foundProviderNames = providersForCompany1.map((p) => p.name);
      expect(foundProviderNames).toEqual(expect.arrayContaining(newProviderNames));

      // Verify company 2's provider is not included
      const company2ProviderNames = additionalProviders
        .filter((p) => p.companyId === companyId2)
        .map((p) => p.name);

      company2ProviderNames.forEach((name) => {
        expect(foundProviderNames).not.toContain(name);
      });
    });

    it('should return an empty array when no providers exist for the company', async () => {
      // Generate a random ID that won't match any existing company
      const nonExistentCompanyId = v4();

      const providers = await CoreProviderRepository.findProvidersForCompany(nonExistentCompanyId);

      expect(providers).toEqual([]);
    });
  });

  describe('findProviderForCompanyByProviderCompanyId', () => {
    it('should find a provider for a company by providerCompanyId', async () => {
      // Find the provider using findProviderForCompanyByProviderCompanyId
      const found = await CoreProviderRepository.findProviderForCompanyByProviderCompanyId(
        seedCompanies[0].id,
        seedProviders[0].providerCompanyId as string,
      );

      expect(found).toEqual({
        id: seedProviders[0].id,
        name: seedProviders[0].name,
        companyId: seedProviders[0].companyId,
        providerCompanyId: seedProviders[0].providerCompanyId,
        tributaryId: seedProviders[0].tributaryId,
        contactInformation: null,
      });
    });

    it('should return undefined when no provider exists with the given companyId and providerCompanyId', async () => {
      // Generate non-existent IDs
      const nonExistentCompanyId = v4();
      const nonExistentProviderCompanyId = v4();

      // Try to find a provider that doesn't exist
      const found = await CoreProviderRepository.findProviderForCompanyByProviderCompanyId(
        nonExistentCompanyId,
        nonExistentProviderCompanyId,
      );

      expect(found).toBeUndefined();
    });

    it('should return the correct provider when multiple providers exist', async () => {
      // Create multiple providers with different providerCompanyIds
      const providers: ProviderEntity[] = [
        {
          id: v4(),
          name: 'Provider Alpha',
          companyId: seedCompanies[0].id,
          providerCompanyId: seedCompanies[1].id,
          tributaryId: '*********',
          contactInformation: null,
        },
        {
          id: v4(),
          name: 'Provider Beta',
          companyId: seedCompanies[0].id,
          providerCompanyId: seedCompanies[2].id,
          tributaryId: '*********',
          contactInformation: null,
        },
        {
          id: v4(),
          name: 'Provider Gamma',
          companyId: seedCompanies[2].id,
          providerCompanyId: seedCompanies[0].id,
          tributaryId: '*********',
          contactInformation: null,
        },
      ];

      await CoreProviderRepository.saveMany(providers);

      // Find the second provider by its specific combination of companyId and providerCompanyId
      const found = await CoreProviderRepository.findProviderForCompanyByProviderCompanyId(
        providers[1].companyId,
        providers[1].providerCompanyId as string,
      );

      expect(found).toEqual(providers[1]);
    });
  });
});
