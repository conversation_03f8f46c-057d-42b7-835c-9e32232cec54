import { v4 } from 'uuid';

import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import CompanyOperator from '#domain/aggregates/company/Company.Operator';
import { seedCompanies } from '#infrastructure/databases/core/seeds/02_Company';
import CoreCompanyRepository from '#infrastructure/implementations/company/repositories/CoreCompany.Repository';
import DatabaseTesting from '#infrastructure/testing/Database.Testing';

jest.unmock('uuid');
jest.unmock('pg');

jest.unmock('#infrastructure/testing/Database.Testing');
jest.unmock('#domain/aggregates/company/Company.Operator');
jest.unmock('#infrastructure/databases/core/seeds/02_Company');

jest.unmock('./CoreCompany.Repository');

describe('Company Repository Integration Test', () => {
  let company: CompanyEntity;

  beforeEach(async () => {
    await DatabaseTesting.setUpDatabase();
  });

  afterEach(async () => {
    await DatabaseTesting.tearDownDatabase();
  });

  describe('save', () => {
    it('should create a company', async () => {
      company = {
        id: v4(),
        name: 'Company 123',
        description: 'Test Company Description',
        country: 'US',
        tributaryId: '12345678',
        contactInformation: null,
      };

      const newCompany = await CoreCompanyRepository.save(company);

      expect(newCompany).toEqual(company);
    });

    it('should update a company', async () => {
      company = {
        id: v4(),
        name: 'Company 123',
        description: 'Test Company Description',
        country: 'US',
        tributaryId: '12345678',
        contactInformation: {
          billingAddress: '123 Main St',
          shippingAddress: '************',
          billingEmail: null,
          purchasesEmail: null,
          salesEmail: null,
          billingPhoneNumber: null,
          purchasesPhoneNumber: null,
          salesPhoneNumber: null,
          billingWhatsapp: null,
          purchasesWhatsapp: null,
          salesWhatsapp: null,
          mainAddress: null,
          representativeName: null,
          mainEmail: null,
          mainPhoneNumber: null,
          mainWhatsapp: null,
        },
      };

      await CoreCompanyRepository.save(company);

      company.name = 'Company 456';
      company.description = 'Updated Description';

      const updatedCompany = await CoreCompanyRepository.save(company);

      expect(updatedCompany).toEqual(company);
    });
  });

  describe('findOneByTributaryIdAndCountry', () => {
    it('should return a company', async () => {
      company = {
        id: v4(),
        name: 'Company 123',
        description: 'Test Company Description',
        country: 'US',
        tributaryId: '12345678',
        contactInformation: null,
      };

      await CoreCompanyRepository.save(company);

      const foundCompany = await CoreCompanyRepository.findOneByTributaryIdAndCountry('12345678', 'US');

      expect(foundCompany).toEqual(company);
    });

    it('should return undefined if company does not exist', async () => {
      const foundCompany = await CoreCompanyRepository.findOneByTributaryIdAndCountry('nonexistent', 'US');

      expect(foundCompany).toBeUndefined();
    });
  });

  describe('findManyByTributaryIdAndCountry', () => {
    it('should return a company', async () => {
      company = {
        id: v4(),
        name: 'Company 123',
        description: 'Test Company Description',
        country: 'US',
        tributaryId: '12345678',
        contactInformation: null,
      };

      await CoreCompanyRepository.save(company);

      const foundCompanies = await CoreCompanyRepository.findManyByTributaryIdAndCountry('12345678', 'US');

      expect(foundCompanies).toEqual([company]);
    });
  });

  describe('findOneById', () => {
    it('should return a company', async () => {
      const expected: CompanyEntity = {
        id: seedCompanies[0].id,
        name: seedCompanies[0].name,
        description: seedCompanies[0].description,
        country: seedCompanies[0].country,
        tributaryId: seedCompanies[0].tributaryId,
        contactInformation: null,
      };

      const found = await CoreCompanyRepository.findOneById(expected.id);

      expect(found).toEqual(expected);
    });

    it('should return undefined if company does not exist', async () => {
      const foundCompany = await CoreCompanyRepository.findOneById(v4());

      expect(foundCompany).toBeUndefined();
    });
  });

  describe('findManyByIds', () => {
    it('should return the catalog discounts by ids', async () => {
      const companies = [seedCompanies[0]];
      const found = await CoreCompanyRepository.findManyByIds(companies.map((companyToFind) => companyToFind.id));
      const expected = companies.map<CompanyEntity>((companyData) => CompanyOperator.build(companyData));

      expect(found).toEqual(expected);
    });
  });
});
