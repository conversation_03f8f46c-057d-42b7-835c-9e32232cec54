import { Knex } from 'knex';

import CompanyRepository from '#application/company/repositories/Company.Repository';
import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import CompanyOperator from '#domain/aggregates/company/Company.Operator';
import { ContactInformationEntity } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';
import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import CoreDatabase from '#infrastructure/databases/core/Core.Database';
import { dbCompany } from '#infrastructure/databases/core/types';

const { COMPANY } = TableNamesConfiguration;

const companySelect: Array<keyof dbCompany> = ['id', 'name', 'description', 'country', 'tributaryId'];

async function findOneByTributaryIdAndCountry(tributaryId: string, country: string): Promise<CompanyEntity | undefined> {
  const db = CoreDatabase.getClient();

  const company = await db(COMPANY).select(companySelect)
    .where('tributaryId', tributaryId)
    .andWhere('country', country)
    .first();

  if (!company) return undefined;

  return CompanyOperator.build(company);
}

async function findManyByTributaryIdAndCountry(tributaryId: string, country: string): Promise<CompanyEntity[]> {
  const db = CoreDatabase.getClient();

  const companies = await db(COMPANY).select(companySelect)
    .where('tributaryId', tributaryId)
    .andWhere('country', country);

  return companies.map<CompanyEntity>((company) => CompanyOperator.build(company));
}

async function findManyByIds(ids: string[]): Promise<CompanyEntity[]> {
  const db = CoreDatabase.getClient();

  const companies = await db(TableNamesConfiguration.COMPANY).select('*').whereIn('id', ids);

  return companies.map<CompanyEntity>((company) => CompanyOperator.build(company));
}

async function save(company: CompanyEntity): Promise<CompanyEntity> {
  const db = CoreDatabase.getClient();

  return db.transaction(async (trx: Knex.Transaction) => {
    const { contactInformation, ...baseCompanyInfo } = company;

    await trx(TableNamesConfiguration.CONTACT_INFORMATION).where({ entityId: company.id, entity: ContactInformationEntity.COMPANY }).del();

    const companyRecord = (await trx(COMPANY).insert({ ...baseCompanyInfo }).returning(companySelect).onConflict(['id'])
      .merge()
      .returning(companySelect))[0];

    if (contactInformation) {
      await trx(TableNamesConfiguration.CONTACT_INFORMATION).insert({
        ...contactInformation,
        entity: ContactInformationEntity.COMPANY,
        entityId: company.id,
      }).onConflict(['entity', 'entityId']).merge();
    }

    return CompanyOperator.build({ ...companyRecord, contactInformation });
  });
}

async function findOneById(id: string): Promise<CompanyEntity | undefined> {
  const db = CoreDatabase.getClient();

  const company = await db(COMPANY).select(companySelect).where({ id }).first();

  if (!company) return undefined;

  return CompanyOperator.build(company);
}

const CoreCompanyRepository: CompanyRepository = {
  save,
  findOneByTributaryIdAndCountry,
  findManyByIds,
  findOneById,
  findManyByTributaryIdAndCountry,
};

export default CoreCompanyRepository;
