import CatalogDiscountEntity from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';

function validateDiscounts(clientDiscounts: CatalogDiscountEntity[]): void {
  const permutations = new Set<string>();

  clientDiscounts.forEach((discount) => {
    discount.catalogIds.forEach((catalogId) => {
      discount.clientIds.forEach((clientId) => {
        const permutation = `${catalogId}-${clientId}-${discount.requiredQuantity}`;

        if (permutations.has(permutation)) {
          throw new Error(`Discount is duplicated for catalog ${catalogId}, client ${clientId} and quantity ${discount.requiredQuantity}`, { cause: 'BAD_REQUEST' });
        }

        permutations.add(permutation);
      });
    });
  });
}

export default {
  validateDiscounts,
};
