import Cause from '#composition/Cause.type';
import CatalogEntity from '#domain/aggregates/catalog/Catalog.Entity';
import CatalogDiscountEntity from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';
import CatalogDiscountOperator from '#domain/aggregates/catalogDiscount/CatalogDiscount.Operator';
import ClientEntity from '#domain/aggregates/client/Client.Entity';
import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import StoreDiscountOperator from '#domain/aggregates/storeDiscount/StoreDiscount.Operator';
import TaxEntity from '#domain/aggregates/tax/Tax.Entity';
import TaxOperator from '#domain/aggregates/tax/Tax.Operator';
import { DiscountEntity } from '#domain/common/aggregates/discount/Discount.Entity';
import DiscountOperator from '#domain/common/aggregates/discount/Discount.Operator';
import DomainUtil from '#domain/common/Domain.Util';

export interface ProcessableDiscount extends DiscountEntity {
  priority: number;
  hasRequiredValue: boolean;
}

interface DiscountEntityWithPriceAfterDiscount extends ProcessableDiscount {
  priceAfterDiscount: number;
}

export interface CatalogWithQuantity extends CatalogEntity {
  quantity: number;
}

export interface CatalogDiscountWithPriceAfterDiscount extends CatalogDiscountEntity {
  priceAfterDiscount: number;
}

export interface StoreDiscountWithPriceAfterDiscount extends StoreDiscountEntity {
  priceAfterDiscount: number;
}

export interface CatalogWithDiscount extends CatalogWithQuantity {
  priceAfterDiscount: number;
  appliedDiscount: {
    catalogDiscount: CatalogDiscountWithPriceAfterDiscount| null;
    storeDiscount: StoreDiscountWithPriceAfterDiscount | null;
  }
  applicableDiscounts: {
    catalogDiscounts:CatalogDiscountWithPriceAfterDiscount[];
    storeDiscounts: StoreDiscountWithPriceAfterDiscount[];
  }
}

export interface CatalogCalculatedTax extends TaxEntity {
  amount: number;
}

export interface CatalogWithTaxes extends CatalogEntity {
  priceAfterTaxes: number;
  taxes: CatalogCalculatedTax [];
}

interface ApplyCatalogDiscountsForClientParams {
  catalog: CatalogWithQuantity[];
  catalogDiscounts: CatalogDiscountEntity[];
  storeDiscounts: StoreDiscountEntity[];
  client: ClientEntity;
}

const { toOperablePrice, toDisplayPrice } = DomainUtil;

function cleanDiscount(discount: DiscountEntityWithPriceAfterDiscount | null): CatalogDiscountWithPriceAfterDiscount | StoreDiscountWithPriceAfterDiscount | null {
  if (!discount) return null;

  const { priority, hasRequiredValue, ...cleanedDiscount } = discount;

  return cleanedDiscount as CatalogDiscountWithPriceAfterDiscount | StoreDiscountWithPriceAfterDiscount;
}

function processDiscount({
  catalog, discounts, operator, client,
} :{ catalog: CatalogWithQuantity, discounts: ProcessableDiscount[], operator: typeof DiscountOperator, client: ClientEntity }): {
    appliedDiscount: DiscountEntityWithPriceAfterDiscount | null,
    applicableDiscounts: DiscountEntityWithPriceAfterDiscount[],
  } {
  const applicableDiscounts: DiscountEntityWithPriceAfterDiscount[] = [];
  let appliedDiscount: DiscountEntityWithPriceAfterDiscount | null = null;
  const applicableReference: {[key: string]: boolean} = {};

  const validDiscounts = discounts.filter((discount) => operator.isApplicable(discount, client.id));
  const discountsApplicableToClient = validDiscounts.filter((discount) => operator.hasClientAccess(discount, client.id));
  const discountApplicableToClientQuantities = new Set(discountsApplicableToClient.map((discount) => discount.priority));
  const discountsApplicableGlobally = validDiscounts.filter((discount) => !discountApplicableToClientQuantities.has(discount.priority));

  const processableDiscounts = [...discountsApplicableToClient, ...discountsApplicableGlobally];

  processableDiscounts.forEach((discount) => {
    const currentPriority = discount.priority;
    const scope = operator.hasGlobalAccess(discount) ? 'global' : 'client';

    if (applicableReference[`${catalog.id}_${currentPriority}_${scope}`]) {
      throw new Error('Client has more than one applicable discount for the same catalog item and required quantity/amount', { cause: Cause.CONFLICT });
    }

    const newPrice = operator.calculatePriceAfterDiscount(discount, catalog.price);

    applicableDiscounts.push({ ...discount, priceAfterDiscount: newPrice });
    applicableReference[`${catalog.id}_${currentPriority}_${scope}`] = true;

    const hasPriority = !appliedDiscount || (currentPriority > appliedDiscount.priority);

    if (discount.hasRequiredValue && hasPriority) {
      appliedDiscount = { ...discount, priceAfterDiscount: newPrice };
    }
  });

  return {
    appliedDiscount,
    applicableDiscounts,
  };
}

function calculateDiscountsForCatalogByClient({
  catalog, catalogDiscounts, storeDiscounts, client,
} :ApplyCatalogDiscountsForClientParams): CatalogWithDiscount[] {
  return catalog.map((catalogItem) => {
    let appliedCatalogDiscount: CatalogDiscountWithPriceAfterDiscount | null = null;
    let applicableCatalogDiscounts: CatalogDiscountWithPriceAfterDiscount[] = [];
    let appliedStoreDiscount: StoreDiscountWithPriceAfterDiscount | null = null;
    let applicableStoreDiscounts: StoreDiscountWithPriceAfterDiscount[] = [];

    const catalogDiscountWithCatalogs = catalogDiscounts
      .filter((discount) => CatalogDiscountOperator.hasCatalogAccess(discount, catalogItem.id))
      .map((discount) => ({
        ...discount,
        hasRequiredValue: CatalogDiscountOperator.hasRequiredQuantity(discount, catalogItem.quantity),
        priority: discount.requiredQuantity,
      }));

    const processedCatalogDiscounts = processDiscount({
      catalog: catalogItem,
      discounts: catalogDiscountWithCatalogs,
      operator: CatalogDiscountOperator as unknown as typeof DiscountOperator,
      client,
    });

    appliedCatalogDiscount = cleanDiscount(processedCatalogDiscounts.appliedDiscount) as CatalogDiscountWithPriceAfterDiscount | null;
    applicableCatalogDiscounts = processedCatalogDiscounts.applicableDiscounts.map(cleanDiscount) as CatalogDiscountWithPriceAfterDiscount[];

    if (applicableCatalogDiscounts.length === 0) {
      const processableStoreDiscounts = storeDiscounts.map((discount) => ({
        ...discount,
        hasRequiredValue: StoreDiscountOperator.hasRequiredAmount(discount, toDisplayPrice(toOperablePrice(catalogItem.price) * catalogItem.quantity)),
        priority: discount.requiredAmount,
      }));

      const processedStoreDiscounts = processDiscount({
        catalog: catalogItem,
        discounts: processableStoreDiscounts,
        operator: StoreDiscountOperator as unknown as typeof DiscountOperator,
        client,
      });

      appliedStoreDiscount = cleanDiscount(processedStoreDiscounts.appliedDiscount) as StoreDiscountWithPriceAfterDiscount;
      applicableStoreDiscounts = processedStoreDiscounts.applicableDiscounts.map(cleanDiscount) as StoreDiscountWithPriceAfterDiscount[];
    }

    const { priceAfterDiscount = catalogItem.price } = appliedCatalogDiscount || appliedStoreDiscount || {};

    return {
      ...catalogItem,
      priceAfterDiscount,
      appliedDiscount: {
        catalogDiscount: appliedCatalogDiscount,
        storeDiscount: appliedStoreDiscount,
      },
      applicableDiscounts: {
        catalogDiscounts: applicableCatalogDiscounts,
        storeDiscounts: applicableStoreDiscounts,
      },
    };
  });
}

function applyTaxesToCatalog(catalog: CatalogWithDiscount, taxes: TaxEntity[]): CatalogWithTaxes {
  const { taxIds } = catalog;
  const applicableTaxes = taxes.filter((tax) => taxIds.includes(tax.id));

  const taxWithAmount = applicableTaxes.map((tax) => {
    const taxAmount = TaxOperator.calculateTaxAmount(tax, catalog.priceAfterDiscount);
    return {
      ...tax,
      amount: taxAmount,
    };
  });

  const priceAfterTaxes = toDisplayPrice(toOperablePrice(catalog.priceAfterDiscount) + taxWithAmount.reduce((acc, tax) => acc + toOperablePrice(tax.amount), 0));

  return {
    ...catalog,
    priceAfterTaxes,
    taxes: taxWithAmount,
  };
}

function calculateCatalogDiscountAndTaxes({
  catalog, catalogDiscounts, storeDiscounts, client, taxes,
}: {
  catalog: CatalogWithQuantity[];
  catalogDiscounts: CatalogDiscountEntity[];
  storeDiscounts: StoreDiscountEntity[];
  client: ClientEntity;
  taxes: TaxEntity[];
}): (CatalogWithDiscount & CatalogWithTaxes)[] {
  const catalogWithDiscounts = calculateDiscountsForCatalogByClient({
    catalog,
    catalogDiscounts,
    storeDiscounts,
    client,
  });

  const catalogWithDiscountAndTaxes = catalogWithDiscounts
    .map((catalogItem) => {
      const catalogWithTaxes = applyTaxesToCatalog(catalogItem, taxes);

      return {
        ...catalogWithTaxes,
        ...catalogItem,
      };
    }) as (CatalogWithDiscount & CatalogWithTaxes)[];

  return catalogWithDiscountAndTaxes;
}

export default {
  calculateDiscountsForCatalogByClient,
  calculateCatalogDiscountAndTaxes,
};
