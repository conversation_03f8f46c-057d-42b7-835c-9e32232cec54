import CatalogDiscountEntity from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';

import CatalogDiscountDomainService from './CatalogDiscount.DomainService';

jest.unmock('./CatalogDiscount.DomainService');

describe('CatalogDiscountDomainService.validateClientDiscounts', () => {
  it('should pass when all permutations are unique', () => {
    const discounts: CatalogDiscountEntity[] = [
      {
        id: '1',
        name: 'Discount 1',
        discountValue: 10,
        discountType: DiscountType.AMOUNT,
        startDate: null,
        endDate: null,
        disabledAt: null,
        companyId: 'company1',
        createdAt: new Date(),
        updatedAt: new Date(),
        clientIds: ['client1', 'client2'],
        catalogIds: ['cat1', 'cat2'],
        requiredQuantity: 2,
      },
      {
        id: '2',
        name: 'Discount 2',
        discountValue: 20,
        discountType: DiscountType.AMOUNT,
        startDate: null,
        endDate: null,
        disabledAt: null,
        companyId: 'company1',
        createdAt: new Date(),
        updatedAt: new Date(),
        clientIds: ['client1', 'client2'],
        catalogIds: ['cat1'],
        requiredQuantity: 5,
      },
    ];

    expect(() => CatalogDiscountDomainService.validateDiscounts(discounts)).not.toThrow();
  });

  it('should throw an error when permutations are duplicated', () => {
    const discounts: CatalogDiscountEntity[] = [
      {
        id: '1',
        name: 'Discount 1',
        discountValue: 10,
        discountType: DiscountType.AMOUNT,
        startDate: null,
        endDate: null,
        disabledAt: null,
        companyId: 'company1',
        createdAt: new Date(),
        updatedAt: new Date(),
        clientIds: ['client1'],
        catalogIds: ['cat1'],
        requiredQuantity: 2,
      },
      {
        id: '1',
        name: 'Discount 2',
        discountValue: 20,
        discountType: DiscountType.AMOUNT,
        startDate: null,
        endDate: null,
        disabledAt: null,
        companyId: 'company1',
        createdAt: new Date(),
        updatedAt: new Date(),
        clientIds: ['client1'],
        catalogIds: ['cat1'],
        requiredQuantity: 2,
      },
    ];

    expect(() => CatalogDiscountDomainService.validateDiscounts(discounts)).toThrow(
      'Discount is duplicated for catalog cat1, client client1 and quantity 2',
    );
  });
});
