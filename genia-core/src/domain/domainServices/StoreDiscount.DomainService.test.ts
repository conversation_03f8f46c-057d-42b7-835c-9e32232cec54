import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';

import StoreDiscountDomainService from './StoreDiscount.DomainService';

jest.unmock('./StoreDiscount.DomainService');

describe('StoreDiscountDomainService.validateClientDiscounts', () => {
  it('should pass when all permutations are unique', () => {
    const discounts: StoreDiscountEntity[] = [
      {
        id: '1',
        name: 'Discount 1',
        discountValue: 10,
        discountType: DiscountType.AMOUNT,
        startDate: null,
        endDate: null,
        disabledAt: null,
        companyId: 'company1',
        createdAt: new Date(),
        updatedAt: new Date(),
        clientIds: ['client1', 'client2'],
        requiredAmount: 200,
      },
      {
        id: '2',
        name: 'Discount 2',
        discountValue: 20,
        discountType: DiscountType.AMOUNT,
        startDate: null,
        endDate: null,
        disabledAt: null,
        companyId: 'company1',
        createdAt: new Date(),
        updatedAt: new Date(),
        clientIds: ['client1', 'client2'],
        requiredAmount: 500,
      },
    ];

    expect(() => StoreDiscountDomainService.validateDiscounts(discounts)).not.toThrow();
  });

  it('should throw an error when permutations are duplicated', () => {
    const discounts: StoreDiscountEntity[] = [
      {
        id: '1',
        name: 'Discount 1',
        discountValue: 10,
        discountType: DiscountType.AMOUNT,
        startDate: null,
        endDate: null,
        disabledAt: null,
        companyId: 'company1',
        createdAt: new Date(),
        updatedAt: new Date(),
        clientIds: ['client1'],
        requiredAmount: 200,
      },
      {
        id: '1',
        name: 'Discount 2',
        discountValue: 20,
        discountType: DiscountType.AMOUNT,
        startDate: null,
        endDate: null,
        disabledAt: null,
        companyId: 'company1',
        createdAt: new Date(),
        updatedAt: new Date(),
        clientIds: ['client1'],
        requiredAmount: 200,
      },
    ];

    expect(() => StoreDiscountDomainService.validateDiscounts(discounts)).toThrow(
      'Discount is duplicated for client client1 and required amount 200',
    );
  });
});
