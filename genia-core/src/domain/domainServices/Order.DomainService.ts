import Cause from '#composition/Cause.type';
import DomainRegistry from '#composition/DomainRegistry';
import CatalogEntity from '#domain/aggregates/catalog/Catalog.Entity';
import CatalogMediaEntity from '#domain/aggregates/catalogMedia/CatalogMedia.Entity';
import InventoryEntity, { InventoryType } from '#domain/aggregates/inventory/Inventory.Entity';
import InventoryOperator from '#domain/aggregates/inventory/Inventory.Operator';
import PurchaseOrderEntity, { PurchaseOrderStatus } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Entity';
import PurchaseOrderOperator, { PurchaseOrderBuildParams } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Operator';
import { QuoteBuildParams } from '#domain/aggregates/quote/Quote.Operator';
import SaleOrderEntity, { SaleOrderStatus } from '#domain/aggregates/saleOrder/SaleOrder.Entity';
import SaleOrderOperator, { SaleOrderBuildParams } from '#domain/aggregates/saleOrder/SaleOrder.Operator';
import { AttributeType } from '#domain/common/Common.Type';
import DomainUtil from '#domain/common/Domain.Util';

export interface UpdateSaleOrderParams {
  order: SaleOrderEntity;
  status: SaleOrderStatus;
  modifierCompanyId: string;
  inventory?: InventoryEntity[];
  catalog?: CatalogEntity[];
}

export interface UpdatePurchaseOrdersParams {
  order: PurchaseOrderEntity;
  status: PurchaseOrderStatus;
  modifierCompanyId: string;
  inventory?: InventoryEntity[];
}

export interface CreateSaleOrderParams {
  orderBuildParams: SaleOrderBuildParams;
  catalog: CatalogEntity[];
  inventory?: InventoryEntity[];
}

export interface CreateQuoteParams {
  quoteBuildParams: QuoteBuildParams;
  catalog: CatalogEntity[];
  inventory?: InventoryEntity[];
}

export interface createPurchaseOrderParams {
  orderBuildParams: PurchaseOrderBuildParams;
  createInventoryFromOrderItems?: boolean;
  catalogReference: Map<string, CatalogEntity & { media?: CatalogMediaEntity[] }>;
}

interface updateInventoryParams {
  inventory: InventoryEntity[];
  catalog: CatalogEntity[];
  order: SaleOrderEntity;
  isNewOrder?: boolean;
}

const { toOperablePrice, toDisplayPrice } = DomainUtil;

function updateInventory(params: updateInventoryParams): InventoryEntity[] {
  const {
    inventory, catalog, order, isNewOrder,
  } = params;

  const inventoryMap = new Map(inventory.map((i) => [i.id, i]));
  const catalogMap = new Map(catalog.map((i) => [i.id, i]));

  return order.orderItems.flatMap((item) => {
    const inventoryRelations = catalogMap.get(item.catalogId)?.inventoryRelations;

    if (!inventoryRelations) return [];

    const modifiedInventory = inventoryRelations.map((relation) => {
      const inventoryItem = inventoryMap.get(relation.inventoryId);

      if (!inventoryItem) throw new Error(`Inventory item ${relation.inventoryId} for orderItem ${item.productId} not found`);

      if (isNewOrder) {
        return InventoryOperator.update(inventoryItem, {
          restrictedStock: inventoryItem.restrictedStock + (item.quantity * relation.quantity),
        });
      }

      if (order.status === SaleOrderStatus.CANCELLED) {
        return InventoryOperator.update(inventoryItem, {
          restrictedStock: inventoryItem.restrictedStock - (item.quantity * relation.quantity),
        });
      }

      return InventoryOperator.update(inventoryItem, {
        stock: inventoryItem.stock - (item.quantity * relation.quantity),
        restrictedStock: inventoryItem.restrictedStock - (item.quantity * relation.quantity),
      });
    });

    return modifiedInventory;
  });
}

function createSaleOrder(params: CreateSaleOrderParams): {saleOrder: SaleOrderEntity, inventory: InventoryEntity[]} {
  const { orderBuildParams, catalog, inventory } = params;

  const newOrder = SaleOrderOperator.build(orderBuildParams);
  const catalogMap = new Map(catalog.map((i) => [i.id, i]));
  let updatedInventory: InventoryEntity[] = [];

  const inventoryRequiredIds = newOrder.orderItems.flatMap((item) => {
    const inventoryRelations = catalogMap.get(item.catalogId)?.inventoryRelations;

    if (!inventoryRelations) return [];

    return inventoryRelations.map((relation) => relation.inventoryId);
  });

  if (inventoryRequiredIds.length) {
    if (!inventory) throw new Error('Inventory is required for creating an order with inventory relations', { cause: Cause.BAD_REQUEST });

    const inventoryMap = new Map(inventory.map((i) => [i.id, i]));

    inventoryRequiredIds.forEach((inventoryId) => {
      const inventoryItem = inventoryMap.get(inventoryId);

      if (!inventoryItem) throw new Error(`Inventory item ${inventoryId} required for order with inventoryRelations`, { cause: Cause.BAD_REQUEST });
    });

    updatedInventory = updateInventory({
      inventory,
      catalog,
      order: newOrder,
      isNewOrder: true,
    });
  }

  return {
    saleOrder: newOrder,
    inventory: updatedInventory,
  };
}

function updateSaleOrderStatus(params: UpdateSaleOrderParams): {saleOrder: SaleOrderEntity, inventory: InventoryEntity[]} {
  const {
    order, inventory, catalog, modifierCompanyId, status,
  } = params;

  const saleOrder = SaleOrderOperator.updateStatus(order, status, modifierCompanyId);
  let updatedInventory: InventoryEntity[] = [];

  if (status === SaleOrderStatus.COMPLETED || status === SaleOrderStatus.CANCELLED) {
    if (!inventory || !catalog) {
      throw new Error(`Inventory and catalog are required for updating the order state to ${status}`, { cause: Cause.BAD_REQUEST });
    }
    updatedInventory = updateInventory({
      inventory,
      catalog,
      order: saleOrder,
      isNewOrder: false,
    });
  }

  return {
    saleOrder,
    inventory: updatedInventory,
  };
}

function updatePurchaseOrderStatus(params: UpdatePurchaseOrdersParams): {purchaseOrder: PurchaseOrderEntity, inventory: InventoryEntity[]} {
  const {
    order, inventory, modifierCompanyId, status,
  } = params;

  const purchaseOrder = PurchaseOrderOperator.updateStatus(order, status, modifierCompanyId);
  let updatedInventory: InventoryEntity[] = [];

  const inventoryRequiredIds = order.orderItems.flatMap((item) => item.inventoryIds);

  if (status === PurchaseOrderStatus.COMPLETED) {
    if (!inventoryRequiredIds.length) {
      return { purchaseOrder, inventory: updatedInventory };
    }

    if (!inventory) {
      throw new Error(`Inventory is required for completing the order ${order.id}`);
    }

    const inventoryMap = new Map(inventory.map((i) => [i.id, i]));

    updatedInventory = order.orderItems.flatMap((item) => item.inventoryIds.map((inventoryId) => {
      const inventoryItem = inventoryMap.get(inventoryId);

      if (!inventoryItem) {
        throw new Error(
          `Inventory item with ID ${inventoryId} for order item with product ID ${item.productId} not found`,
          { cause: Cause.NOT_FOUND },
        );
      }

      return InventoryOperator.update(inventoryItem, {
        stock: inventoryItem.stock + item.quantity,
      });
    }));
  }

  return {
    purchaseOrder,
    inventory: updatedInventory,
  };
}

async function createPurchaseOrder(params: createPurchaseOrderParams): Promise<{purchaseOrder: PurchaseOrderEntity, inventory: InventoryEntity[]}> {
  const { orderBuildParams, createInventoryFromOrderItems, catalogReference } = params;

  const inventoryToCreate: InventoryEntity[] = [];
  const orderItems = await Promise.all(
    orderBuildParams.orderItems.map(async (item) => {
      if (item.inventoryIds?.length || !createInventoryFromOrderItems) return item;

      let attributes: AttributeType[] = [];
      const catalogItem = catalogReference.get(item.referenceId);
      if (catalogItem) {
        attributes = catalogItem.attributes;
      }

      const newId = await DomainRegistry.IdentificationService.generateId();
      const inventory = InventoryOperator.build({
        id: newId,
        companyId: orderBuildParams.companyId,
        hasStockValidation: false,
        measurementUnit: 'unit',
        name: item.name,
        sku: `${item.productId}`,
        stock: 0,
        providers: [{
          providerId: orderBuildParams.providerId,
          providerProductSku: item.productId,
          currentPurchasePrice: item.unitPrice,
          currentDiscount: toDisplayPrice((1 - (toOperablePrice(item.unitPriceAfterDiscount || 1) / toOperablePrice((item.unitPrice || 1)))) * toOperablePrice(100)),
        }],
        type: InventoryType.COMMODITY,
        attributes,
      });

      inventoryToCreate.push(inventory);

      return {
        ...item,
        inventoryIds: [inventory.id],
      };
    }),
  );

  const newOrder = PurchaseOrderOperator.build({
    ...orderBuildParams,
    orderItems,
  });

  return {
    purchaseOrder: newOrder,
    inventory: inventoryToCreate,
  };
}

export default {
  createSaleOrder,
  updateSaleOrderStatus,
  updatePurchaseOrderStatus,
  createPurchaseOrder,
};
