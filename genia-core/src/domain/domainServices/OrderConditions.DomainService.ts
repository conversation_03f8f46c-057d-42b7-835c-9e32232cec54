import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import DomainUtil from '#domain/common/Domain.Util';

interface orderTax {
  id: string,
  value: number,
  type: TaxType;
  name: string,
  amount: number,
}

export interface OrderItemConditions {
  catalogId: string,
  quantity: number,
  unitPrice: number,
  unitPriceAfterDiscount: number,
  unitPriceAfterDiscountAndTaxes: number,
  taxes: orderTax[],
}

export interface OrderConditions {
  total: number,
  shippingPrice: number,
  subtotal: number,
  subtotalBeforeDiscount: number,
  totalDiscount: number,
  totalTaxes: number,
  taxes: orderTax[],
  hasTaxOnShipping: boolean,
  orderItems: (OrderItemConditions & {
    subtotal: number,
    total: number,
  })[],
}

export interface CalculateConditionsParams {
  orderItems: OrderItemConditions[],
  shippingPrice: number,
  addTaxToShipping?: boolean,
}

const { toOperablePrice, toStorablePrice } = DomainUtil;

function calculateConditions(params: CalculateConditionsParams): OrderConditions {
  const operableTaxesMap: Record<string, orderTax> = {};
  const { orderItems, shippingPrice, addTaxToShipping = true } = params;

  orderItems.forEach((item) => {
    item.taxes.forEach((tax) => {
      const operableTax = operableTaxesMap[tax.id] || {
        ...tax,
        amount: 0,
      };
      operableTax.amount += toOperablePrice(tax.amount) * item.quantity;
      operableTaxesMap[tax.id] = operableTax;
    });
  });

  const taxes = Object.values(operableTaxesMap).map((tax) => ({
    ...tax,
    amount: tax.amount + (addTaxToShipping ? (toOperablePrice(shippingPrice) * (toOperablePrice(tax.value) / toOperablePrice(100))) : 0),
  }));

  const operableItems = orderItems.map((item) => ({
    ...item,
    subtotal: toOperablePrice(item.unitPriceAfterDiscount) * item.quantity,
    total: toOperablePrice(item.unitPriceAfterDiscountAndTaxes) * item.quantity,
    unitPrice: toOperablePrice(item.unitPrice),
    unitPriceAfterDiscount: toOperablePrice(item.unitPriceAfterDiscount),
    unitPriceAfterDiscountAndTaxes: toOperablePrice(item.unitPriceAfterDiscountAndTaxes),
  }));

  const subtotalBeforeDiscount = operableItems.reduce((acc, item) => acc + (item.subtotal + (item.unitPrice - item.unitPriceAfterDiscount) * item.quantity), 0);
  const subtotal = operableItems.reduce((acc, item) => acc + item.subtotal, 0);
  const totalDiscounts = operableItems.reduce((acc, item) => acc + (item.unitPrice - item.unitPriceAfterDiscount) * item.quantity, 0);
  const totalTaxes = taxes.reduce((acc, item) => acc + item.amount, 0);

  const total = toOperablePrice(shippingPrice) + subtotal + totalTaxes;

  return {
    subtotalBeforeDiscount: toStorablePrice(subtotalBeforeDiscount),
    totalDiscount: toStorablePrice(totalDiscounts),
    subtotal: toStorablePrice(subtotal),
    totalTaxes: toStorablePrice(totalTaxes),
    shippingPrice,
    total: toStorablePrice(total),
    taxes: taxes.map((tax) => ({ ...tax, amount: toStorablePrice(tax.amount) })),
    hasTaxOnShipping: addTaxToShipping,
    orderItems: operableItems.map((item) => ({
      catalogId: item.catalogId,
      quantity: item.quantity,
      subtotal: toStorablePrice(item.subtotal),
      total: toStorablePrice(item.total),
      unitPrice: toStorablePrice(item.unitPrice),
      unitPriceAfterDiscount: toStorablePrice(item.unitPriceAfterDiscount),
      unitPriceAfterDiscountAndTaxes: toStorablePrice(item.unitPriceAfterDiscountAndTaxes),
      taxes: item.taxes.map((tax) => ({
        ...tax,
        amount: toStorablePrice(toOperablePrice(tax.amount) * item.quantity),
      })),
    })),
  };
}

export default {
  calculateConditions,
};
