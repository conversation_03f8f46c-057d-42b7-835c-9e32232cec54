import Cause from '#composition/Cause.type';
import CatalogEntity, { CatalogType } from '#domain/aggregates/catalog/Catalog.Entity';
import InventoryEntity, { InventoryType } from '#domain/aggregates/inventory/Inventory.Entity';
import InventoryOperator from '#domain/aggregates/inventory/Inventory.Operator';
import PurchaseOrderEntity, { PurchaseOrderStatus } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Entity';
import PurchaseOrderOperator from '#domain/aggregates/purchaseOrder/PurchaseOrder.Operator';
import SaleOrderEntity, { SaleOrderStatus } from '#domain/aggregates/saleOrder/SaleOrder.Entity';
import SaleOrderOperator, { SaleOrderBuildParams } from '#domain/aggregates/saleOrder/SaleOrder.Operator';
import { AttributeType } from '#domain/common/Common.Type';

import OrderDomainService from './Order.DomainService';

jest.unmock('#domain/domainServices/Order.DomainService');
jest.unmock('#domain/common/Domain.Util');

// Mock Registry.IdentificationService
jest.mock('#composition/DomainRegistry', () => ({
  IdentificationService: {
    generateId: jest.fn().mockImplementation(() => Promise.resolve('mock-id')),
  },
}));

describe('OrderDomainService', () => {
  // Mock data
  const mockCompanyId = 'company123';
  const modifierCompanyId = 'modifier456';
  const mockDate = new Date();

  // Sale Order Mock Data
  const mockSaleOrder: SaleOrderEntity = {
    id: 'sale123',
    companyId: mockCompanyId,
    assignedUserId: null,
    clientId: 'customer123',
    readId: 'SO-123',
    status: SaleOrderStatus.PENDING,
    deliveryDate: null,
    shippedAt: null,
    reviewStartedAt: null,
    subtotalBeforeDiscount: 650,
    subtotal: 650,
    totalDiscount: 0,
    totalTaxes: 0,
    total: 650,
    shippingPrice: 0,
    shippingAddress: null,
    notes: null,
    taxes: [],
    createdAt: mockDate,
    updatedAt: mockDate,
    relatedPurchaseOrderId: null,
    orderItems: [
      {
        productId: 'product1',
        catalogId: 'catalog1',
        name: 'Product 1',
        quantity: 2,
        unitPrice: 100,
        unitPriceAfterDiscount: 100,
        unitPriceAfterDiscountAndTaxes: 100,
        subtotal: 200,
        total: 200,
        discount: null,
        taxes: [],
        createdAt: mockDate,
        updatedAt: mockDate,
      },
      {
        productId: 'product2',
        catalogId: 'catalog2',
        name: 'Product 2',
        quantity: 3,
        unitPrice: 150,
        unitPriceAfterDiscount: 150,
        unitPriceAfterDiscountAndTaxes: 150,
        subtotal: 450,
        total: 450,
        discount: null,
        taxes: [],
        createdAt: mockDate,
        updatedAt: mockDate,
      },
    ],
  };

  // Purchase Order Mock Data
  const mockPurchaseOrder: PurchaseOrderEntity = {
    id: 'purchase123',
    companyId: mockCompanyId,
    assignedUserId: null,
    readId: 'PO-123',
    status: PurchaseOrderStatus.PENDING,
    deliveryDate: null,
    shippedAt: null,
    reviewStartedAt: null,
    subtotalBeforeDiscount: 1600,
    subtotal: 1600,
    totalDiscount: 0,
    totalTaxes: 0,
    total: 1600,
    shippingPrice: 0,
    shippingAddress: null,
    notes: null,
    taxes: [],
    createdAt: mockDate,
    updatedAt: mockDate,
    orderItems: [
      {
        id: 'poi-1',
        productId: 'product1',
        inventoryIds: ['inventory1'],
        referenceId: 'ref-001',
        name: 'Product 1',
        quantity: 5,
        unitPrice: 80,
        unitPriceAfterDiscount: 80,
        unitPriceAfterDiscountAndTaxes: 80,
        subtotal: 400,
        total: 400,
        discount: null,
        taxes: [],
        createdAt: mockDate,
        updatedAt: mockDate,
      },
      {
        id: 'poi-2',
        productId: 'product2',
        inventoryIds: ['inventory2'],
        referenceId: 'ref-002',
        name: 'Product 2',
        quantity: 10,
        unitPrice: 120,
        unitPriceAfterDiscount: 120,
        unitPriceAfterDiscountAndTaxes: 120,
        subtotal: 1200,
        total: 1200,
        discount: null,
        taxes: [],
        createdAt: mockDate,
        updatedAt: mockDate,
      },
    ],
    providerId: 'provider456',
    relatedSaleOrderId: null,
  };

  // Inventory Mock Data
  const mockInventory: InventoryEntity[] = [
    {
      id: 'inventory1',
      sku: 'SKU001',
      name: 'Inventory Item 1',
      description: 'Description for inventory item 1',
      stock: 50,
      attributes: [],
      companyId: mockCompanyId,
      standardIdentifier: null,
      type: InventoryType.PRODUCT_INPUT,
      measurementUnit: 'unit',
      hasStockValidation: true,
      restrictedStock: 0,
      providers: [
        {
          providerId: 'provider123',
          currentPurchasePrice: 75,
          currentDiscount: null,
          providerProductSku: 'PROV-001',
        },
      ],
      createdAt: mockDate,
      updatedAt: mockDate,
      disabledAt: null,
    },
    {
      id: 'inventory2',
      sku: 'SKU002',
      name: 'Inventory Item 2',
      description: 'Description for inventory item 2',
      stock: 100,
      attributes: [],
      companyId: mockCompanyId,
      standardIdentifier: null,
      type: InventoryType.PRODUCT_INPUT,
      measurementUnit: 'unit',
      hasStockValidation: true,
      restrictedStock: 0,
      providers: [
        {
          providerId: 'provider123',
          currentPurchasePrice: 110,
          currentDiscount: null,
          providerProductSku: 'PROV-002',
        },
      ],
      createdAt: mockDate,
      updatedAt: mockDate,
      disabledAt: null,
    },
  ];

  // Catalog Mock Data
  const mockCatalog: CatalogEntity[] = [
    {
      id: 'catalog1',
      readId: 'CAT-001',
      name: 'Product 1',
      description: 'Product 1 description',
      price: 100,
      requiresStock: true,
      attributes: [],
      type: CatalogType.PRODUCT,
      discountIds: [],
      taxIds: [],
      mediaIds: [],
      inventoryRelations: [
        {
          inventoryId: 'inventory1',
          quantity: 1,
        },
      ],
      companyId: mockCompanyId,
      disabledAt: null,
      createdAt: mockDate,
      updatedAt: mockDate,
    },
    {
      id: 'catalog2',
      readId: 'CAT-002',
      name: 'Product 2',
      description: 'Product 2 description',
      price: 150,
      requiresStock: true,
      attributes: [],
      type: CatalogType.PRODUCT,
      discountIds: [],
      taxIds: [],
      mediaIds: [],
      inventoryRelations: [
        {
          inventoryId: 'inventory2',
          quantity: 2,
        },
      ],
      companyId: mockCompanyId,
      disabledAt: null,
      createdAt: mockDate,
      updatedAt: mockDate,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock SaleOrderOperator.updateStatus
    (SaleOrderOperator.updateStatus as jest.Mock).mockImplementation((order, status) => ({
      ...order,
      status,
    }));

    // Mock SaleOrderOperator.build
    (SaleOrderOperator.build as jest.Mock).mockImplementation((params) => ({
      id: 'new-sale-123',
      companyId: params.companyId || mockCompanyId,
      assignedUserId: null,
      clientId: params.clientId || 'customer123',
      readId: 'SO-NEW-123',
      status: SaleOrderStatus.PENDING,
      deliveryDate: null,
      shippedAt: null,
      reviewStartedAt: null,
      subtotalBeforeDiscount: 650,
      subtotal: 650,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 650,
      shippingPrice: 0,
      shippingAddress: null,
      notes: null,
      taxes: [],
      createdAt: mockDate,
      updatedAt: mockDate,
      relatedPurchaseOrderId: null,
      orderItems: params.orderItems || mockSaleOrder.orderItems,
    }));

    // Mock PurchaseOrderOperator.updateStatus
    (PurchaseOrderOperator.updateStatus as jest.Mock).mockImplementation((order, status) => ({
      ...order,
      status,
    }));

    // Mock InventoryOperator.update
    (InventoryOperator.update as jest.Mock).mockImplementation((inventory, update) => ({
      ...inventory,
      ...update,
    }));

    // Mock PurchaseOrderOperator.build
    (PurchaseOrderOperator.build as jest.Mock).mockImplementation((params) => ({
      id: 'new-purchase-123',
      companyId: params.companyId || mockCompanyId,
      assignedUserId: null,
      readId: 'PO-NEW-123',
      status: PurchaseOrderStatus.PENDING,
      deliveryDate: null,
      shippedAt: null,
      reviewStartedAt: null,
      subtotalBeforeDiscount: 1600,
      subtotal: 1600,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 1600,
      shippingPrice: 0,
      shippingAddress: null,
      notes: null,
      taxes: [],
      createdAt: mockDate,
      updatedAt: mockDate,
      providerId: params.providerId || 'provider456',
      relatedSaleOrderId: null,
      orderItems: params.orderItems || mockPurchaseOrder.orderItems,
    }));

    // Mock InventoryOperator.build
    (InventoryOperator.build as jest.Mock).mockImplementation((params) => ({
      id: params.id || 'new-inventory-id',
      sku: params.sku || 'SKU-NEW',
      name: params.name || 'New Inventory Item',
      description: '',
      stock: params.stock !== undefined ? params.stock : 0,
      attributes: params.attributes || [],
      companyId: params.companyId || mockCompanyId,
      standardIdentifier: null,
      type: params.type || InventoryType.PRODUCT_INPUT,
      measurementUnit: params.measurementUnit || 'unit',
      hasStockValidation: params.hasStockValidation !== undefined ? params.hasStockValidation : true,
      restrictedStock: 0,
      providers: params.providers || [],
      createdAt: mockDate,
      updatedAt: mockDate,
      disabledAt: null,
    }));
  });

  describe('createSaleOrder', () => {
    it('should create a new sale order with no inventory changes when there are no inventory relations', () => {
      // Create catalog without inventory relations
      const catalogWithoutInventoryRelations: CatalogEntity[] = [
        {
          ...mockCatalog[0],
          inventoryRelations: [],
          requiresStock: false, // Add this to prevent inventory requirement
        },
        {
          ...mockCatalog[1],
          inventoryRelations: [],
          requiresStock: false, // Add this to prevent inventory requirement
        },
      ];

      const orderBuildParams: SaleOrderBuildParams = {
        id: 'new-sale-123',
        readId: 'SO-NEW-123',
        subtotalBeforeDiscount: 650,
        subtotal: 650,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 650,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        clientId: 'customer123',
        status: SaleOrderStatus.PENDING,
        orderItems: [
          {
            productId: 'product1',
            catalogId: 'catalog1',
            name: 'Product 1',
            quantity: 2,
            unitPrice: 100,
            subtotal: 200,
            total: 200,
            taxes: [],
            unitPriceAfterDiscount: 100,
            unitPriceAfterDiscountAndTaxes: 100,
            discount: null,
          },
        ],
      };

      const result = OrderDomainService.createSaleOrder({
        orderBuildParams,
        catalog: catalogWithoutInventoryRelations,
      });

      expect(result.saleOrder).toBeDefined();
      expect(result.saleOrder.id).toBe('new-sale-123');
      expect(result.inventory).toEqual([]);
    });

    it('should create a new sale order and update restrictedStock when there are inventory relations', () => {
      const orderBuildParams: SaleOrderBuildParams = {
        id: 'new-sale-123',
        readId: 'SO-NEW-123',
        subtotalBeforeDiscount: 650,
        subtotal: 650,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 650,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        clientId: 'customer123',
        status: SaleOrderStatus.PENDING,
        orderItems: [
          {
            productId: 'product1',
            catalogId: 'catalog1',
            name: 'Product 1',
            quantity: 2,
            unitPrice: 100,
            subtotal: 200,
            total: 200,
            taxes: [],
            unitPriceAfterDiscount: 100,
            unitPriceAfterDiscountAndTaxes: 100,
            discount: null,
          },
          {
            productId: 'product2',
            catalogId: 'catalog2',
            name: 'Product 2',
            quantity: 3,
            unitPrice: 150,
            subtotal: 450,
            total: 450,
            taxes: [],
            unitPriceAfterDiscount: 150,
            unitPriceAfterDiscountAndTaxes: 150,
            discount: null,
          },
        ],
      };

      const result = OrderDomainService.createSaleOrder({
        orderBuildParams,
        catalog: mockCatalog,
        inventory: mockInventory,
      });

      expect(result.saleOrder).toBeDefined();
      expect(result.saleOrder.id).toBe('new-sale-123');
      expect(result.inventory.length).toBe(2);

      // Check restricted stock increases
      // First item: 2 quantity * 1 inventory relation quantity
      expect(result.inventory[0].restrictedStock).toBe(2);
      // Second item: 3 quantity * 2 inventory relation quantity
      expect(result.inventory[1].restrictedStock).toBe(6);
    });

    it('should throw error when inventory is required but not provided', () => {
      const orderBuildParams: SaleOrderBuildParams = {
        id: 'new-sale-123',
        readId: 'SO-NEW-123',
        subtotalBeforeDiscount: 650,
        subtotal: 650,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 650,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        clientId: 'customer123',
        status: SaleOrderStatus.PENDING,
        orderItems: [
          {
            productId: 'product1',
            catalogId: 'catalog1',
            name: 'Product 1',
            quantity: 2,
            unitPrice: 100,
            subtotal: 200,
            total: 200,
            taxes: [],
            unitPriceAfterDiscount: 100,
            unitPriceAfterDiscountAndTaxes: 100,
            discount: null,
          },
        ],
      };

      expect(() => {
        OrderDomainService.createSaleOrder({
          orderBuildParams,
          catalog: mockCatalog,
        });
      }).toThrow();

      // Verify error includes the cause
      try {
        OrderDomainService.createSaleOrder({
          orderBuildParams,
          catalog: mockCatalog,
        });
      } catch (error) {
        expect((error as Error).cause).toBe(Cause.BAD_REQUEST);
      }
    });

    it('should throw error when required inventory item is not found', () => {
      const orderBuildParams: SaleOrderBuildParams = {
        id: 'new-sale-123',
        readId: 'SO-NEW-123',
        subtotalBeforeDiscount: 650,
        subtotal: 650,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 650,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        clientId: 'customer123',
        status: SaleOrderStatus.PENDING,
        orderItems: [
          {
            productId: 'product1',
            catalogId: 'catalog1',
            name: 'Product 1',
            quantity: 2,
            unitPrice: 100,
            subtotal: 200,
            total: 200,
            taxes: [],
            unitPriceAfterDiscount: 100,
            unitPriceAfterDiscountAndTaxes: 100,
            discount: null,
          },
        ],
      };

      // Missing the inventory2 item that would be needed for catalog2
      const incompleteInventory = [mockInventory[0]];

      expect(() => {
        OrderDomainService.createSaleOrder({
          orderBuildParams,
          catalog: mockCatalog,
          inventory: incompleteInventory,
        });
      }).not.toThrow();

      // Test with an order that specifically needs the missing inventory
      const orderRequiringMissingInventory: SaleOrderBuildParams = {
        id: 'new-sale-123',
        readId: 'SO-NEW-123',
        subtotalBeforeDiscount: 650,
        subtotal: 650,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 650,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        clientId: 'customer123',
        status: SaleOrderStatus.PENDING,
        orderItems: [
          {
            productId: 'product2',
            catalogId: 'catalog2',
            name: 'Product 2',
            quantity: 3,
            unitPrice: 150,
            subtotal: 450,
            total: 450,
            taxes: [],
            unitPriceAfterDiscount: 150,
            unitPriceAfterDiscountAndTaxes: 150,
            discount: null,
          },
        ],
      };

      expect(() => {
        OrderDomainService.createSaleOrder({
          orderBuildParams: orderRequiringMissingInventory,
          catalog: mockCatalog,
          inventory: incompleteInventory,
        });
      }).toThrow();

      // Verify error includes the cause
      try {
        OrderDomainService.createSaleOrder({
          orderBuildParams: orderRequiringMissingInventory,
          catalog: mockCatalog,
          inventory: incompleteInventory,
        });
      } catch (error) {
        expect((error as Error).cause).toBe(Cause.BAD_REQUEST);
      }
    });
  });

  describe('updateSaleOrderStatus', () => {
    it('should update sale order status without affecting inventory when status is not COMPLETED', () => {
      const result = OrderDomainService.updateSaleOrderStatus({
        order: mockSaleOrder,
        status: SaleOrderStatus.IN_TRANSIT,
        modifierCompanyId,
        inventory: mockInventory,
        catalog: mockCatalog,
      });

      expect(result.saleOrder.status).toBe(SaleOrderStatus.IN_TRANSIT);
      expect(result.inventory).toEqual([]);
    });

    it('should update sale order status and decrease inventory stock when status is COMPLETED', () => {
      const result = OrderDomainService.updateSaleOrderStatus({
        order: mockSaleOrder,
        status: SaleOrderStatus.COMPLETED,
        modifierCompanyId,
        inventory: mockInventory,
        catalog: mockCatalog,
      });

      expect(result.saleOrder.status).toBe(SaleOrderStatus.COMPLETED);
      expect(result.inventory.length).toBe(2);

      // Check inventory updates (2 units from item1 * 1 unit per relation)
      expect(result.inventory[0].stock).toBe(48);
      // Check inventory updates (3 units from item2 * 2 units per relation)
      expect(result.inventory[1].stock).toBe(94);
    });

    it('should update sale order status and decrease restrictedStock only when status is CANCELLED', () => {
      const result = OrderDomainService.updateSaleOrderStatus({
        order: mockSaleOrder,
        status: SaleOrderStatus.CANCELLED,
        modifierCompanyId,
        inventory: mockInventory,
        catalog: mockCatalog,
      });

      expect(result.saleOrder.status).toBe(SaleOrderStatus.CANCELLED);
      expect(result.inventory.length).toBe(2);

      // Check inventory updates - for CANCELLED, only restrictedStock should decrease
      // First item: 2 units from item1 * 1 unit per relation
      expect(result.inventory[0].stock).toBe(50); // Unchanged
      expect(result.inventory[0].restrictedStock).toBe(-2); // Decreased by 2

      // Second item: 3 units from item2 * 2 units per relation
      expect(result.inventory[1].stock).toBe(100); // Unchanged
      expect(result.inventory[1].restrictedStock).toBe(-6); // Decreased by 6
    });

    it('should throw error when cancelling order without inventory and catalog', () => {
      expect(() => {
        OrderDomainService.updateSaleOrderStatus({
          order: { ...mockSaleOrder },
          status: SaleOrderStatus.CANCELLED,
          modifierCompanyId,
        });
      }).toThrow();

      // Verify the error includes the cause
      try {
        OrderDomainService.updateSaleOrderStatus({
          order: { ...mockSaleOrder },
          status: SaleOrderStatus.CANCELLED,
          modifierCompanyId,
        });
      } catch (error) {
        expect((error as Error).cause).toBe(Cause.BAD_REQUEST);
      }
    });

    it('should throw error when completing order without inventory and catalog', () => {
      expect(() => {
        OrderDomainService.updateSaleOrderStatus({
          order: { ...mockSaleOrder },
          status: SaleOrderStatus.COMPLETED,
          modifierCompanyId,
        });
      }).toThrow();
    });

    it('should throw error when inventory item is not found', () => {
      const incompleteInventory = [mockInventory[0]]; // Missing inventory2

      expect(() => {
        OrderDomainService.updateSaleOrderStatus({
          order: { ...mockSaleOrder },
          status: SaleOrderStatus.COMPLETED,
          modifierCompanyId,
          inventory: incompleteInventory,
          catalog: mockCatalog,
        });
      }).toThrow();
    });

    it('should handle sale order with catalog items that have no inventory relations', () => {
      // Create a catalog without inventory relations
      const catalogWithoutInventoryRelations: CatalogEntity = {
        ...mockCatalog[0],
        id: 'catalog3',
        inventoryRelations: [],
      };

      // Create a sale order with an item that uses this catalog
      const saleOrderWithNoInventoryRelations: SaleOrderEntity = {
        ...mockSaleOrder,
        orderItems: [
          {
            ...mockSaleOrder.orderItems[0],
            catalogId: 'catalog3',
          },
        ],
      };

      const result = OrderDomainService.updateSaleOrderStatus({
        order: saleOrderWithNoInventoryRelations,
        status: SaleOrderStatus.COMPLETED,
        modifierCompanyId,
        inventory: mockInventory,
        catalog: [catalogWithoutInventoryRelations],
      });

      expect(result.saleOrder.status).toBe(SaleOrderStatus.COMPLETED);
      // Should return empty array since there are no inventory relations to update
      expect(result.inventory).toEqual([]);
    });

    it('should handle catalog item that does not exist in the catalog map', () => {
      // Create a sale order with an item that references a non-existent catalog ID
      const saleOrderWithNonExistentCatalog: SaleOrderEntity = {
        ...mockSaleOrder,
        orderItems: [
          {
            ...mockSaleOrder.orderItems[0],
            catalogId: 'non-existent-catalog',
          },
        ],
      };

      const result = OrderDomainService.updateSaleOrderStatus({
        order: saleOrderWithNonExistentCatalog,
        status: SaleOrderStatus.COMPLETED,
        modifierCompanyId,
        inventory: mockInventory,
        catalog: mockCatalog,
      });

      expect(result.saleOrder.status).toBe(SaleOrderStatus.COMPLETED);
      // Should return empty array since the catalog ID doesn't exist
      expect(result.inventory).toEqual([]);
    });

    it('should update sale order status and decrease inventory stock and restrictedStock when status is COMPLETED', () => {
      // First set some restricted stock
      const inventoryWithRestrictedStock = mockInventory.map((item) => ({
        ...item,
        restrictedStock: 10,
      }));

      const result = OrderDomainService.updateSaleOrderStatus({
        order: mockSaleOrder,
        status: SaleOrderStatus.COMPLETED,
        modifierCompanyId,
        inventory: inventoryWithRestrictedStock,
        catalog: mockCatalog,
      });

      expect(result.saleOrder.status).toBe(SaleOrderStatus.COMPLETED);
      expect(result.inventory.length).toBe(2);

      // Check inventory updates (2 units from item1 * 1 unit per relation)
      expect(result.inventory[0].stock).toBe(48);
      expect(result.inventory[0].restrictedStock).toBe(8);

      // Check inventory updates (3 units from item2 * 2 units per relation)
      expect(result.inventory[1].stock).toBe(94);
      expect(result.inventory[1].restrictedStock).toBe(4);
    });

    it('should throw error with BAD_REQUEST cause when completing order without inventory and catalog', () => {
      expect(() => {
        OrderDomainService.updateSaleOrderStatus({
          order: { ...mockSaleOrder },
          status: SaleOrderStatus.COMPLETED,
          modifierCompanyId,
        });
      }).toThrow();

      // Verify the error includes the cause
      try {
        OrderDomainService.updateSaleOrderStatus({
          order: { ...mockSaleOrder },
          status: SaleOrderStatus.COMPLETED,
          modifierCompanyId,
        });
      } catch (error) {
        expect((error as Error).cause).toBe(Cause.BAD_REQUEST);
      }
    });
  });

  describe('updatePurchaseOrderStatus', () => {
    it('should update purchase order status without affecting inventory when status is not COMPLETED', () => {
      const result = OrderDomainService.updatePurchaseOrderStatus({
        order: mockPurchaseOrder,
        status: PurchaseOrderStatus.PROCESSING,
        modifierCompanyId,
        inventory: mockInventory,
      });

      expect(result.purchaseOrder.status).toBe(PurchaseOrderStatus.PROCESSING);
      expect(result.inventory).toEqual([]);
    });

    it('should update purchase order status and increase inventory stock when status is COMPLETED', () => {
      const result = OrderDomainService.updatePurchaseOrderStatus({
        order: mockPurchaseOrder,
        status: PurchaseOrderStatus.COMPLETED,
        modifierCompanyId,
        inventory: mockInventory,
      });

      expect(result.purchaseOrder.status).toBe(PurchaseOrderStatus.COMPLETED);
      expect(result.inventory.length).toBe(2);

      // Check inventory updates (increased by order quantities)
      expect(result.inventory[0].stock).toBe(55); // 50 + 5
      expect(result.inventory[1].stock).toBe(110); // 100 + 10
    });

    it('should throw error when completing order with required inventory but no inventory provided', () => {
      expect(() => {
        OrderDomainService.updatePurchaseOrderStatus({
          order: { ...mockPurchaseOrder },
          status: PurchaseOrderStatus.COMPLETED,
          modifierCompanyId,
        });
      }).toThrow();
    });

    it('should throw error when inventory item is not found', () => {
      const incompleteInventory = [mockInventory[0]]; // Missing inventory2

      expect(() => {
        OrderDomainService.updatePurchaseOrderStatus({
          order: { ...mockPurchaseOrder },
          status: PurchaseOrderStatus.COMPLETED,
          modifierCompanyId,
          inventory: incompleteInventory,
        });
      }).toThrow();
    });

    it('should handle purchase order with no inventory IDs', () => {
      const orderWithoutInventory = {
        ...mockPurchaseOrder,
        orderItems: [
          {
            ...mockPurchaseOrder.orderItems[0],
            inventoryIds: [],
            referenceId: 'ref-no-inventory',
          },
        ],
      };

      const result = OrderDomainService.updatePurchaseOrderStatus({
        order: orderWithoutInventory,
        status: PurchaseOrderStatus.COMPLETED,
        modifierCompanyId,
      });

      expect(result.purchaseOrder.status).toBe(PurchaseOrderStatus.COMPLETED);
      expect(result.inventory).toEqual([]);
    });

    it('should filter out null inventory items when completing a purchase order', () => {
      // Create a purchase order with mixed inventory items (some null, some valid)
      const mixedPurchaseOrder: PurchaseOrderEntity = {
        ...mockPurchaseOrder,
        orderItems: [
          {
            ...mockPurchaseOrder.orderItems[0],
            inventoryIds: ['inventory1'],
          },
          {
            ...mockPurchaseOrder.orderItems[1],
            inventoryIds: [],
          },
        ],
      };

      const result = OrderDomainService.updatePurchaseOrderStatus({
        order: mixedPurchaseOrder,
        status: PurchaseOrderStatus.COMPLETED,
        modifierCompanyId,
        inventory: mockInventory,
      });

      expect(result.purchaseOrder.status).toBe(PurchaseOrderStatus.COMPLETED);
      // Should only have one inventory item (the non-null one) in the result
      expect(result.inventory.length).toBe(1);
      expect(result.inventory[0].id).toBe('inventory1');
      expect(result.inventory[0].stock).toBe(55); // 50 + 5
    });

    it('should throw error with NOT_FOUND cause when inventory item is not found', () => {
      const incompleteInventory = [mockInventory[0]]; // Missing inventory2

      expect(() => {
        OrderDomainService.updatePurchaseOrderStatus({
          order: { ...mockPurchaseOrder },
          status: PurchaseOrderStatus.COMPLETED,
          modifierCompanyId,
          inventory: incompleteInventory,
        });
      }).toThrow();

      // Verify the error includes the NOT_FOUND cause
      try {
        OrderDomainService.updatePurchaseOrderStatus({
          order: { ...mockPurchaseOrder },
          status: PurchaseOrderStatus.COMPLETED,
          modifierCompanyId,
          inventory: incompleteInventory,
        });
      } catch (error) {
        expect((error as Error).cause).toBe(Cause.NOT_FOUND);
      }
    });
  });

  describe('createPurchaseOrder', () => {
    it('should create a purchase order without creating inventory items when createInventoryFromOrderItems is false', async () => {
      const orderBuildParams = {
        id: 'new-purchase-123',
        readId: 'PO-NEW-123',
        subtotalBeforeDiscount: 1600,
        subtotal: 1600,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 1600,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        providerId: 'provider456',
        status: PurchaseOrderStatus.PENDING,
        orderItems: [
          {
            id: 'soi-1',
            productId: 'product1',
            inventoryId: 'inventory1',
            referenceId: 'ref-001',
            name: 'Product 1',
            quantity: 5,
            unitPrice: 80,
            unitPriceAfterDiscount: 80,
            unitPriceAfterDiscountAndTaxes: 80,
            subtotal: 400,
            total: 400,
            discount: null,
            taxes: [],
          },
        ],
      };

      const result = await OrderDomainService.createPurchaseOrder({
        orderBuildParams,
        createInventoryFromOrderItems: false,
        catalogReference: new Map(),
      });

      expect(result.purchaseOrder).toBeDefined();
      expect(result.purchaseOrder.id).toBe('new-purchase-123');
      expect(result.inventory).toEqual([]);
      expect(PurchaseOrderOperator.build).toHaveBeenCalledWith(expect.objectContaining({
        orderItems: expect.arrayContaining([
          expect.objectContaining({
            inventoryId: 'inventory1', // Should remain unchanged
          }),
        ]),
      }));
    });

    it('should create a purchase order and create inventory items for items without inventoryId', async () => {
      const orderBuildParams = {
        id: 'new-purchase-123',
        readId: 'PO-NEW-123',
        subtotalBeforeDiscount: 1600,
        subtotal: 1600,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 1600,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        providerId: 'provider456',
        status: PurchaseOrderStatus.PENDING,
        orderItems: [
          {
            id: 'soi-1',
            productId: 'product1',
            inventoryId: null, // No inventory ID
            referenceId: 'ref-001',
            name: 'Product 1',
            quantity: 5,
            unitPrice: 80,
            unitPriceAfterDiscount: 80,
            unitPriceAfterDiscountAndTaxes: 80,
            subtotal: 400,
            total: 400,
            discount: null,
            taxes: [],
          },
        ],
      };

      const result = await OrderDomainService.createPurchaseOrder({
        orderBuildParams,
        createInventoryFromOrderItems: true,
        catalogReference: new Map(),
      });

      expect(result.purchaseOrder).toBeDefined();
      expect(result.inventory.length).toBe(1);
      expect(result.inventory[0].name).toBe('Product 1');
      expect(result.inventory[0].sku).toBe('product1');
      expect(result.inventory[0].type).toBe(InventoryType.COMMODITY);
      expect(result.inventory[0].providers[0].providerId).toBe('provider456');
      expect(result.inventory[0].providers[0].currentPurchasePrice).toBe(80);

      // Validate order items were updated with new inventory ID
      expect(PurchaseOrderOperator.build).toHaveBeenCalledWith(expect.objectContaining({
        orderItems: expect.arrayContaining([
          expect.objectContaining({
            inventoryIds: [result.inventory[0].id],
          }),
        ]),
      }));
    });

    it('should handle mixed order items with and without inventoryId', async () => {
      const orderBuildParams = {
        id: 'new-purchase-123',
        readId: 'PO-NEW-123',
        subtotalBeforeDiscount: 1600,
        subtotal: 1600,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 1600,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        providerId: 'provider456',
        status: PurchaseOrderStatus.PENDING,
        orderItems: [
          {
            id: 'soi-1',
            productId: 'product1',
            inventoryIds: ['existing-inventory-id'],
            referenceId: 'ref-001',
            name: 'Product 1',
            quantity: 5,
            unitPrice: 80,
            unitPriceAfterDiscount: 80,
            unitPriceAfterDiscountAndTaxes: 80,
            subtotal: 400,
            total: 400,
            discount: null,
            taxes: [],
          },
          {
            id: 'soi-2',
            productId: 'product2',
            inventoryIds: [],
            referenceId: 'ref-002',
            name: 'Product 2',
            quantity: 10,
            unitPrice: 120,
            unitPriceAfterDiscount: 120,
            unitPriceAfterDiscountAndTaxes: 120,
            subtotal: 1200,
            total: 1200,
            discount: null,
            taxes: [],
          },
        ],
      };

      const result = await OrderDomainService.createPurchaseOrder({
        orderBuildParams,
        createInventoryFromOrderItems: true,
        catalogReference: new Map(),
      });

      expect(result.purchaseOrder).toBeDefined();
      expect(result.inventory.length).toBe(1);
      expect(result.inventory[0].name).toBe('Product 2');

      // Should update PurchaseOrderOperator.build with correct order items
      expect(PurchaseOrderOperator.build).toHaveBeenCalledWith(expect.objectContaining({
        orderItems: expect.arrayContaining([
          expect.objectContaining({
            productId: 'product1',
            inventoryIds: ['existing-inventory-id'], // Unchanged
          }),
          expect.objectContaining({
            productId: 'product2',
            inventoryIds: [result.inventory[0].id], // Updated with new inventory ID
          }),
        ]),
      }));
    });

    it('should calculate proper discount percentage for provider', async () => {
      const orderBuildParams = {
        id: 'new-purchase-123',
        readId: 'PO-NEW-123',
        subtotalBeforeDiscount: 1600,
        subtotal: 1600,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 1600,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        providerId: 'provider456',
        status: PurchaseOrderStatus.PENDING,
        orderItems: [
          {
            id: 'soi-1',
            productId: 'product1',
            inventoryId: null,
            referenceId: 'ref-001',
            name: 'Product 1',
            quantity: 5,
            unitPrice: 100,
            unitPriceAfterDiscount: 80, // 20% discount
            unitPriceAfterDiscountAndTaxes: 80,
            subtotal: 400,
            total: 400,
            discount: null,
            taxes: [],
          },
        ],
      };

      const result = await OrderDomainService.createPurchaseOrder({
        orderBuildParams,
        createInventoryFromOrderItems: true,
        catalogReference: new Map(),
      });

      expect(result.inventory.length).toBe(1);
      expect(result.inventory[0].providers[0].currentPurchasePrice).toBe(100);
      expect(result.inventory[0].providers[0].currentDiscount).toBe(20); // 20% discount
    });

    it('should handle zero unitPrice when calculating discount', async () => {
      const orderBuildParams = {
        id: 'new-purchase-123',
        readId: 'PO-NEW-123',
        subtotalBeforeDiscount: 0,
        subtotal: 0,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 0,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        providerId: 'provider456',
        status: PurchaseOrderStatus.PENDING,
        orderItems: [
          {
            id: 'soi-1',
            productId: 'product1',
            inventoryId: null,
            referenceId: 'ref-001',
            name: 'Product 1',
            quantity: 5,
            unitPrice: 0, // Zero price
            unitPriceAfterDiscount: 0,
            unitPriceAfterDiscountAndTaxes: 0,
            subtotal: 0,
            total: 0,
            discount: null,
            taxes: [],
          },
        ],
      };

      const result = await OrderDomainService.createPurchaseOrder({
        orderBuildParams,
        createInventoryFromOrderItems: true,
        catalogReference: new Map(),
      });

      expect(result.inventory.length).toBe(1);
      expect(result.inventory[0].providers[0].currentPurchasePrice).toBe(0);
      expect(result.inventory[0].providers[0].currentDiscount).toBe(0); // Should not cause division by zero
    });

    it('should use attributes from catalogReference when creating inventory', async () => {
      const orderBuildParams = {
        id: 'new-purchase-123',
        readId: 'PO-NEW-123',
        subtotalBeforeDiscount: 1600,
        subtotal: 1600,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 1600,
        shippingPrice: 0,
        shippingAddress: null,
        taxes: [],
        companyId: mockCompanyId,
        providerId: 'provider456',
        status: PurchaseOrderStatus.PENDING,
        orderItems: [
          {
            id: 'poi-1',
            productId: 'product1',
            inventoryId: null,
            referenceId: 'ref-001',
            name: 'Product 1',
            quantity: 5,
            unitPrice: 80,
            unitPriceAfterDiscount: 80,
            unitPriceAfterDiscountAndTaxes: 80,
            subtotal: 400,
            total: 400,
            discount: null,
            taxes: [],
          },
        ],
      };

      const mockAttributes: AttributeType[] = [
        { key: 'color', values: ['red'] },
        { key: 'size', values: ['medium'] },
      ];

      const catalogReference = new Map([
        ['ref-001', {
          id: 'catalog-001',
          attributes: mockAttributes,
          readId: 'CAT-001',
          name: 'Product 1',
          description: 'Test product',
          price: 100,
          requiresStock: true,
          type: CatalogType.PRODUCT,
          discountIds: [],
          taxIds: [],
          mediaIds: [],
          inventoryRelations: [],
          companyId: mockCompanyId,
          disabledAt: null,
          createdAt: mockDate,
          updatedAt: mockDate,
        }],
      ]);

      const result = await OrderDomainService.createPurchaseOrder({
        orderBuildParams,
        createInventoryFromOrderItems: true,
        catalogReference,
      });

      expect(result.inventory.length).toBe(1);
      expect(result.inventory[0].attributes).toEqual(mockAttributes);
    });
  });
});
