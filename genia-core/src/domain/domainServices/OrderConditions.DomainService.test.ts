import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import DomainUtil from '#domain/common/Domain.Util';

import OrderConditionsDomainService, { OrderItemConditions } from './OrderConditions.DomainService';

jest.unmock('./OrderConditions.DomainService');

describe('OrderConditions.DomainService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Setup the price utility mocks with default implementations that simulate real behavior
    (DomainUtil.toOperablePrice as jest.Mock).mockImplementation((value) => Number(value));
    (DomainUtil.toStorablePrice as jest.Mock).mockImplementation((value) => Number(value));
  });

  describe('calculateConditions', () => {
    it('should calculate order conditions correctly with no shipping', () => {
      // Arrange
      const orderItems: OrderItemConditions[] = [
        {
          catalogId: 'item1',
          quantity: 2,
          unitPrice: 100,
          unitPriceAfterDiscount: 80,
          unitPriceAfterDiscountAndTaxes: 96,
          taxes: [
            {
              id: 'tax1', value: 20, type: TaxType.PERCENTAGE, name: 'VAT', amount: 16,
            },
          ],
        },
        {
          catalogId: 'item1',
          quantity: 1,
          unitPrice: 50,
          unitPriceAfterDiscount: 50,
          unitPriceAfterDiscountAndTaxes: 65,
          taxes: [
            {
              id: 'tax1', value: 20, type: TaxType.PERCENTAGE, name: 'VAT', amount: 5,
            },
            {
              id: 'tax2', value: 10, type: TaxType.AMOUNT, name: 'Eco Tax', amount: 10,
            },
          ],
        },
      ];

      // Act
      const result = OrderConditionsDomainService.calculateConditions(
        {
          orderItems,
          shippingPrice: 0,
        },
      );

      // Assert
      expect(result.subtotal).toBe(210); // (80 * 2) + 50
      expect(result.totalDiscount).toBe(40); // (100 - 80) * 2 + (50 - 50) * 1
      expect(result.totalTaxes).toBe(47);
      expect(result.total).toBe(257); // 210 + 47

      // Check tax aggregation
      expect(result.taxes).toHaveLength(2);
      const vatTax = result.taxes.find((tax) => tax.name === 'VAT');
      const ecoTax = result.taxes.find((tax) => tax.name === 'Eco Tax');
      expect(vatTax?.amount).toBe(37); // 16*2 + 5
      expect(ecoTax?.amount).toBe(10);

      // Check items in return
      expect(result.orderItems).toHaveLength(2);
      expect(result.orderItems[0].subtotal).toBe(160); // 80 * 2
      expect(result.orderItems[0].total).toBe(192); // 96 * 2
      expect(result.orderItems[1].subtotal).toBe(50); // 50 * 1
      expect(result.orderItems[1].total).toBe(65); // 65 * 1

      // Verify the utility functions were called correctly
      expect(DomainUtil.toOperablePrice).toHaveBeenCalled();
      expect(DomainUtil.toStorablePrice).toHaveBeenCalled();
    });

    it('should calculate order conditions correctly with shipping and tax on shipping', () => {
      // Arrange
      const orderItems: OrderItemConditions[] = [
        {
          catalogId: 'item1',
          quantity: 2,
          unitPrice: 100,
          unitPriceAfterDiscount: 80,
          unitPriceAfterDiscountAndTaxes: 96,
          taxes: [
            {
              id: 'tax1', value: 20, type: TaxType.PERCENTAGE, name: 'VAT', amount: 16,
            },
          ],
        },
      ];

      // Act
      const result = OrderConditionsDomainService.calculateConditions(
        {
          orderItems,
          shippingPrice: 50,
          addTaxToShipping: true,
        },
      );

      // Assert
      expect(result.subtotal).toBe(160); // 80 * 2
      expect(result.shippingPrice).toBe(50);
      expect(result.totalDiscount).toBe(40); // (100 - 80) * 2
      expect(result.hasTaxOnShipping).toBe(true);

      // Check tax calculation with shipping
      // Base tax amount: 32 (16*2) + Shipping tax: 50 * 0.2 = 10 => Total: 42
      expect(result.totalTaxes).toBe(42);
      expect(result.total).toBe(252); // 160 + 50 + 42

      // Check tax details
      expect(result.taxes).toHaveLength(1);
      const vatTax = result.taxes.find((tax) => tax.name === 'VAT');
      expect(vatTax?.amount).toBe(42); // 32 + 10 (shipping tax)
    });

    it('should calculate order conditions correctly with shipping but no tax on shipping', () => {
      // Arrange
      const orderItems: OrderItemConditions[] = [
        {
          catalogId: 'item1',
          quantity: 2,
          unitPrice: 100,
          unitPriceAfterDiscount: 80,
          unitPriceAfterDiscountAndTaxes: 96,
          taxes: [
            {
              id: 'tax1', value: 20, type: TaxType.PERCENTAGE, name: 'VAT', amount: 16,
            },
          ],
        },
      ];

      // Act
      const result = OrderConditionsDomainService.calculateConditions(
        {
          orderItems,
          shippingPrice: 50,
          addTaxToShipping: false,
        },
      );

      // Assert
      expect(result.subtotal).toBe(160); // 80 * 2
      expect(result.shippingPrice).toBe(50);
      expect(result.hasTaxOnShipping).toBe(false);
      expect(result.totalDiscount).toBe(40); // (100 - 80) * 2

      // Tax only on the items, not on shipping
      expect(result.totalTaxes).toBe(32); // 16 * 2
      expect(result.total).toBe(242); // 160 + 50 + 32

      // Check tax details
      expect(result.taxes).toHaveLength(1);
      const vatTax = result.taxes.find((tax) => tax.name === 'VAT');
      expect(vatTax?.amount).toBe(32); // 16 * 2 (no shipping tax)
    });

    it('should handle empty order items', () => {
      // Arrange
      const orderItems: OrderItemConditions[] = [];

      // Act
      const result = OrderConditionsDomainService.calculateConditions({
        orderItems,
        shippingPrice: 0,
      });

      // Assert
      expect(result.subtotal).toBe(0);
      expect(result.totalDiscount).toBe(0);
      expect(result.totalTaxes).toBe(0);
      expect(result.total).toBe(0);
      expect(result.taxes).toEqual([]);
      expect(result.orderItems).toEqual([]);
      expect(result.hasTaxOnShipping).toBe(true); // Default value should be true
    });

    it('should handle items with no taxes', () => {
      // Arrange
      const orderItems: OrderItemConditions[] = [
        {
          catalogId: 'item1',
          quantity: 3,
          unitPrice: 100,
          unitPriceAfterDiscount: 90,
          unitPriceAfterDiscountAndTaxes: 90,
          taxes: [],
        },
      ];

      // Act
      const result = OrderConditionsDomainService.calculateConditions({
        orderItems,
        shippingPrice: 0,
      });

      // Assert
      expect(result.subtotal).toBe(270); // 90 * 3
      expect(result.totalDiscount).toBe(30); // (100 - 90) * 3
      expect(result.totalTaxes).toBe(0);
      expect(result.total).toBe(270); // 270 + 0
      expect(result.taxes).toEqual([]);
      expect(result.hasTaxOnShipping).toBe(true); // Default value
    });
  });
});
