import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';

function validateDiscounts(clientDiscounts: StoreDiscountEntity[]): void {
  const permutations = new Set<string>();

  clientDiscounts.forEach((discount) => {
    discount.clientIds.forEach((clientId) => {
      const permutation = `${clientId}-${discount.requiredAmount}`;

      if (permutations.has(permutation)) {
        throw new Error(`Discount is duplicated for client ${clientId} and required amount ${discount.requiredAmount}`, { cause: 'BAD_REQUEST' });
      }

      permutations.add(permutation);
    });
  });
}

export default {
  validateDiscounts,
};
