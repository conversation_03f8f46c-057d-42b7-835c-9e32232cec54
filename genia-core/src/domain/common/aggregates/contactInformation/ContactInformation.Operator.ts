import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';

export type ContactInformationBuildParams = Partial<ContactInformationValueObject>;

function build(params: ContactInformationBuildParams): ContactInformationValueObject {
  const {
    mainEmail = null,
    mainPhoneNumber = null,
    mainWhatsapp = null,
    billingEmail = null,
    billingPhoneNumber = null,
    billingWhatsapp = null,
    purchasesEmail = null,
    purchasesPhoneNumber = null,
    purchasesWhatsapp = null,
    salesEmail = null,
    salesPhoneNumber = null,
    salesWhatsapp = null,
    shippingAddress = null,
    billingAddress = null,
    mainAddress = null,
    representativeName = null,
  } = params;

  return {
    mainEmail,
    mainPhoneNumber,
    mainWhatsapp,
    billingEmail,
    billingPhoneNumber,
    billingWhatsapp,
    purchasesEmail,
    purchasesPhoneNumber,
    purchasesWhatsapp,
    salesEmail,
    salesPhoneNumber,
    salesWhatsapp,
    shippingAddress,
    billingAddress,
    mainAddress,
    representativeName,
  };
}

export default {
  build,
};
