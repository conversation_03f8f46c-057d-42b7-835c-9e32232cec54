export interface ContactInformationValueObject {
  mainEmail: string | null;
  mainPhoneNumber: string | null;
  mainWhatsapp: string | null;
  billingEmail: string | null;
  billingPhoneNumber: string | null;
  billingWhatsapp: string | null;
  purchasesEmail: string | null;
  purchasesPhoneNumber: string | null;
  purchasesWhatsapp: string | null;
  salesEmail: string | null;
  salesPhoneNumber: string | null;
  salesWhatsapp: string | null;
  shippingAddress: string | null;
  mainAddress: string | null;
  billingAddress: string | null;
  representativeName: string | null;
}

export enum ContactInformationEntity {
  CLIENT = 'client',
  PROVIDER = 'provider',
  COMPANY = 'company',
}
