import ContactInformationOperator, { ContactInformationBuildParams } from '#domain/common/aggregates/contactInformation/ContactInformation.Operator';

jest.unmock('#domain/common/aggregates/contactInformation/ContactInformation.Operator');

describe('ContactInformation.Operator', () => {
  describe('build', () => {
    it('should return a ContactInformationValueObject with default values', () => {
      const params: ContactInformationBuildParams = {};
      const expected = {
        mainEmail: null,
        mainPhoneNumber: null,
        mainWhatsapp: null,
        billingEmail: null,
        billingPhoneNumber: null,
        billingWhatsapp: null,
        purchasesEmail: null,
        purchasesPhoneNumber: null,
        purchasesWhatsapp: null,
        salesEmail: null,
        salesPhoneNumber: null,
        salesWhatsapp: null,
        shippingAddress: null,
        billingAddress: null,
        mainAddress: null,
        representativeName: null,
      };

      const got = ContactInformationOperator.build(params);

      expect(got).toStrictEqual(expected);
    });

    it('should return a ContactInformationValueObject with custom values', () => {
      const params: ContactInformationBuildParams = {
        mainEmail: 'mainEmail',
        mainPhoneNumber: 'mainPhoneNumber',
        mainWhatsapp: 'mainWhatsapp',
        billingEmail: 'billingEmail',
        billingPhoneNumber: 'billingPhoneNumber',
        billingWhatsapp: 'billingWhatsapp',
        purchasesEmail: 'purchasesEmail',
        purchasesPhoneNumber: 'purchasesPhoneNumber',
        purchasesWhatsapp: 'purchasesWhatsapp',
        salesEmail: 'salesEmail',
        salesPhoneNumber: 'salesPhoneNumber',
        salesWhatsapp: 'salesWhatsapp',
        shippingAddress: 'shippingAddress',
        billingAddress: 'billingAddress',
        mainAddress: 'mainAddress',
        representativeName: 'representativeName',
      };

      const got = ContactInformationOperator.build(params);

      expect(got).toStrictEqual(params);
    });
  });
});
