import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import OrderEntity, { OrderStatus } from '#domain/common/aggregates/order/Order.Entity';
import OrderOperator, { OrderBuildParams } from '#domain/common/aggregates/order/Order.Operator';

jest.unmock('./Order.Operator');
jest.unmock('#domain/common/aggregates/order/Order.Operator');

describe('OrderOperator', () => {
  describe('build', () => {
    it('should build a valid  order successfully', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [], // Añadido campo taxes que faltaba
          },
        ],
        taxes: [
          {
            value: 10, name: 'VAT', type: TaxType.PERCENTAGE, amount: 10,
          }, // Corregido formato de taxes
        ],
      };

      const result = OrderOperator.build(params);

      expect(result).toEqual({
        ...params,
        notes: null,
        deliveryDate: null,
        shippingAddress: null,
        shippingPrice: 0,
        reviewStartedAt: null,
        shippedAt: null,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        orderItems: [
          {
            ...params.orderItems[0],
            discount: null,
            taxes: [], // Corregido
            createdAt: expect.any(Date),
            updatedAt: expect.any(Date),
          },
        ],
      });
    });

    it('should build a  order with all optional parameters', () => {
      const now = new Date();
      const deliveryDate = new Date(now.getTime() + 86400000); // Tomorrow

      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PENDING, // Corregido, DRAFT no está en el enum
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        notes: 'Special instructions',
        deliveryDate,
        createdAt: now,
        updatedAt: now,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            discount: {
              value: 10,
              type: DiscountType.PERCENTAGE,
            },
            taxes: [
              {
                value: 10, name: 'VAT', type: TaxType.PERCENTAGE, amount: 10,
              }, // Corregido formato
            ],
          },
        ],
        taxes: [
          {
            value: 10, name: 'VAT', type: TaxType.PERCENTAGE, amount: 10,
          }, // Corregido formato
        ],
      };

      const result = OrderOperator.build(params);

      expect(result).toEqual({
        ...params,
        notes: 'Special instructions',
        assignedUserId: null,
        deliveryDate,
        createdAt: now,
        updatedAt: now,
        reviewStartedAt: null,
        shippedAt: null,
        shippingAddress: null,
        shippingPrice: 0,
        orderItems: [
          {
            ...params.orderItems[0],
            createdAt: now,
            updatedAt: now,
            discount: {
              value: 10,
              type: DiscountType.PERCENTAGE,
            },
            taxes: [
              {
                value: 10, name: 'VAT', type: TaxType.PERCENTAGE, amount: 10,
              },
            ],
          },
        ],
      });
    });

    it('should throw an error when order items are empty', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Order items cannot be empty');
    });

    it('should throw an error when subtotal is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: -100, // Negative value
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [], // Añadido campo taxes
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Subtotal cannot be negative');
    });

    it('should throw an error when totalDiscount is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: -20, // Negative value
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [], // Añadido campo taxes
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Total discount cannot be negative');
    });

    it('should throw an error when item quantity is zero', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 0, // Zero quantity
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [], // Añadido campo taxes
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Quantity cannot be less than or equal to 0');
    });

    it('should throw an error when item unitPrice is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: -60, // Negative value
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [], // Añadido campo taxes
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Unit price cannot be negative');
    });

    it('should throw an error when totalTaxes is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: -10, // Negative value
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Total taxes cannot be negative');
    });

    it('should throw an error when total is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: -110, // Negative value
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Total cannot be negative');
    });

    it('should throw an error when subtotalBeforeDiscount is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: -120, // Negative value
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Sub total before discount cannot be negative');
    });

    it('should throw an error when unitPriceAfterDiscount is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: -50, // Negative value
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Unit price after discount cannot be negative');
    });

    it('should throw an error when unitPriceAfterDiscountAndTaxes is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: -55, // Negative value
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Unit price after discount and taxes cannot be negative');
    });

    it('should throw an error when item subtotal is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: -100, // Negative value
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Subtotal cannot be negative');
    });

    it('should throw an error when item total is negative', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: -110, // Negative value
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Total cannot be negative');
    });

    it('should throw an error when subtotal is greater than total', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 150, // Higher than total
        subtotalBeforeDiscount: 160,
        totalDiscount: 10,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Subtotal cannot be greater than total');
    });

    it('should throw an error when totalDiscount is greater than subtotal', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 80,
        subtotalBeforeDiscount: 180,
        totalDiscount: 100, // Greater than subtotal
        totalTaxes: 10,
        total: 90,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 40,
            unitPriceAfterDiscountAndTaxes: 45,
            subtotal: 80,
            total: 90,
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Total discount cannot be greater than subtotal');
    });

    it('should throw an error when unitPriceAfterDiscount is greater than unitPrice', () => {
      const params: OrderBuildParams = {
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 50,
            unitPriceAfterDiscount: 60, // Greater than unitPrice
            unitPriceAfterDiscountAndTaxes: 65,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      };

      expect(() => {
        OrderOperator.build(params);
      }).toThrow('Unit price after discount cannot be greater than unit price');
    });
  });

  it('should throw an error when shipping price is negative', () => {
    const params: OrderBuildParams = {
      id: '1234',
      readId: 'PO_1234',
      companyId: 'COMP_1234',
      assignedUserId: 'USER_1234',
      status: OrderStatus.PENDING,
      subtotal: 100,
      subtotalBeforeDiscount: 120,
      totalDiscount: 20,
      totalTaxes: 10,
      total: 110,
      shippingPrice: -10, // Negative value
      orderItems: [
        {
          name: 'Test Product',
          productId: 'PROD_1234',
          quantity: 2,
          unitPrice: 60,
          unitPriceAfterDiscount: 50,
          unitPriceAfterDiscountAndTaxes: 55,
          subtotal: 100,
          total: 110,
          taxes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      taxes: [],
    };

    expect(() => {
      OrderOperator.build(params);
    }).toThrow('Shipping price cannot be negative');
  });

  describe('updateStatus', () => {
    it('should update status successfully from CLIENT_APPROVAL to APPROVED_BY_CLIENT', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.CLIENT_APPROVAL,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const result = OrderOperator.updateStatus(order, OrderStatus.APPROVED_BY_CLIENT);

      expect(result.status).toBe(OrderStatus.APPROVED_BY_CLIENT);
      expect(result.shippedAt).toBeNull();
      expect(result.reviewStartedAt).toBeNull();
    });

    it('should update status successfully from PENDING to IN_REVIEW and set reviewStartedAt', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const result = OrderOperator.updateStatus(order, OrderStatus.IN_REVIEW);

      expect(result.status).toBe(OrderStatus.IN_REVIEW);
      expect(result.shippedAt).toBeNull();
      expect(result.reviewStartedAt).toBeInstanceOf(Date);
    });

    it('should update status successfully from PROCESSING to IN_TRANSIT and set shippedAt', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PROCESSING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const result = OrderOperator.updateStatus(order, OrderStatus.IN_TRANSIT);

      expect(result.status).toBe(OrderStatus.IN_TRANSIT);
      expect(result.shippedAt).toBeInstanceOf(Date);
    });

    it('should throw error when trying to update to the same status', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      expect(() => {
        OrderOperator.updateStatus(order, OrderStatus.PENDING);
      }).toThrow('New status is the same as the current status');
    });

    it('should throw error when order is already completed', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.COMPLETED,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      expect(() => {
        OrderOperator.updateStatus(order, OrderStatus.PENDING);
      }).toThrow('Order is already completed');
    });

    it('should throw error when order is cancelled', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.CANCELLED,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      expect(() => {
        OrderOperator.updateStatus(order, OrderStatus.PENDING);
      }).toThrow('Order is cancelled');
    });

    it('should throw error for invalid status transition', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.CLIENT_APPROVAL,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      expect(() => {
        OrderOperator.updateStatus(order, OrderStatus.PROCESSING);
      }).toThrow('Invalid status change from client_approval to processing');
    });
  });

  describe('update', () => {
    it('should update order notes successfully', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const updatedOrder = OrderOperator.update(order as OrderEntity<OrderStatus>, { notes: 'Updated notes' });

      expect(updatedOrder.notes).toBe('Updated notes');
      expect(updatedOrder.updatedAt).not.toBe(order.updatedAt);
    });

    it('should update assigned user successfully', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const updatedOrder = OrderOperator.update(order, { assignedUserId: 'USER_5678' });

      expect(updatedOrder.assignedUserId).toBe('USER_5678');
    });

    it('should update shipping address successfully', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const updatedOrder = OrderOperator.update(order, { shippingAddress: '123 Main St' });

      expect(updatedOrder.shippingAddress).toBe('123 Main St');
    });

    it('should update delivery date successfully', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const deliveryDate = new Date();
      const updatedOrder = OrderOperator.update(order, { deliveryDate });

      expect(updatedOrder.deliveryDate).toBe(deliveryDate);
    });

    it('should throw error when trying to set assigned user to null when it already has one', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        assignedUserId: 'USER_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      expect(() => {
        OrderOperator.update(order, { assignedUserId: null });
      }).toThrow('Assigned user can not be unassigned');
    });

    it('should throw error when trying to modify delivery date for completed order', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.COMPLETED,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      expect(() => {
        OrderOperator.update(order, { deliveryDate: new Date() });
      }).toThrow('Cannot modify delivery date for completed or cancelled orders');
    });

    it('should throw error when trying to modify shipping address for cancelled order', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.CANCELLED,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      expect(() => {
        OrderOperator.update(order, { shippingAddress: '123 Main St' });
      }).toThrow('Cannot modify shipping address for completed or cancelled orders');
    });

    it('should throw error when trying to modify assigned user for completed order', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.COMPLETED,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      expect(() => {
        OrderOperator.update(order, { assignedUserId: 'USER_5678' });
      }).toThrow('Cannot modify assigned user for completed or cancelled orders');
    });

    it('should return the same order when no properties are provided to update', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const updatedOrder = OrderOperator.update(order, {});

      expect(updatedOrder).toEqual(order);
    });
  });

  describe('availableStatuses', () => {
    it('should return correct available statuses for CLIENT_APPROVAL status', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.CLIENT_APPROVAL,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const availableStatuses = OrderOperator.availableStatuses(order);
      expect(availableStatuses).toEqual([
        OrderStatus.APPROVED_BY_CLIENT,
        OrderStatus.CANCELLED,
      ]);
    });

    it('should return correct available statuses for APPROVED_BY_CLIENT status', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.APPROVED_BY_CLIENT,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const availableStatuses = OrderOperator.availableStatuses(order);
      expect(availableStatuses).toEqual([
        OrderStatus.PENDING,
        OrderStatus.CANCELLED,
      ]);
    });

    it('should return correct available statuses for PENDING status', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.PENDING,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const availableStatuses = OrderOperator.availableStatuses(order);
      expect(availableStatuses).toEqual([
        OrderStatus.CANCELLED,
        OrderStatus.PROCESSING,
        OrderStatus.IN_REVIEW,
      ]);
    });

    it('should return empty array for COMPLETED status', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.COMPLETED,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const availableStatuses = OrderOperator.availableStatuses(order);
      expect(availableStatuses).toEqual([]);
    });

    it('should return empty array for CANCELLED status', () => {
      const order = OrderOperator.build({
        id: '1234',
        readId: 'PO_1234',
        companyId: 'COMP_1234',
        status: OrderStatus.CANCELLED,
        subtotal: 100,
        subtotalBeforeDiscount: 120,
        totalDiscount: 20,
        totalTaxes: 10,
        total: 110,
        orderItems: [
          {
            name: 'Test Product',
            productId: 'PROD_1234',
            quantity: 2,
            unitPrice: 60,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 55,
            subtotal: 100,
            total: 110,
            taxes: [],
          },
        ],
        taxes: [],
      });

      const availableStatuses = OrderOperator.availableStatuses(order);
      expect(availableStatuses).toEqual([]);
    });
  });
});
