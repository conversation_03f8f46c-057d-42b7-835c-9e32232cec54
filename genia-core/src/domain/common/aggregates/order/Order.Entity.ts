import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';

export enum OrderStatus {
  CLIENT_APPROVAL = 'client_approval',
  APPROVED_BY_CLIENT = 'approved_by_client',
  PENDING = 'pending',
  PROCESSING = 'processing',
  IN_TRANSIT = 'in_transit',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  IN_REVIEW = 'in_review',
}

export interface OrderItem {
  productId: string;
  name: string;
  quantity: number;
  unitPrice: number;
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  subtotal: number;
  total: number;
  discount: {
    value: number;
    type: DiscountType;
  } | null;
  taxes: {
    value: number;
    name: string;
    type: TaxType;
    amount: number;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

export default interface OrderEntity<T extends OrderStatus = OrderStatus > {
  id: string;
  companyId: string;
  assignedUserId: string | null;
  readId: string;
  status: T;
  deliveryDate: Date | null;
  shippedAt: Date | null;
  reviewStartedAt: Date | null;
  subtotalBeforeDiscount: number;
  subtotal: number;
  totalDiscount: number;
  totalTaxes: number;
  total: number;
  shippingPrice: number;
  shippingAddress: string | null;
  notes: string | null;
  taxes: {
    value: number,
    name: string
    type: TaxType;
    amount: number,
  }[];
  createdAt: Date;
  updatedAt: Date;
  orderItems: OrderItem[];
}
