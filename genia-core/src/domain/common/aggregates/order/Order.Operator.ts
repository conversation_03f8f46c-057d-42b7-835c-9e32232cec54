import Cause from '#composition/Cause.type';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import OrderEntity, { OrderItem, OrderStatus } from '#domain/common/aggregates/order/Order.Entity';
import { Modify } from '#domain/common/Common.Type';

export type OrderItemBuildParams = Modify<OrderItem, {
  createdAt?: Date;
  updatedAt?: Date;
  discount?: {
    value: number;
    type: DiscountType;
  } | null;
}>;

export type OrderBuildParams<T extends OrderStatus = OrderStatus> = Modify<OrderEntity, {
  createdAt?: Date;
  updatedAt?: Date;
  notes?: string | null;
  deliveryDate?: Date | null;
  orderItems: OrderItemBuildParams[];
  shippedAt?: Date | null;
  reviewStartedAt?: Date | null;
  shippingPrice?: number;
  shippingAddress?: string | null;
  assignedUserId?: string | null;
  status: T;
}>;

export type OrderUpdateParams<T extends OrderStatus = OrderStatus> =
 Modify<Pick<OrderEntity, 'notes' | 'assignedUserId' | 'shippingAddress' | 'status' | 'deliveryDate'>, {
   notes?: string | null;
   assignedUserId?: string | null;
   shippingAddress?: string | null;
   deliveryDate?: Date | null;
   status?: T;
 }>;

const stateFlowMap: {[key: string]: OrderStatus[]} = {
  [OrderStatus.CLIENT_APPROVAL]: [
    OrderStatus.APPROVED_BY_CLIENT,
    OrderStatus.CANCELLED,
  ],
  [OrderStatus.APPROVED_BY_CLIENT]: [
    OrderStatus.PENDING,
    OrderStatus.CANCELLED,
  ],
  [OrderStatus.PENDING]: [
    OrderStatus.CANCELLED,
    OrderStatus.PROCESSING,
    OrderStatus.IN_REVIEW,
  ],
  [OrderStatus.PROCESSING]: [
    OrderStatus.IN_REVIEW,
    OrderStatus.IN_TRANSIT,
    OrderStatus.CANCELLED,
  ],
  [OrderStatus.IN_TRANSIT]: [
    OrderStatus.IN_REVIEW,
    OrderStatus.COMPLETED,
    OrderStatus.CANCELLED,
  ],
  [OrderStatus.IN_REVIEW]: [
    OrderStatus.PROCESSING,
    OrderStatus.IN_TRANSIT,
    OrderStatus.CANCELLED,
  ],
};

function build<T extends OrderStatus = OrderStatus>(params: OrderBuildParams<T>): OrderEntity<T> {
  const {
    id,
    readId,
    companyId,
    assignedUserId,
    status,
    subtotal,
    subtotalBeforeDiscount,
    totalDiscount,
    totalTaxes,
    total,
    notes,
    orderItems,
    taxes,
    deliveryDate,
    createdAt,
    updatedAt,
    shippedAt,
    reviewStartedAt,
    shippingPrice,
    shippingAddress,
  } = params;

  if (orderItems.length === 0) {
    throw new Error('Order items cannot be empty', { cause: Cause.BAD_REQUEST });
  }

  if (subtotal < 0) {
    throw new Error('Subtotal cannot be negative', { cause: Cause.BAD_REQUEST });
  }

  if (totalDiscount < 0) {
    throw new Error('Total discount cannot be negative', { cause: Cause.BAD_REQUEST });
  }

  if (totalTaxes < 0) {
    throw new Error('Total taxes cannot be negative', { cause: Cause.BAD_REQUEST });
  }

  if (total < 0) {
    throw new Error('Total cannot be negative', { cause: Cause.BAD_REQUEST });
  }

  if (subtotalBeforeDiscount < 0) {
    throw new Error('Sub total before discount cannot be negative', { cause: Cause.BAD_REQUEST });
  }

  if (shippingPrice && shippingPrice < 0) {
    throw new Error('Shipping price cannot be negative', { cause: Cause.BAD_REQUEST });
  }

  if (subtotal > total) {
    throw new Error('Subtotal cannot be greater than total', { cause: Cause.BAD_REQUEST });
  }

  if (totalDiscount > subtotal) {
    throw new Error('Total discount cannot be greater than subtotal', { cause: Cause.BAD_REQUEST });
  }

  const builtOrderItems: OrderItem[] = orderItems.map((item) => {
    if (item.quantity <= 0) {
      throw new Error('Quantity cannot be less than or equal to 0', { cause: Cause.BAD_REQUEST });
    }
    if (item.unitPrice < 0) {
      throw new Error('Unit price cannot be negative', { cause: Cause.BAD_REQUEST });
    }
    if (item.unitPriceAfterDiscount < 0) {
      throw new Error('Unit price after discount cannot be negative', { cause: Cause.BAD_REQUEST });
    }
    if (item.unitPriceAfterDiscountAndTaxes < 0) {
      throw new Error('Unit price after discount and taxes cannot be negative', { cause: Cause.BAD_REQUEST });
    }
    if (item.subtotal < 0) {
      throw new Error('Subtotal cannot be negative', { cause: Cause.BAD_REQUEST });
    }
    if (item.total < 0) {
      throw new Error('Total cannot be negative', { cause: Cause.BAD_REQUEST });
    }

    if (item.unitPriceAfterDiscount > item.unitPrice) {
      throw new Error('Unit price after discount cannot be greater than unit price', { cause: Cause.BAD_REQUEST });
    }

    return {
      name: item.name,
      productId: item.productId,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      unitPriceAfterDiscount: item.unitPriceAfterDiscount,
      unitPriceAfterDiscountAndTaxes: item.unitPriceAfterDiscountAndTaxes,
      subtotal: item.subtotal,
      total: item.total,
      discount: item.discount ? {
        value: item.discount.value,
        type: item.discount.type,
      } : null,
      taxes: item.taxes,
      createdAt: item.createdAt || new Date(),
      updatedAt: item.updatedAt || new Date(),
    };
  });

  return {
    id,
    readId,
    companyId,
    assignedUserId: assignedUserId || null,
    status,
    subtotalBeforeDiscount,
    totalDiscount,
    subtotal,
    totalTaxes,
    shippingPrice: shippingPrice || 0,
    total,
    shippedAt: shippedAt || null,
    reviewStartedAt: reviewStartedAt || null,
    shippingAddress: shippingAddress || null,
    notes: notes || null,
    deliveryDate: deliveryDate || null,
    createdAt: createdAt || new Date(),
    updatedAt: updatedAt || new Date(),
    orderItems: builtOrderItems,
    taxes,
  };
}

function updateStatus<T extends OrderStatus = OrderStatus>(order: OrderEntity, status: T): OrderEntity<T> {
  const orderToUpdate = { ...order, orderItems: order.orderItems.map((item) => ({ ...item })) };

  if (status === orderToUpdate.status) {
    throw new Error('New status is the same as the current status', { cause: Cause.BAD_REQUEST });
  }

  if (order.status === OrderStatus.COMPLETED) {
    throw new Error('Order is already completed', { cause: Cause.BAD_REQUEST });
  }

  if (order.status === OrderStatus.CANCELLED) {
    throw new Error('Order is cancelled', { cause: Cause.BAD_REQUEST });
  }

  const allowedNextStates = stateFlowMap[orderToUpdate.status as keyof typeof stateFlowMap];

  if (!allowedNextStates.includes(status)) {
    throw new Error(`Invalid status change from ${orderToUpdate.status} to ${status}`, { cause: Cause.BAD_REQUEST });
  }

  let shippedAt = orderToUpdate.shippedAt || null;
  if (status === OrderStatus.IN_TRANSIT && !shippedAt) {
    shippedAt = new Date();
  }

  let reviewStartedAt = status !== OrderStatus.IN_REVIEW ? null : orderToUpdate.reviewStartedAt || null;
  if (status === OrderStatus.IN_REVIEW) {
    reviewStartedAt = new Date();
  }

  return {
    ...orderToUpdate,
    shippedAt,
    reviewStartedAt,
    status,
  };
}

function update<T extends OrderStatus = OrderStatus>(order: OrderEntity<T>, propertiesToUpdate: OrderUpdateParams<T>): OrderEntity<T> {
  const {
    notes,
    assignedUserId,
    shippingAddress,
    deliveryDate,
  } = propertiesToUpdate;

  const newOrder = { ...order, orderItems: order.orderItems.map((item) => ({ ...item })) };

  if (notes === undefined && assignedUserId === undefined && shippingAddress === undefined && deliveryDate === undefined) {
    return newOrder;
  }

  if (order.assignedUserId && assignedUserId === null) {
    throw new Error('Assigned user can not be unassigned', { cause: Cause.BAD_REQUEST });
  }

  if (order.status === OrderStatus.COMPLETED || order.status === OrderStatus.CANCELLED) {
    if (deliveryDate) {
      throw new Error('Cannot modify delivery date for completed or cancelled orders', { cause: Cause.BAD_REQUEST });
    }

    if (shippingAddress) {
      throw new Error('Cannot modify shipping address for completed or cancelled orders', { cause: Cause.BAD_REQUEST });
    }

    if (assignedUserId) {
      throw new Error('Cannot modify assigned user for completed or cancelled orders', { cause: Cause.BAD_REQUEST });
    }
  }

  return {
    ...newOrder,
    notes: notes || order.notes,
    assignedUserId: assignedUserId || order.assignedUserId,
    shippingAddress: shippingAddress || order.shippingAddress,
    deliveryDate: deliveryDate || order.deliveryDate,
    updatedAt: new Date(),
  };
}

function availableStatuses(order: OrderEntity): OrderStatus[] {
  if (order.status === OrderStatus.COMPLETED || order.status === OrderStatus.CANCELLED) {
    return [];
  }

  return stateFlowMap[order.status];
}

export default {
  build,
  updateStatus,
  update,
  availableStatuses,
};
