import MediaEntity from '#domain/common/aggregates/media/MediaEntity';
import { Modify } from '#domain/common/Common.Type';

export type MediaBuildParams = Modify<MediaEntity, {
  createdAt?: Date;
  updatedAt?: Date;
  processing?: boolean;
}>;

function build(params: MediaBuildParams): MediaEntity {
  const {
    id,
    mediaType,
    url,
    processing = true,
    createdAt = new Date(),
    updatedAt = new Date(),
  } = params;

  return {
    id,
    mediaType,
    url,
    processing,
    createdAt,
    updatedAt,
  };
}

export default {
  build,
};
