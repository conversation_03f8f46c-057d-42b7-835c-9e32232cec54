import { MediaType } from '#domain/common/aggregates/media/MediaEntity';

import MediaOperator, { MediaBuildParams } from './Media.Operator';

jest.unmock('./Media.Operator');

describe('CatalogMediaOperator', () => {
  describe('build', () => {
    it('should build a catalog media object with default values', () => {
      const params: MediaBuildParams = {
        id: 'media1',
        mediaType: MediaType.IMAGE,
        url: 'http://example.com/image.jpg',
      };

      const result = MediaOperator.build(params);

      expect(result).toMatchObject({
        id: 'media1',
        mediaType: MediaType.IMAGE,
        url: 'http://example.com/image.jpg',
        processing: true,
      });
      expect(result.createdAt).toBeInstanceOf(Date);
    });

    it('should build a catalog media object with custom values', () => {
      const customDate = new Date('2021-01-01T00:00:00Z');
      const params: MediaBuildParams = {
        id: 'media2',
        mediaType: MediaType.VIDEO,
        url: 'http://example.com/video.mp4',
        processing: false,
        createdAt: customDate,
        updatedAt: customDate,
      };

      const result = MediaOperator.build(params);

      expect(result).toEqual({
        id: 'media2',
        mediaType: MediaType.VIDEO,
        url: 'http://example.com/video.mp4',
        processing: false,
        createdAt: customDate,
        updatedAt: customDate,
      });
    });
  });
});
