import Cause from '#composition/Cause.type';
import { DiscountEntity, DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import { Modify } from '#domain/common/Common.Type';
import DomainUtil from '#domain/common/Domain.Util';

export type DiscountEntityBuildParams = Modify<DiscountEntity, {
  startDate?: Date | null
  endDate?: Date | null
  disabledAt?: Date | null
  clientIds?: string[]
  createdAt?: Date
  updatedAt?: Date
}>;

function build(params: DiscountEntityBuildParams) : DiscountEntity {
  const {
    startDate = null,
    endDate = null,
    disabledAt = null,
    createdAt = new Date(),
    updatedAt = new Date(),
    clientIds = [],
  } = params;

  if (startDate && endDate && startDate > endDate) throw new Error('Start date must be before end date', { cause: Cause.BAD_REQUEST });

  return {
    ...params,
    startDate,
    endDate,
    disabledAt,
    createdAt,
    updatedAt,
    clientIds,
  };
}

function isDateValid(startDate: Date | null, endDate: Date | null): boolean {
  const now = new Date();
  return (!startDate || startDate <= now) && (!endDate || endDate >= now);
}

function hasClientAccess(discount: DiscountEntity, clientId: string): boolean {
  return discount.clientIds.includes(clientId);
}

function hasGlobalAccess(discount: DiscountEntity): boolean {
  return discount.clientIds.length === 0;
}

function isApplicable(discount: DiscountEntity, clientId: string): boolean {
  if (discount.disabledAt) return false;
  return (hasClientAccess(discount, clientId) || hasGlobalAccess(discount)) && isDateValid(discount.startDate, discount.endDate);
}

function calculatePriceAfterDiscount(discount: DiscountEntity, price: number): number {
  const { discountType, discountValue } = discount;
  let priceAfterDiscount = DomainUtil.toOperablePrice(price);
  const operablePrice = DomainUtil.toOperablePrice(price);

  if (discountType === DiscountType.PERCENTAGE) {
    priceAfterDiscount = operablePrice - operablePrice * (discountValue / 100);
  } else if (discountType === DiscountType.AMOUNT) {
    priceAfterDiscount = operablePrice - DomainUtil.toOperablePrice(discountValue);
  }

  return DomainUtil.toDisplayPrice(priceAfterDiscount);
}

function assignClients(catalogDiscount: DiscountEntity, clientIds: string[]): DiscountEntity {
  const { clientIds: oldClientIds } = catalogDiscount;

  const clientsAlreadyAssigned = clientIds.filter((clientId) => hasClientAccess(catalogDiscount, clientId));

  if (clientsAlreadyAssigned.length) {
    throw new Error(`Some clients are already assigned: ${clientsAlreadyAssigned.join(',')}`, { cause: Cause.BAD_REQUEST });
  }

  return {
    ...catalogDiscount,
    clientIds: [...oldClientIds, ...clientIds],
  };
}

function unassignClients(catalogDiscount: DiscountEntity, clientIds: string[]): DiscountEntity {
  const { clientIds: oldClientIds } = catalogDiscount;

  const clientsWithoutAccess = clientIds.filter((clientId) => !hasClientAccess(catalogDiscount, clientId));

  if (clientsWithoutAccess.length) {
    throw new Error(`Some clients do not have access to the discount: ${clientsWithoutAccess.join(',')}`, { cause: Cause.FORBIDDEN });
  }

  return {
    ...catalogDiscount,
    clientIds: oldClientIds.filter((clientId) => !clientIds.includes(clientId)),
  };
}

export default {
  build,
  isApplicable,
  hasClientAccess,
  hasGlobalAccess,
  calculatePriceAfterDiscount,
  assignClients,
  unassignClients,
};
