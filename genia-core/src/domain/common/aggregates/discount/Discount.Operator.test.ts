import { DiscountEntity, DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import DiscountOperator, { DiscountEntityBuildParams } from '#domain/common/aggregates/discount/Discount.Operator';

jest.unmock('#domain/common/aggregates/discount/Discount.Operator');
jest.unmock('#domain/common/Domain.Util');

describe('DiscountOperator', () => {
  describe('build', () => {
    it('should build a discount with default values', () => {
      const params: DiscountEntityBuildParams = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        companyId: 'comp_123',
      };

      const discount = DiscountOperator.build(params);

      expect(discount).toEqual({
        ...params,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: [],
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });

    it('should build a discount with provided values', () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-12-31');
      const params: DiscountEntityBuildParams = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.AMOUNT,
        discountValue: 100,
        startDate,
        endDate,
        clientIds: ['client1', 'client2'],
        companyId: 'comp_123',
      };

      const discount = DiscountOperator.build(params);

      expect(discount).toEqual({
        ...params,
        disabledAt: null,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });

    it('should throw error if start date is after end date', () => {
      const startDate = new Date('2023-12-31');
      const endDate = new Date('2023-01-01');
      const params: DiscountEntityBuildParams = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate,
        endDate,
        companyId: 'comp_123',
      };

      expect(() => DiscountOperator.build(params)).toThrow('Start date must be before end date');
    });
  });

  describe('hasClientAccess', () => {
    it('should return true when client has access', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: ['client1', 'client2'],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.hasClientAccess(discount, 'client1')).toBe(true);
    });

    it('should return false when client does not have access', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: ['client1', 'client2'],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.hasClientAccess(discount, 'client3')).toBe(false);
    });
  });

  describe('hasGlobalAccess', () => {
    it('should return true when discount has global access (empty clientIds)', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.hasGlobalAccess(discount)).toBe(true);
    });

    it('should return false when discount has specific client access', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: ['client1'],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.hasGlobalAccess(discount)).toBe(false);
    });
  });

  describe('isApplicable', () => {
    it('should return true when discount is applicable (global access, no dates)', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.isApplicable(discount, 'client1')).toBe(true);
    });

    it('should return true when discount is applicable (specific access, valid dates)', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 10);

      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10);

      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: pastDate,
        endDate: futureDate,
        disabledAt: null,
        clientIds: ['client1'],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.isApplicable(discount, 'client1')).toBe(true);
    });

    it('should return false when discount is disabled', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: null,
        endDate: null,
        disabledAt: new Date(),
        clientIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.isApplicable(discount, 'client1')).toBe(false);
    });

    it('should return false when client does not have access', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: ['client2'],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.isApplicable(discount, 'client1')).toBe(false);
    });

    it('should return false when discount is expired', () => {
      const pastDate1 = new Date();
      pastDate1.setDate(pastDate1.getDate() - 20);

      const pastDate2 = new Date();
      pastDate2.setDate(pastDate2.getDate() - 10);

      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: pastDate1,
        endDate: pastDate2,
        disabledAt: null,
        clientIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.isApplicable(discount, 'client1')).toBe(false);
    });

    it('should return false when discount is not yet active', () => {
      const futureDate1 = new Date();
      futureDate1.setDate(futureDate1.getDate() + 10);

      const futureDate2 = new Date();
      futureDate2.setDate(futureDate2.getDate() + 20);

      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: futureDate1,
        endDate: futureDate2,
        disabledAt: null,
        clientIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      expect(DiscountOperator.isApplicable(discount, 'client1')).toBe(false);
    });
  });

  describe('calculatePriceAfterDiscount', () => {
    it('should calculate correct price after percentage discount', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      const priceAfterDiscount = DiscountOperator.calculatePriceAfterDiscount(discount, 100);

      expect(priceAfterDiscount).toBe(90);
    });

    it('should calculate correct price after amount discount', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.AMOUNT,
        discountValue: 15,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      const priceAfterDiscount = DiscountOperator.calculatePriceAfterDiscount(discount, 100);

      expect(priceAfterDiscount).toBe(85);
    });

    it('should handle decimal values correctly for percentage discount', () => {
      const discount: DiscountEntity = {
        id: '1',
        name: 'Test Discount',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 33.33,
        startDate: null,
        endDate: null,
        disabledAt: null,
        clientIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        companyId: 'comp_123',
      };

      const priceAfterDiscount = DiscountOperator.calculatePriceAfterDiscount(discount, 100);

      expect(priceAfterDiscount).toBe(66.67);
    });
  });
});
