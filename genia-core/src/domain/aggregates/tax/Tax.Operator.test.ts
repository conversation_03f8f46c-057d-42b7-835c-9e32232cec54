import TaxEntity, { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import TaxOperator, { taxBuildParams } from '#domain/aggregates/tax/Tax.Operator';
import DomainUtil from '#domain/common/Domain.Util';

jest.unmock('./Tax.Operator');

describe('TaxOperator', () => {
  const mockDate = new Date('2023-01-01');

  beforeAll(() => {
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('build', () => {
    it('should build a tax object with default dates', () => {
      const params: taxBuildParams = {
        id: 'CAT_1234',
        name: 'rotor',
        value: 10,
        type: TaxType.AMOUNT,
        countryCode: 'CO',
      };

      const result = TaxOperator.build(params);

      expect(result).toEqual({
        ...params,
        createdAt: mockDate,
        updatedAt: mockDate,
        disabledAt: null,
      });
    });

    it('should use provided dates when available', () => {
      // Arrange
      const createdAt = new Date('2023-01-01');
      const updatedAt = new Date('2023-01-02');
      const disabledAt = new Date('2023-01-03');

      const params: taxBuildParams = {
        id: 'tax-123',
        name: 'VAT',
        type: TaxType.PERCENTAGE,
        value: 19,
        createdAt,
        updatedAt,
        disabledAt,
        countryCode: 'MX',
      };

      // Act
      const result = TaxOperator.build(params);

      // Assert
      expect(result).toEqual({
        ...params,
        createdAt,
        updatedAt,
        disabledAt,
      });
    });
  });

  describe('calculatePriceAfterTax', () => {
    it('should calculate price after percentage tax correctly', () => {
      // Arrange
      const tax: TaxEntity = {
        id: 'tax-123',
        name: 'VAT',
        type: TaxType.PERCENTAGE,
        value: 10,
        countryCode: 'MX',
        createdAt: new Date(),
        updatedAt: new Date(),
        disabledAt: null,
      };
      const price = 100;

      // Mock DomainUtil functions
      jest.spyOn(DomainUtil, 'toOperablePrice').mockImplementation((p) => p);
      jest.spyOn(DomainUtil, 'toDisplayPrice').mockImplementation((p) => p);

      // Act
      const result = TaxOperator.calculatePriceAfterTax(tax, price);

      // Assert
      expect(result).toBe(110); // 100 + (100 * 10/100)

      // Restore mocks
      jest.restoreAllMocks();
    });

    it('should calculate price after amount tax correctly', () => {
      // Arrange
      const tax: TaxEntity = {
        id: 'tax-123',
        name: 'Fixed Fee',
        type: TaxType.AMOUNT,
        value: 15,
        countryCode: 'MX',
        createdAt: new Date(),
        updatedAt: new Date(),
        disabledAt: null,
      };
      const price = 100;

      // Mock DomainUtil functions
      jest.spyOn(DomainUtil, 'toOperablePrice').mockImplementation((p) => p);
      jest.spyOn(DomainUtil, 'toDisplayPrice').mockImplementation((p) => p);

      // Act
      const result = TaxOperator.calculatePriceAfterTax(tax, price);

      // Assert
      expect(result).toBe(115); // 100 + 15

      // Restore mocks
      jest.restoreAllMocks();
    });
  });

  describe('calculateTaxAmount', () => {
    it('should calculate percentage tax amount correctly', () => {
      // Arrange
      const tax: TaxEntity = {
        id: 'tax-123',
        name: 'VAT',
        type: TaxType.PERCENTAGE,
        value: 10,
        countryCode: 'MX',
        createdAt: new Date(),
        updatedAt: new Date(),
        disabledAt: null,
      };
      const price = 100;

      // Mock DomainUtil functions
      jest.spyOn(DomainUtil, 'toOperablePrice').mockImplementation((p) => p);
      jest.spyOn(DomainUtil, 'toDisplayPrice').mockImplementation((p) => p);

      // Act
      const result = TaxOperator.calculateTaxAmount(tax, price);

      // Assert
      expect(result).toBe(10); // (100 * 10/100)

      // Restore mocks
      jest.restoreAllMocks();
    });

    it('should return tax value for amount tax type', () => {
      // Arrange
      const tax: TaxEntity = {
        id: 'tax-123',
        name: 'Fixed Fee',
        type: TaxType.AMOUNT,
        value: 15,
        countryCode: 'MX',
        createdAt: new Date(),
        updatedAt: new Date(),
        disabledAt: null,
      };
      const price = 100;

      // Act
      const result = TaxOperator.calculateTaxAmount(tax, price);

      // Assert
      expect(result).toBe(15); // Fixed amount
    });
  });
});
