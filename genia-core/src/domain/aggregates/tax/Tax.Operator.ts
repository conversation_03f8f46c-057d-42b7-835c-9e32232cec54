import DomainUtil from '#domain/common/Domain.Util';

import TaxObject from './Tax.Entity';

export type taxBuildParams = Omit<TaxObject, 'createdAt' |'updatedAt' | 'disabledAt'> & {createdAt?: Date; updatedAt?: Date, disabledAt?: Date | null};

function build(params: taxBuildParams): TaxObject {
  const createdAt = params.createdAt || new Date();
  const updatedAt = params.updatedAt || new Date();
  const disabledAt = params.disabledAt || null;

  return {
    ...params, createdAt, updatedAt, disabledAt,
  };
}

function calculatePriceAfterTax(tax: TaxObject, price: number): number {
  const operablePrice = DomainUtil.toOperablePrice(price);
  const operableTax = DomainUtil.toOperablePrice(tax.value);

  if (tax.type === 'percentage') {
    return DomainUtil.toDisplayPrice(operablePrice + (operablePrice * operableTax) / 100);
  }

  return DomainUtil.toDisplayPrice(operablePrice + operableTax);
}

function calculateTaxAmount(tax: TaxObject, price: number): number {
  const operablePrice = DomainUtil.toOperablePrice(price);

  if (tax.type === 'percentage') {
    return DomainUtil.toDisplayPrice((operablePrice * tax.value) / 100);
  }

  return tax.value;
}

export default {
  build,
  calculatePriceAfterTax,
  calculateTaxAmount,
};
