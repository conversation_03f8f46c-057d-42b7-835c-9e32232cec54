import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';

export default interface ClientEntity {
  id: string;
  name: string;
  tributaryId: string | null;
  clientCompanyId: string | null;
  companyId: string;
  storeDiscounts: string[];
  contactInformation: ContactInformationValueObject | null;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
}
