import ClientEntity from '#domain/aggregates/client/Client.Entity';
import ClientOperator, { clientBuildParams, clientUpdateParams } from '#domain/aggregates/client/Client.Operator';
import ContactInformationOperator from '#domain/common/aggregates/contactInformation/ContactInformation.Operator';
import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';

jest.unmock('#domain/aggregates/client/Client.Operator');

describe('ClientOperator', () => {
  describe('build', () => {
    it('Should return a client with default values', () => {
      const params: clientBuildParams = {
        id: 'cli_123',
        name: 'kavak',
        companyId: 'comp_123',
      };

      const expected: ClientEntity = {
        ...params,
        tributaryId: null,
        clientCompanyId: null,
        storeDiscounts: [],
        contactInformation: null,
        notes: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const got = ClientOperator.build(params);

      expect(got).toStrictEqual(expected);
    });

    it('Should return a client with custom values', () => {
      const contactInformation = {
        billingAddress: '123 Main St',
        billingEmail: '<EMAIL>',
        billingPhoneNumber: '*********0',
        billingWhatsapp: '*********0',
        purchasesEmail: null,
        purchasesPhoneNumber: null,
        purchasesWhatsapp: null,
        salesEmail: null,
        salesPhoneNumber: null,
        salesWhatsapp: null,
        shippingAddress: null,
      };

      (ContactInformationOperator.build as jest.Mock).mockReturnValue(contactInformation);

      const params: clientBuildParams = {
        id: 'cli_123',
        name: 'kavak',
        companyId: 'comp_123',
        tributaryId: '*********',
        clientCompanyId: 'comp_124',
        storeDiscounts: ['sd123'],
        contactInformation,
        notes: 'this client is important',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const got = ClientOperator.build(params);

      expect(got).toStrictEqual(params);
    });
  });

  describe('update', () => {
    const contactInformation: ContactInformationValueObject = {
      mainEmail: '<EMAIL>',
      mainPhoneNumber: '3126686868',
      mainWhatsapp: '3126686868',
      billingEmail: '<EMAIL>',
      billingPhoneNumber: '3126686868',
      billingWhatsapp: '3126686868',
      purchasesEmail: '<EMAIL>',
      purchasesPhoneNumber: '3126686868',
      purchasesWhatsapp: '3126686868',
      salesEmail: '<EMAIL>',
      salesPhoneNumber: '3126686868',
      salesWhatsapp: '3126686868',
      billingAddress: '123 Main St',
      shippingAddress: '123 Main St',
      mainAddress: '123 Main St',
      representativeName: 'John Doe',
    };

    it('should update a client correctly', () => {
      const client: ClientEntity = {
        id: 'cli_123',
        name: 'China Parts On Fire',
        tributaryId: '*********',
        clientCompanyId: null,
        companyId: 'comp_123',
        storeDiscounts: [],
        contactInformation: null,
        notes: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const params: clientUpdateParams = {
        storeDiscounts: ['sd_123'],
      };

      const updated = ClientOperator.update(client, params);

      expect(updated).toEqual({ ...client, ...params });
    });

    it('should work correctly when contact information is passed and previous one was null', () => {
      (ContactInformationOperator.build as jest.Mock).mockReturnValue(contactInformation);

      const client: ClientEntity = {
        id: 'cli_123',
        name: 'China Parts On Fire',
        tributaryId: '*********',
        clientCompanyId: null,
        companyId: 'comp_123',
        storeDiscounts: [],
        contactInformation: null,
        notes: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const params: clientUpdateParams = {
        contactInformation,
      };

      const updated = ClientOperator.update(client, params);

      expect(ContactInformationOperator.build).toHaveBeenCalledWith(contactInformation);
      expect(updated).toEqual({
        ...client,
        contactInformation,
      });
    });

    it('should leave previous values when new contact information is undefined', () => {
      const client: ClientEntity = {
        id: 'cli_123',
        name: 'China Parts On Fire',
        tributaryId: '*********',
        clientCompanyId: null,
        companyId: 'comp_123',
        storeDiscounts: [],
        contactInformation,
        notes: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const params: clientUpdateParams = { name: 'Updated Client Name' };

      const updated = ClientOperator.update(client, params);

      expect(updated).toEqual({
        ...client,
        ...params,
      });
    });

    it('should set new contact information to null', () => {
      const client: ClientEntity = {
        id: 'cli_123',
        name: 'China Parts On Fire',
        tributaryId: '*********',
        clientCompanyId: null,
        companyId: 'comp_123',
        storeDiscounts: [],
        contactInformation,
        notes: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const params: clientUpdateParams = { contactInformation: null };

      const updated = ClientOperator.update(client, params);

      expect(updated).toEqual({
        ...client,
        ...params,
      });
    });

    it('should update only new contact information', () => {
      (ContactInformationOperator.build as jest.Mock).mockReturnValue({ ...contactInformation, billingAddress: 'updated billing address' });

      const client: ClientEntity = {
        id: 'cli_123',
        name: 'China Parts On Fire',
        tributaryId: '*********',
        clientCompanyId: null,
        companyId: 'comp_123',
        storeDiscounts: [],
        contactInformation,
        notes: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const params: clientUpdateParams = { contactInformation: { billingAddress: 'updated billing address' } };

      const updated = ClientOperator.update(client, params);

      expect(updated).toEqual({
        ...client,
        contactInformation: {
          ...contactInformation,
          billingAddress: 'updated billing address',
        },
      });
    });
  });
});
