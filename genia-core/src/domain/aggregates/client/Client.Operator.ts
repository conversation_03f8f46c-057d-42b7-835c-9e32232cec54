import ClientEntity from '#domain/aggregates/client/Client.Entity';
import ContactInformationOperator, { ContactInformationBuildParams } from '#domain/common/aggregates/contactInformation/ContactInformation.Operator';
import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';
import { Modify } from '#domain/common/Common.Type';

export type contactInformationParams = Partial<ContactInformationValueObject>;

export type clientBuildParams = Modify<
ClientEntity,
{
  tributaryId?: string | null;
  clientCompanyId?: string | null;
  storeDiscounts?: string[];
  contactInformation?: contactInformationParams | null;
  notes?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
}
>;

export type clientUpdateParams = {
  name?: string;
  tributaryId?: string | null;
  storeDiscounts?: string[];
  contactInformation?: contactInformationParams | null;
  notes?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
};

function build(params: clientBuildParams): ClientEntity {
  const {
    id,
    name,
    companyId,
    tributaryId = null,
    clientCompanyId = null,
    storeDiscounts = [],
    contactInformation,
    notes = null,
    createdAt = new Date(),
    updatedAt = createdAt,
  } = params;

  const client: ClientEntity = {
    id,
    name,
    companyId,
    tributaryId,
    clientCompanyId,
    storeDiscounts,
    contactInformation: contactInformation ? ContactInformationOperator.build(contactInformation) : null,
    notes,
    createdAt,
    updatedAt,
  };

  return client;
}

function update(client: ClientEntity, params: clientUpdateParams): ClientEntity {
  const { contactInformation: newContactInformation, ...baseUpdates } = params;
  const updatedClient: ClientEntity = {
    ...client,
    ...baseUpdates,
    updatedAt: new Date(),
  };

  if (newContactInformation) {
    const oldContactInformation = client.contactInformation || {};

    const newContactInformationParams: ContactInformationBuildParams = {
      ...oldContactInformation,
      ...newContactInformation,
    };

    updatedClient.contactInformation = ContactInformationOperator.build(newContactInformationParams);
  }

  if (newContactInformation === null) updatedClient.contactInformation = null;

  return build(updatedClient);
}

export default {
  build,
  update,
};
