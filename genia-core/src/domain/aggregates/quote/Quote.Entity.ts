import OrderEntity, { OrderItem } from '#domain/common/aggregates/order/Order.Entity';

export interface QuoteItem extends OrderItem {
  catalogId: string;
}

export enum QuoteStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  APPROVED_BY_CLIENT = 'approved_by_client',
  CHANGES_REQUESTED = 'changes_requested',
  CLOSED = 'closed'
}

export interface QuoteProviderFields {
  relatedSaleOrderId: string | null;
  notes: string;
}

export interface QuoteClientFields {
  relatedPurchaseOrderId: string | null;
  notes: string;
}

export default interface QuoteEntity extends Omit<OrderEntity, 'notes' | 'status' | 'deliveryDate' | 'shippedAt' | 'reviewStartedAt' | 'orderItems'> {
  promisedDeliveryDate: Date | null;
  orderItems: QuoteItem[];
  clientId: string;
  status: QuoteStatus;
  providerFields: QuoteProviderFields | null;
  clientFields: QuoteClientFields | null;
}
