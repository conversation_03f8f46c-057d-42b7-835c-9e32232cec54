import QuoteEntity, { QuoteItem, QuoteStatus } from '#domain/aggregates/quote/Quote.Entity';
import { OrderStatus } from '#domain/common/aggregates/order/Order.Entity';
import OrderOperator, { OrderBuildParams, OrderItemBuildParams } from '#domain/common/aggregates/order/Order.Operator';
import { Modify } from '#domain/common/Common.Type';

export type QuoteItemBuildParams = Modify<OrderItemBuildParams, {
  catalogId: string;
}>;

export type QuoteBuildParams = Modify<Omit <OrderBuildParams, 'deliveryDate' | 'notes' | 'shippedAt' | 'reviewStartedAt' | 'assignedUserId'>, {
  clientId: string;
  orderItems: QuoteItemBuildParams[];
  status: QuoteStatus;
  promisedDeliveryDate?: Date | null;
  providerFields?: {
    relatedSaleOrderId: string | null;
    notes: string;
  } | null;
  clientFields?: {
    relatedPurchaseOrderId: string | null;
    notes: string;
  } | null;
  assignedUserId?: string | null;
}>;

function build(params: QuoteBuildParams): QuoteEntity {
  const { status: orderStatus, ...order } = OrderOperator.build({ ...params, status: OrderStatus.PENDING });

  const {
    clientId,
    status,
    orderItems,
    promisedDeliveryDate = null,
    providerFields = null,
    clientFields = null,
    createdAt,
    updatedAt,
  } = params;

  const builtOrderItems: QuoteItem[] = orderItems.map((item, i) => ({
    ...order.orderItems[i],
    catalogId: item.catalogId,
  }));

  return {
    clientId,
    ...order,
    status,
    promisedDeliveryDate,
    orderItems: builtOrderItems,
    providerFields,
    clientFields,
    createdAt: createdAt || new Date(),
    updatedAt: updatedAt || new Date(),
  };
}

export default {
  build,
};
