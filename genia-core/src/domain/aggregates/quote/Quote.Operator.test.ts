import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';

import { QuoteStatus } from './Quote.Entity';
import QuoteOperator, { QuoteBuildParams } from './Quote.Operator';

jest.unmock('./Quote.Operator');
jest.unmock('#domain/common/aggregates/order/Order.Operator');

describe('QuoteOperator.build', () => {
  const now = new Date();
  const baseParams: QuoteBuildParams = {
    id: 'q1',
    readId: 'Q-001',
    companyId: 'comp1',
    assignedUserId: 'user1',
    clientId: 'client1',
    status: QuoteStatus.DRAFT,
    promisedDeliveryDate: now,
    subtotal: 100,
    subtotalBeforeDiscount: 100,
    totalDiscount: 0,
    totalTaxes: 16,
    total: 116,
    shippingPrice: 0,
    shippingAddress: '123 Main St',
    taxes: [
      {
        name: 'VAT', value: 0.16, type: TaxType.PERCENTAGE, amount: 16,
      },
    ],
    createdAt: now,
    updatedAt: now,
    orderItems: [
      {
        catalogId: 'cat1',
        productId: 'prod1',
        name: 'Product 1',
        quantity: 2,
        unitPrice: 50,
        unitPriceAfterDiscount: 50,
        unitPriceAfterDiscountAndTaxes: 58,
        subtotal: 100,
        total: 116,
        discount: { value: 0, type: DiscountType.AMOUNT },
        taxes: [{
          name: 'VAT', value: 0.16, type: TaxType.PERCENTAGE, amount: 16,
        }],
        createdAt: now,
        updatedAt: now,
      },
    ],
    providerFields: {
      relatedSaleOrderId: null,
      notes: 'Provider notes',
    },
    clientFields: {
      relatedPurchaseOrderId: null,
      notes: 'Client notes',
    },
  };

  it('should build a valid QuoteEntity', () => {
    const quote = QuoteOperator.build(baseParams);

    expect(quote).toMatchObject({
      id: 'q1',
      readId: 'Q-001',
      companyId: 'comp1',
      assignedUserId: 'user1',
      clientId: 'client1',
      status: QuoteStatus.DRAFT,
      promisedDeliveryDate: now,
      subtotal: 100,
      subtotalBeforeDiscount: 100,
      totalDiscount: 0,
      totalTaxes: 16,
      total: 116,
      shippingPrice: 0,
      shippingAddress: '123 Main St',
      taxes: [
        {
          name: 'VAT', value: 0.16, type: TaxType.PERCENTAGE, amount: 16,
        },
      ],
      createdAt: now,
      updatedAt: now,
      providerFields: {
        relatedSaleOrderId: null,
        notes: 'Provider notes',
      },
      clientFields: {
        relatedPurchaseOrderId: null,
        notes: 'Client notes',
      },
    });

    expect(quote.orderItems.length).toBe(1);
    expect(quote.orderItems[0]).toMatchObject({
      catalogId: 'cat1',
      productId: 'prod1',
      name: 'Product 1',
      quantity: 2,
      unitPrice: 50,
      unitPriceAfterDiscount: 50,
      unitPriceAfterDiscountAndTaxes: 58,
      subtotal: 100,
      total: 116,
      discount: { value: 0, type: DiscountType.AMOUNT },
      taxes: [{
        name: 'VAT', value: 0.16, type: TaxType.PERCENTAGE, amount: 16,
      }],
      createdAt: now,
      updatedAt: now,
    });
  });

  it('should set default nulls for optional fields', () => {
    const params = {
      ...baseParams, promisedDeliveryDate: undefined, providerFields: undefined, clientFields: undefined,
    };
    const quote = QuoteOperator.build(params);

    expect(quote.promisedDeliveryDate).toBeNull();
    expect(quote.providerFields).toBeNull();
    expect(quote.clientFields).toBeNull();
  });

  it('should throw if orderItems is empty', () => {
    const params = { ...baseParams, orderItems: [] };
    expect(() => QuoteOperator.build(params)).toThrow('Order items cannot be empty');
  });

  it('should throw if subtotal is negative', () => {
    const params = { ...baseParams, subtotal: -1 };
    expect(() => QuoteOperator.build(params)).toThrow('Subtotal cannot be negative');
  });
});
