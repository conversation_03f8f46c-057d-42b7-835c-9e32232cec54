import Cause from '#composition/Cause.type';
import CatalogDiscountEntity from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';
import DiscountOperator, { DiscountEntityBuildParams } from '#domain/common/aggregates/discount/Discount.Operator';

export interface catalogDiscountBuildParams extends DiscountEntityBuildParams {
  requiredQuantity?: number;
  catalogIds?: string[];
}

export type catalogDiscountUpdateParams = Partial<Omit<CatalogDiscountEntity, 'id' | 'createdAt' |'updatedAt' | 'catalogIds' | 'clientIds' | 'companyId'>>;

function build(params: catalogDiscountBuildParams): CatalogDiscountEntity {
  const { disabledAt = new Date() } = params;
  const discount = DiscountOperator.build(params);
  const requiredQuantity = params.requiredQuantity || 0;
  const catalogIds = params.catalogIds || [];

  if (requiredQuantity < 0) throw new Error('Required amount must be greater than 0', { cause: Cause.BAD_REQUEST });

  if (catalogIds.length === 0 && (!disabledAt || disabledAt > new Date())) {
    throw new Error('Catalogs must be assigned to enabled discounts', { cause: Cause.BAD_REQUEST });
  }

  return {
    ...discount,
    requiredQuantity,
    catalogIds,
    disabledAt,
  };
}

function assignClients(catalogDiscount: CatalogDiscountEntity, clientIds: string[]): CatalogDiscountEntity {
  return DiscountOperator.assignClients(catalogDiscount, clientIds) as CatalogDiscountEntity;
}

function unassignClients(catalogDiscount: CatalogDiscountEntity, clientIds: string[]): CatalogDiscountEntity {
  return DiscountOperator.unassignClients(catalogDiscount, clientIds) as CatalogDiscountEntity;
}

function hasCatalogAccess(catalogDiscount: CatalogDiscountEntity, catalogId: string): boolean {
  return catalogDiscount.catalogIds.includes(catalogId);
}

function hasRequiredQuantity(discount: CatalogDiscountEntity, quantity: number): boolean {
  return discount.requiredQuantity <= quantity;
}

function assignCatalogs(catalogDiscount: CatalogDiscountEntity, catalogIds: string[]): CatalogDiscountEntity {
  const { catalogIds: oldCatalogIds } = catalogDiscount;

  const catalogsAlreadyAssigned = catalogIds.filter((catalogId) => hasCatalogAccess(catalogDiscount, catalogId));
  if (catalogsAlreadyAssigned.length) {
    throw new Error(`Some catalogs are already assigned: ${catalogsAlreadyAssigned.join(',')}`, { cause: Cause.BAD_REQUEST });
  }

  return {
    ...catalogDiscount,
    catalogIds: [...oldCatalogIds, ...catalogIds],
  };
}

function unassignCatalogs(catalogDiscount: CatalogDiscountEntity, catalogIds: string[]): CatalogDiscountEntity {
  const { catalogIds: oldCatalogIds } = catalogDiscount;

  const catalogsWithoutAccess = catalogIds.filter((catalogId) => !hasCatalogAccess(catalogDiscount, catalogId));
  if (catalogsWithoutAccess.length) {
    throw new Error(`Some catalogs are not assigned: ${catalogsWithoutAccess.join(',')}`, { cause: Cause.BAD_REQUEST });
  }

  return {
    ...catalogDiscount,
    catalogIds: oldCatalogIds.filter((catalogId) => !catalogIds.includes(catalogId)),
  };
}

function update(catalogDiscount: CatalogDiscountEntity, params: catalogDiscountUpdateParams): CatalogDiscountEntity {
  const updatedCatalogDiscount = { ...catalogDiscount, ...params };

  return build(updatedCatalogDiscount);
}

export default {
  ...DiscountOperator,
  build,
  update,
  assignClients,
  unassignClients,
  hasCatalogAccess,
  hasRequiredQuantity,
  assignCatalogs,
  unassignCatalogs,
};
