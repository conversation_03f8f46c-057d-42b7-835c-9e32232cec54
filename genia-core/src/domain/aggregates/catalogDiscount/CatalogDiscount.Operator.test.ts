import CatalogDiscountOperator, { catalogDiscountBuildParams } from '#domain/aggregates/catalogDiscount/CatalogDiscount.Operator';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';

jest.unmock('./CatalogDiscount.Operator');
jest.unmock('#domain/common/aggregates/discount/Discount.Operator');

describe('CatalogDiscountOperator', () => {
  const mockDate = new Date('2023-01-01');
  jest.useFakeTimers();
  jest.setSystemTime(mockDate);

  describe('build', () => {
    const defaultParams: catalogDiscountBuildParams = {
      id: 'cd_123',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      name: '10 USD',
      requiredQuantity: 0,
      startDate: new Date(),
      companyId: 'comp_123',
    };

    it('should build a CatalogDiscount correctly with default values', () => {
      const got = CatalogDiscountOperator.build(defaultParams);

      expect(got).toEqual({
        ...defaultParams, disabledAt: new Date(), endDate: null, clientIds: [], catalogIds: [], createdAt: new Date(), updatedAt: new Date(),
      });
    });

    it('should build a CatalogDiscount correctly with custom values', () => {
      const customParams = {
        disabledAt: null, endDate: new Date(), clientIds: ['client_123'], catalogIds: ['catalog_123'], createdAt: new Date(), updatedAt: new Date(),
      };

      const got = CatalogDiscountOperator.build({
        ...defaultParams, ...customParams,
      });

      expect(got).toEqual({
        ...defaultParams, ...customParams,
      });
    });

    it('should throw an error if required quantity is less than 0', () => {
      const params = {
        ...defaultParams, requiredQuantity: -1,
      };

      expect(() => CatalogDiscountOperator.build(params)).toThrow(new Error('Required amount must be greater than 0', { cause: 'BAD_REQUEST' }));
    });
  });

  describe('hasCatalogAccess', () => {
    const catalogDiscount = {
      id: 'cd_123',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      name: '10 USD',
      requiredQuantity: 0,
      startDate: new Date(),
      companyId: 'comp_123',
      disabledAt: null,
      endDate: null,
      clientIds: [],
      catalogIds: ['catalog_123', 'catalog_456'],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should return true if the catalog ID is included in the discount', () => {
      const result = CatalogDiscountOperator.hasCatalogAccess(catalogDiscount, 'catalog_123');
      expect(result).toBe(true);
    });

    it('should return false if the catalog ID is not included in the discount', () => {
      const result = CatalogDiscountOperator.hasCatalogAccess(catalogDiscount, 'catalog_789');
      expect(result).toBe(false);
    });
  });

  describe('hasRequiredQuantity', () => {
    it('should return true if the quantity is equal to the required quantity', () => {
      const catalogDiscount = {
        id: 'cd_123',
        discountValue: 10,
        discountType: DiscountType.AMOUNT,
        name: '10 USD',
        requiredQuantity: 5,
        startDate: new Date(),
        companyId: 'comp_123',
        disabledAt: null,
        endDate: null,
        clientIds: [],
        catalogIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = CatalogDiscountOperator.hasRequiredQuantity(catalogDiscount, 5);
      expect(result).toBe(true);
    });

    it('should return true if the quantity is greater than the required quantity', () => {
      const catalogDiscount = {
        id: 'cd_123',
        discountValue: 10,
        discountType: DiscountType.AMOUNT,
        name: '10 USD',
        requiredQuantity: 5,
        startDate: new Date(),
        companyId: 'comp_123',
        disabledAt: null,
        endDate: null,
        clientIds: [],
        catalogIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = CatalogDiscountOperator.hasRequiredQuantity(catalogDiscount, 10);
      expect(result).toBe(true);
    });

    it('should return false if the quantity is less than the required quantity', () => {
      const catalogDiscount = {
        id: 'cd_123',
        discountValue: 10,
        discountType: DiscountType.AMOUNT,
        name: '10 USD',
        requiredQuantity: 5,
        startDate: new Date(),
        companyId: 'comp_123',
        disabledAt: null,
        endDate: null,
        clientIds: [],
        catalogIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = CatalogDiscountOperator.hasRequiredQuantity(catalogDiscount, 3);
      expect(result).toBe(false);
    });
  });

  describe('assignClients', () => {
    const catalogDiscount = {
      id: 'cd_123',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      name: '10 USD',
      requiredQuantity: 0,
      startDate: new Date(),
      companyId: 'comp_123',
      disabledAt: null,
      endDate: null,
      clientIds: ['client1'],
      catalogIds: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should assign new clients to the catalog discount', () => {
      const updatedDiscount = CatalogDiscountOperator.assignClients(catalogDiscount, ['client2', 'client3']);
      expect(updatedDiscount.clientIds).toEqual(['client1', 'client2', 'client3']);
    });

    it('should throw an error if any client is already assigned', () => {
      expect(() => {
        CatalogDiscountOperator.assignClients(catalogDiscount, ['client1', 'client3']);
      }).toThrow('Some clients are already assigned: client1');
    });
  });

  describe('unassignClients', () => {
    const catalogDiscount = {
      id: 'cd_123',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      name: '10 USD',
      requiredQuantity: 0,
      startDate: new Date(),
      companyId: 'comp_123',
      disabledAt: null,
      endDate: null,
      clientIds: ['client1', 'client2', 'client3'],
      catalogIds: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should unassign specified clients from the catalog discount', () => {
      const updatedDiscount = CatalogDiscountOperator.unassignClients(catalogDiscount, ['client1', 'client3']);
      expect(updatedDiscount.clientIds).toEqual(['client2']);
    });

    it('should throw an error if any client does not have access', () => {
      expect(() => {
        CatalogDiscountOperator.unassignClients(catalogDiscount, ['client4']);
      }).toThrow('Some clients do not have access to the discount: client4');
    });
  });

  describe('assignCatalogs', () => {
    const catalogDiscount = {
      id: 'cd_123',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      name: '10 USD',
      requiredQuantity: 0,
      startDate: new Date(),
      companyId: 'comp_123',
      disabledAt: null,
      endDate: null,
      clientIds: [],
      catalogIds: ['catalog_1', 'catalog_2'],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should assign new catalogs successfully', () => {
      const newCatalogIds = ['catalog_3', 'catalog_4'];

      const updatedCatalogDiscount = CatalogDiscountOperator.assignCatalogs(catalogDiscount, newCatalogIds);

      expect(updatedCatalogDiscount.catalogIds).toEqual(['catalog_1', 'catalog_2', 'catalog_3', 'catalog_4']);
    });

    it('should throw an error if a new catalog is already assigned', () => {
      const newCatalogIds = ['catalog_2', 'catalog_3'];

      expect(() => CatalogDiscountOperator.assignCatalogs(catalogDiscount, newCatalogIds)).toThrowError(
        'Some catalogs are already assigned: catalog_2',
      );
    });
  });

  describe('unassignCatalogs', () => {
    const catalogDiscount = {
      id: 'cd_123',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      name: '10 USD',
      requiredQuantity: 0,
      startDate: new Date(),
      companyId: 'comp_123',
      disabledAt: null,
      endDate: null,
      clientIds: [],
      catalogIds: ['catalog_1', 'catalog_2', 'catalog_3'],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should unassign specified catalogs from the catalog discount', () => {
      const updatedCatalogDiscount = CatalogDiscountOperator.unassignCatalogs(catalogDiscount, ['catalog_1', 'catalog_3']);
      expect(updatedCatalogDiscount.catalogIds).toEqual(['catalog_2']);
    });

    it('should throw an error if any catalog is not assigned', () => {
      expect(() => {
        CatalogDiscountOperator.unassignCatalogs(catalogDiscount, ['catalog_4']);
      }).toThrow('Some catalogs are not assigned: catalog_4');
    });
  });

  describe('update', () => {
    const catalogDiscount = {
      id: 'cd_123',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      name: '10 USD',
      requiredQuantity: 0,
      startDate: new Date(),
      companyId: 'comp_123',
      disabledAt: null,
      endDate: null,
      clientIds: [],
      catalogIds: ['catalog_1', 'catalog_2'],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should update the catalog discount with valid parameters', () => {
      const updatedParams = {
        name: 'Updated Discount',
        discountValue: 20,
        requiredQuantity: 5,
      };

      const updatedCatalogDiscount = CatalogDiscountOperator.update(catalogDiscount, updatedParams);

      expect(updatedCatalogDiscount).toEqual({
        ...catalogDiscount,
        ...updatedParams,
        updatedAt: expect.any(Date),
      });
    });

    it('should throw an error if catalogs are not assigned to an enabled discount', () => {
      const invalidParams = {
        disabledAt: null,
        catalogIds: [],
      };

      expect(() => CatalogDiscountOperator.update(catalogDiscount, invalidParams)).toThrow(
        'Catalogs must be assigned to enabled discounts',
      );
    });

    it('should throw an error if catalogs are not assigned and disabledAt is in a future date', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10);

      const invalidParams = {
        disabledAt: futureDate,
        catalogIds: [],
      };

      expect(() => CatalogDiscountOperator.update(catalogDiscount, invalidParams)).toThrow(
        'Catalogs must be assigned to enabled discounts',
      );
    });
  });
});
