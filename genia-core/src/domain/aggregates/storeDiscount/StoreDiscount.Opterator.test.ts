import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import StoreDiscountOperator, { StoreDiscountBuildParams } from '#domain/aggregates/storeDiscount/StoreDiscount.Operator';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';

jest.unmock('#domain/aggregates/storeDiscount/StoreDiscount.Operator');
jest.unmock('#domain/common/aggregates/discount/Discount.Operator');

describe('StoreDiscountOperator', () => {
  describe('build', () => {
    it('should build a valid store discount with default values', () => {
      const params: StoreDiscountBuildParams = {
        id: 'sd_001',
        name: 'Summer Sale',
        discountValue: 20,
        companyId: 'comp_001',
        discountType: DiscountType.PERCENTAGE,
      };

      const discount = StoreDiscountOperator.build(params);
      expect(discount).toEqual({
        ...params,
        requiredAmount: 0,
        startDate: null,
        endDate: null,
        disabledAt: expect.any(Date),
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        clientIds: [],
      });
    });

    it('should build a valid store discount with custom values', () => {
      const now = new Date();
      const later = new Date(now.getTime() + 10000);
      const params = {
        id: 'sd_002',
        name: 'Winter Sale',
        discountValue: 15,
        companyId: 'comp_002',
        discountType: DiscountType.AMOUNT,
        requiredAmount: 5,
        startDate: now,
        endDate: later,
        disabledAt: null,
        createdAt: now,
        updatedAt: now,
        clientIds: ['client_001'],
      };

      const discount = StoreDiscountOperator.build(params);
      expect(discount).toEqual(params);
    });

    it('should throw an error if startDate is after endDate', () => {
      const now = new Date();
      const earlier = new Date(now.getTime() - 10000);
      const params = {
        id: 'sd_003',
        name: 'Invalid Date Discount',
        discountValue: 10,
        companyId: 'comp_003',
        discountType: DiscountType.AMOUNT,
        startDate: now,
        endDate: earlier,
      };

      expect(() => StoreDiscountOperator.build(params)).toThrow(/Start date must be before end date/);
    });

    it('should throw an error if requiredAmount is negative', () => {
      const params = {
        id: 'sd_004',
        name: 'Negative Required Amount',
        discountValue: 5,
        companyId: 'comp_004',
        discountType: DiscountType.AMOUNT,
        requiredAmount: -1,
      };

      expect(() => StoreDiscountOperator.build(params)).toThrow(/Required amount must be greater than 0/);
    });
  });

  describe('hasRequiredAmount', () => {
    it('should return true when amount is greater than required amount', () => {
      const discount = StoreDiscountOperator.build({
        id: 'sd_005',
        name: 'Minimum Purchase Discount',
        discountValue: 10,
        companyId: 'comp_005',
        discountType: DiscountType.PERCENTAGE,
        requiredAmount: 50,
      });

      expect(StoreDiscountOperator.hasRequiredAmount(discount, 100)).toBe(true);
    });

    it('should return true when amount is equal to required amount', () => {
      const discount = StoreDiscountOperator.build({
        id: 'sd_006',
        name: 'Exact Purchase Discount',
        discountValue: 10,
        companyId: 'comp_006',
        discountType: DiscountType.PERCENTAGE,
        requiredAmount: 75,
      });

      expect(StoreDiscountOperator.hasRequiredAmount(discount, 75)).toBe(true);
    });

    it('should return false when amount is less than required amount', () => {
      const discount = StoreDiscountOperator.build({
        id: 'sd_007',
        name: 'High Purchase Discount',
        discountValue: 15,
        companyId: 'comp_007',
        discountType: DiscountType.PERCENTAGE,
        requiredAmount: 100,
      });

      expect(StoreDiscountOperator.hasRequiredAmount(discount, 99)).toBe(false);
    });

    it('should return true when required amount is 0', () => {
      const discount = StoreDiscountOperator.build({
        id: 'sd_008',
        name: 'No Minimum Discount',
        discountValue: 5,
        companyId: 'comp_008',
        discountType: DiscountType.PERCENTAGE,
      });

      expect(StoreDiscountOperator.hasRequiredAmount(discount, 0)).toBe(true);
    });
  });

  describe('assignClients', () => {
    const storeDiscount: StoreDiscountEntity = {
      id: 'sd_001',
      name: 'Test Discount',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date(),
      endDate: null,
      disabledAt: null,
      companyId: 'comp_123',
      clientIds: ['client1'],
      requiredAmount: 100,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should assign new clients to the store discount', () => {
      const updatedDiscount = StoreDiscountOperator.assignClients(storeDiscount, ['client2', 'client3']);
      expect(updatedDiscount.clientIds).toEqual(['client1', 'client2', 'client3']);
    });

    it('should throw an error if any client is already assigned', () => {
      expect(() => {
        StoreDiscountOperator.assignClients(storeDiscount, ['client1', 'client3']);
      }).toThrow('Some clients are already assigned: client1');
    });
  });

  describe('unassignClients', () => {
    const storeDiscount = {
      id: 'cd_123',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      name: '10 USD',
      requiredAmount: 0,
      startDate: new Date(),
      companyId: 'comp_123',
      disabledAt: null,
      endDate: null,
      clientIds: ['client1', 'client2', 'client3'],
      storeIds: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should unassign specified clients from the store discount', () => {
      const updatedDiscount = StoreDiscountOperator.unassignClients(storeDiscount, ['client1', 'client3']);
      expect(updatedDiscount.clientIds).toEqual(['client2']);
    });

    it('should throw an error if any client does not have access', () => {
      expect(() => {
        StoreDiscountOperator.unassignClients(storeDiscount, ['client4']);
      }).toThrow('Some clients do not have access to the discount: client4');
    });
  });

  describe('update', () => {
    const storeDiscount: StoreDiscountEntity = {
      id: 'sd_001',
      name: 'Original Discount',
      discountValue: 10,
      discountType: DiscountType.AMOUNT,
      startDate: new Date('2023-01-01T00:00:00.000Z'),
      endDate: new Date('2023-12-31T23:59:59.999Z'),
      disabledAt: null,
      companyId: 'comp_123',
      clientIds: ['client1', 'client2'],
      requiredAmount: 100,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should update the store discount with valid parameters', () => {
      const updatedParams = {
        name: 'Updated Discount',
        discountValue: 20,
        requiredAmount: 200,
      };

      const updatedStoreDiscount = StoreDiscountOperator.update(storeDiscount, updatedParams);

      expect(updatedStoreDiscount).toEqual({
        ...storeDiscount,
        ...updatedParams,
        updatedAt: expect.any(Date),
      });
    });

    it('should throw an error if requiredAmount is negative', () => {
      const invalidParams = {
        requiredAmount: -50,
      };

      expect(() => StoreDiscountOperator.update(storeDiscount, invalidParams)).toThrow(
        'Required amount must be greater than 0',
      );
    });

    it('should throw an error if startDate is after endDate', () => {
      const invalidParams = {
        startDate: new Date('2023-12-31T23:59:59.999Z'),
        endDate: new Date('2023-01-01T00:00:00.000Z'),
      };

      expect(() => StoreDiscountOperator.update(storeDiscount, invalidParams)).toThrow(
        'Start date must be before end date',
      );
    });
  });
});
