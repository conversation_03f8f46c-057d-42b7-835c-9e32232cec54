import Cause from '#composition/Cause.type';
import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import DiscountOperator, { DiscountEntityBuildParams } from '#domain/common/aggregates/discount/Discount.Operator';

export type storeDiscountUpdateParams = Partial<Omit<StoreDiscountEntity, 'id' | 'createdAt' |'updatedAt' | 'clientIds' | 'companyId'>>;

export interface StoreDiscountBuildParams extends DiscountEntityBuildParams {
  requiredAmount?: number;
}

function build(params: StoreDiscountBuildParams) : StoreDiscountEntity {
  const { disabledAt = new Date() } = params;

  const discount = DiscountOperator.build(params);

  const {
    requiredAmount = 0,
  } = params;

  if (requiredAmount < 0) throw new Error('Required amount must be greater than 0', { cause: Cause.BAD_REQUEST });

  return {
    ...discount,
    requiredAmount,
    disabledAt,
  };
}

function update(storeDiscount: StoreDiscountEntity, params: storeDiscountUpdateParams): StoreDiscountEntity {
  const updatedStoreDiscount = { ...storeDiscount, ...params };

  return build(updatedStoreDiscount);
}

function hasRequiredAmount(discount: StoreDiscountEntity, amount: number): boolean {
  return discount.requiredAmount <= amount;
}

function assignClients(storeDiscount: StoreDiscountEntity, clientIds: string[]): StoreDiscountEntity {
  return DiscountOperator.assignClients(storeDiscount, clientIds) as StoreDiscountEntity;
}

function unassignClients(storeDiscount: StoreDiscountEntity, clientIds: string[]): StoreDiscountEntity {
  return DiscountOperator.unassignClients(storeDiscount, clientIds) as StoreDiscountEntity;
}

export default {
  ...DiscountOperator,
  build,
  hasRequiredAmount,
  assignClients,
  unassignClients,
  update,
};
