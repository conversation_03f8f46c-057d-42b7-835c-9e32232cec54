import { MediaType } from '#domain/common/aggregates/media/MediaEntity';

import CatalogMediaOperator, { CatalogMediaBuildParams } from './CatalogMedia.Operator';

jest.unmock('./CatalogMedia.Operator');
jest.unmock('#domain/common/aggregates/media/Media.Operator');

describe('CatalogMediaOperator', () => {
  describe('build', () => {
    it('should build a catalog media object with default values', () => {
      const params: CatalogMediaBuildParams = {
        id: 'media1',
        mediaType: MediaType.IMAGE,
        catalogId: 'cat1',
        url: 'http://example.com/image.jpg',
      };

      const result = CatalogMediaOperator.build(params);

      expect(result).toMatchObject({
        id: 'media1',
        mediaType: MediaType.IMAGE,
        catalogId: 'cat1',
        url: 'http://example.com/image.jpg',
        processing: true,
      });
      expect(result.createdAt).toBeInstanceOf(Date);
    });

    it('should build a catalog media object with custom values', () => {
      const customDate = new Date('2021-01-01T00:00:00Z');
      const params: CatalogMediaBuildParams = {
        id: 'media2',
        mediaType: MediaType.VIDEO,
        catalogId: 'cat2',
        url: 'http://example.com/video.mp4',
        processing: false,
        createdAt: customDate,
        updatedAt: customDate,
      };

      const result = CatalogMediaOperator.build(params);

      expect(result).toEqual({
        id: 'media2',
        mediaType: MediaType.VIDEO,
        catalogId: 'cat2',
        url: 'http://example.com/video.mp4',
        processing: false,
        createdAt: customDate,
        updatedAt: customDate,
      });
    });
  });

  describe('update', () => {
    it('should update fields of a catalog media object', () => {
      const original = CatalogMediaOperator.build({
        id: 'media3',
        mediaType: MediaType.IMAGE,
        catalogId: 'cat3',
        url: 'http://example.com',
        processing: true,
      });

      const updated = CatalogMediaOperator.update(original, { processing: false });

      expect(updated).toMatchObject({
        id: 'media3',
        mediaType: MediaType.IMAGE,
        catalogId: 'cat3',
        url: 'http://example.com',
        processing: false,
      });
      expect(updated.createdAt).toBeInstanceOf(Date);
      expect(updated.updatedAt).toBeInstanceOf(Date);
    });
  });
});
