import CatalogMediaEntity from '#domain/aggregates/catalogMedia/CatalogMedia.Entity';
import MediaOperator from '#domain/common/aggregates/media/Media.Operator';
import { Modify } from '#domain/common/Common.Type';

export type CatalogMediaBuildParams = Modify<CatalogMediaEntity, {
  createdAt?: Date;
  updatedAt?: Date;
  processing?: boolean;
}>;

export type CatalogMediaUpdateParams = {
  processing: boolean;
};

function build(params: CatalogMediaBuildParams): CatalogMediaEntity {
  const {
    id,
    mediaType,
    catalogId,
    url,
    processing = true,
    createdAt = new Date(),
    updatedAt = new Date(),
  } = params;

  const media = MediaOperator.build({
    id,
    mediaType,
    url,
    processing,
    createdAt,
    updatedAt,
  });

  return {
    ...media,
    catalogId,
  };
}

function update(CatalogMedia: CatalogMediaEntity, updates: CatalogMediaUpdateParams): CatalogMediaEntity {
  return build({
    ...CatalogMedia,
    ...updates,
  });
}

export default {
  build,
  update,
};
