import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import ProviderOperator, { providerBuildParams } from '#domain/aggregates/provider/Provider.Operator';
import ContactInformationOperator from '#domain/common/aggregates/contactInformation/ContactInformation.Operator';
import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';

jest.unmock('./Provider.Operator');

describe('ProviderOperator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('build', () => {
    it('Should build a provider correctly with provided vaules', () => {
      (ContactInformationOperator.build as jest.Mock).mockImplementation((ci) => ci);

      const params: providerBuildParams = {
        id: 'prov_123',
        name: 'China Parts On Fire',
        companyId: 'comp_123',
        tributaryId: '*********',
        providerCompanyId: 'comp_123',
        contactInformation: {
          billingAddress: 'billingAddress',
          mainAddress: 'mainAddress',
          representativeName: 'representativeName',
          shippingAddress: 'shippingAddress',
          billingEmail: 'billingEmail',
          billingPhoneNumber: 'billingPhoneNumber',
          billingWhatsapp: 'billingWhatsapp',
          mainEmail: 'mainEmail',
          mainPhoneNumber: 'mainPhoneNumber',
          mainWhatsapp: 'mainWhatsapp',
          purchasesEmail: 'purchasesEmail',
          purchasesPhoneNumber: 'purchasesPhoneNumber',
          purchasesWhatsapp: 'purchasesWhatsapp',
          salesEmail: 'salesEmail',
          salesPhoneNumber: 'salesPhoneNumber',
          salesWhatsapp: 'salesWhatsapp',
        },
      };

      const expected: ProviderEntity = {
        ...params,
        tributaryId: '*********',
        providerCompanyId: 'comp_123',
        contactInformation: {
          billingAddress: 'billingAddress',
          mainAddress: 'mainAddress',
          representativeName: 'representativeName',
          shippingAddress: 'shippingAddress',
          mainEmail: 'mainEmail',
          mainPhoneNumber: 'mainPhoneNumber',
          mainWhatsapp: 'mainWhatsapp',
          billingEmail: 'billingEmail',
          billingPhoneNumber: 'billingPhoneNumber',
          billingWhatsapp: 'billingWhatsapp',
          purchasesEmail: 'purchasesEmail',
          purchasesPhoneNumber: 'purchasesPhoneNumber',
          purchasesWhatsapp: 'purchasesWhatsapp',
          salesEmail: 'salesEmail',
          salesPhoneNumber: 'salesPhoneNumber',
          salesWhatsapp: 'salesWhatsapp',
        },
      };

      const got = ProviderOperator.build(params);

      expect(got).toStrictEqual(expected);
    });

    it('Should build a provider correctly with default vaules', () => {
      const params: providerBuildParams = {
        id: 'prov_123',
        name: 'China Parts On Fire',
        companyId: 'comp_123',
      };

      const expected: ProviderEntity = {
        ...params,
        tributaryId: null,
        providerCompanyId: null,
        contactInformation: null,
      };

      const got = ProviderOperator.build(params);

      expect(got).toStrictEqual(expected);
    });
  });

  describe('update', () => {
    const contactInformation: ContactInformationValueObject = {
      billingAddress: 'billingAddress',
      mainAddress: 'billingAddress',
      representativeName: 'representativeName',
      shippingAddress: 'shippingAddress',
      mainEmail: 'billingEmail',
      mainPhoneNumber: 'billingPhoneNumber',
      mainWhatsapp: 'billingWhatsapp',
      billingEmail: 'billingEmail',
      billingPhoneNumber: 'billingPhoneNumber',
      billingWhatsapp: 'billingWhatsapp',
      purchasesEmail: 'purchasesEmail',
      purchasesPhoneNumber: 'purchasesPhoneNumber',
      purchasesWhatsapp: 'purchasesWhatsapp',
      salesEmail: 'salesEmail',
      salesPhoneNumber: 'salesPhoneNumber',
      salesWhatsapp: 'salesWhatsapp',
    };

    it('should return the updated provider when there was no previous contact information', () => {
      (ContactInformationOperator.build as jest.Mock).mockImplementation((ci) => ci);
      const provider: ProviderEntity = {
        id: 'prov_123',
        name: 'China Parts On Fire',
        companyId: 'comp_123',
        tributaryId: '*********',
        providerCompanyId: 'comp_123',
        contactInformation: null,
      };

      const updates = {
        tributaryId: '*********',
        contactInformation,
      };

      const expected: ProviderEntity = {
        ...provider,
        ...updates,
      };

      const got = ProviderOperator.update(provider, updates);

      expect(got).toEqual(expected);
    });

    it('should return the updated provider with new contact information when there was previous contact information', () => {
      const provider: ProviderEntity = {
        id: 'prov_123',
        name: 'China Parts On Fire',
        companyId: 'comp_123',
        tributaryId: '*********',
        providerCompanyId: 'comp_123',
        contactInformation,
      };

      const updates = {
        contactInformation,
      };

      const expected: ProviderEntity = {
        ...provider,
        ...updates,
      };

      const got = ProviderOperator.update(provider, updates);

      expect(got).toEqual(expected);
    });

    it('should should set contact information to null correctly', () => {
      const provider: ProviderEntity = {
        id: 'prov_123',
        name: 'China Parts On Fire',
        companyId: 'comp_123',
        tributaryId: '*********',
        providerCompanyId: 'comp_123',
        contactInformation,
      };

      const updates = {
        contactInformation: null,
      };

      const expected: ProviderEntity = {
        ...provider,
        ...updates,
      };

      const got = ProviderOperator.update(provider, updates);

      expect(got).toEqual(expected);
    });
  });
});
