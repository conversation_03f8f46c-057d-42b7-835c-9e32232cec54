import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import ContactInformationOperator, { ContactInformationBuildParams } from '#domain/common/aggregates/contactInformation/ContactInformation.Operator';
import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';
import { Modify } from '#domain/common/Common.Type';

export type providerBuildParams = Modify<
ProviderEntity,
{
  tributaryId?: string | null;
  providerCompanyId?: string | null;
  contactInformation?: Partial<ContactInformationValueObject> | null;
}
>;

export type providerUpdateParams = Modify<Partial<Omit<ProviderEntity, 'id'>>, {
  contactInformation?: Partial<ContactInformationValueObject> | null;
}>;

function build(params: providerBuildParams): ProviderEntity {
  const { tributaryId = null, providerCompanyId = null, contactInformation } = params;

  return {
    ...params,
    tributaryId,
    providerCompanyId,
    contactInformation: contactInformation ? ContactInformationOperator.build(contactInformation) : null,
  };
}

function update(provider: ProviderEntity, toUpdate: providerUpdateParams): ProviderEntity {
  const { contactInformation: newContactInformation, ...baseUpdates } = toUpdate;

  const updatedProvider: ProviderEntity = {
    ...provider,
    ...baseUpdates,
  };

  if (newContactInformation) {
    const oldContactInformation = provider.contactInformation || {};

    const newContactInformationParams: ContactInformationBuildParams = {
      ...oldContactInformation,
      ...newContactInformation,
    };

    updatedProvider.contactInformation = ContactInformationOperator.build(newContactInformationParams);
  }

  if (newContactInformation === null) updatedProvider.contactInformation = null;

  return build(updatedProvider);
}

export default {
  build,
  update,
};
