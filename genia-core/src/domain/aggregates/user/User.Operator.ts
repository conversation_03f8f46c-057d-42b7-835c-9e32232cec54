import Cause from '#composition/Cause.type';
import UserEntity, { UserRole, UserState } from '#domain/aggregates/user/User.Entity';
import UserErrors from '#domain/aggregates/user/User.Errors';
import { Modify } from '#domain/common/Common.Type';

export type UserOperatorBuildParams = Modify<UserEntity, {
  role?: UserRole
  state?: UserState

}>;

export type UserOperatorUpdateParams = {
  role?: UserRole
  state?: UserState
  name?: string;
  lastName?: string;
  phoneNumber?: string;
};

function build(params: UserOperatorBuildParams) : UserEntity {
  const {
    id, email, companies, lastName, name, phoneNumber, state,
  } = params;

  if (companies.length === 0) throw new Error('User must have at least one company', { cause: Cause.BAD_REQUEST });

  return {
    id,
    email,
    name,
    lastName,
    companies,
    phoneNumber,
    state: state || UserState.ACTIVE,
    role: params.role || UserRole.USER,
  };
}

function addCompany(user: UserEntity, companyId: string): UserEntity {
  if (user.companies.includes(companyId)) throw new Error('User already belongs to this company', { cause: Cause.BAD_REQUEST });

  const modifiedUser = {
    ...user,
    companies: [...user.companies, companyId],
  };

  return modifiedUser;
}

function updateByUser(user: UserEntity, updates: UserOperatorUpdateParams, updaterUser: UserEntity): UserEntity {
  const {
    role = user.role, state = user.state, name = user.name, lastName = user.lastName, phoneNumber = user.phoneNumber,
  } = updates;

  const {
    role: currentRole, state: currentState, name: currentName, lastName: currentLastName, phoneNumber: currentPhoneNumber,
  } = user;

  const { role: updaterRole, id: updaterId } = updaterUser;

  if ((updaterRole !== UserRole.ADMIN) && (role !== currentRole || state !== currentState)) {
    throw UserErrors.FORBIDDEN_TO_REGULAR_USER;
  }

  if ((updaterId !== user.id) && (name !== currentName || lastName !== currentLastName || phoneNumber !== currentPhoneNumber)) {
    throw UserErrors.FORBIDDEN_TO_DIFFERENT_USER;
  }

  const updatedUser = {
    ...user,
    ...updates,
  };

  return updatedUser;
}

export default {
  build,
  updateByUser,
  addCompany,
};
