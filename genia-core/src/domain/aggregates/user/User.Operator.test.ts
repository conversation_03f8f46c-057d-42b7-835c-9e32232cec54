import UserEntity, { UserRole, UserState } from '#domain/aggregates/user/User.Entity';
import UserErrors from '#domain/aggregates/user/User.Errors';

import UserOperator, { UserOperatorBuildParams, UserOperatorUpdateParams } from './User.Operator';

jest.unmock('./User.Operator');
jest.unmock('#domain/aggregates/user/User.Errors');

describe('UserOperator', () => {
  describe('build', () => {
    it('should create a user with valid parameters and default role', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: '<PERSON>',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company1'],
      };

      const user = UserOperator.build(userParams);

      expect(user).toEqual({
        id: '123',
        email: '<EMAIL>',
        name: '<PERSON>',
        lastName: '<PERSON><PERSON>',
        phoneNumber: '1234567890',
        companies: ['company1'],
        state: UserState.ACTIVE, // Default state
        role: UserRole.USER, // Default role
      });
    });

    it('should create a user with specified role', () => {
      const userParams: UserOperatorBuildParams = {
        id: '456',
        email: '<EMAIL>',
        name: 'Jane',
        lastName: 'Smith',
        phoneNumber: '0987654321',
        companies: ['company1', 'company2'],
        role: UserRole.ADMIN,
        state: UserState.DISABLED,
      };

      const user = UserOperator.build(userParams);

      expect(user).toEqual({
        id: '456',
        email: '<EMAIL>',
        name: 'Jane',
        lastName: 'Smith',
        phoneNumber: '0987654321',
        companies: ['company1', 'company2'],
        role: UserRole.ADMIN,
        state: UserState.DISABLED,
      });
    });

    it('should throw error when creating user without companies', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: [],
      };

      expect(() => {
        UserOperator.build(userParams);
      }).toThrow('User must have at least one company');
    });
  });

  describe('addCompany', () => {
    it('should add a new company to user', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company1'],
      };

      const user = UserOperator.build(userParams);
      const updatedUser = UserOperator.addCompany(user, 'company2');

      expect(updatedUser.companies).toContain('company2');
      expect(updatedUser.companies.length).toBe(2);
      expect(updatedUser.companies).toEqual(['company1', 'company2']);
    });

    it('should throw error when adding duplicate company', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company1'],
      };

      const user = UserOperator.build(userParams);

      expect(() => {
        UserOperator.addCompany(user, 'company1');
      }).toThrow('User already belongs to this company');
    });

    it('should return a new user object without mutating the original', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company1'],
      };

      const user = UserOperator.build(userParams);
      const updatedUser = UserOperator.addCompany(user, 'company2');

      // Should return a different object reference
      expect(updatedUser).not.toBe(user);

      // Original user should have the same properties except companies array reference
      expect(user.id).toBe(updatedUser.id);
      expect(user.email).toBe(updatedUser.email);
      expect(user.name).toBe(updatedUser.name);
      expect(user.lastName).toBe(updatedUser.lastName);
      expect(user.phoneNumber).toBe(updatedUser.phoneNumber);
      expect(user.role).toBe(updatedUser.role);
    });
  });

  describe('updateByUser', () => {
    it('should throw an error if a regular user is updating role or state', () => {
      const user: UserEntity = {
        id: 'user-1',
        name: 'John',
        email: '<EMAIL>',
        lastName: 'Doe',
        phoneNumber: '3212121212',
        role: UserRole.USER,
        state: UserState.ACTIVE,
        companies: [],
      };

      const updater: UserEntity = {
        id: 'user-2',
        name: 'NotAdmin',
        lastName: 'Istrator',
        email: '<EMAIL>',
        phoneNumber: '000000000',
        role: UserRole.USER,
        state: UserState.ACTIVE,
        companies: [],
      };

      const updates: UserOperatorUpdateParams = {
        role: UserRole.ADMIN,
      };

      expect(() => {
        UserOperator.updateByUser(user, updates, updater);
      }).toThrow(UserErrors.FORBIDDEN_TO_REGULAR_USER.message);
    });

    it('should throw an error if not self user is updating basic properties', () => {
      const user: UserEntity = {
        id: 'user-1',
        name: 'John',
        email: '<EMAIL>',
        lastName: 'Doe',
        phoneNumber: '3212121212',
        role: UserRole.USER,
        state: UserState.ACTIVE,
        companies: [],
      };

      const updater: UserEntity = {
        id: 'user-2',
        name: 'NotAdmin',
        lastName: 'Istrator',
        email: '<EMAIL>',
        phoneNumber: '000000000',
        role: UserRole.ADMIN,
        state: UserState.ACTIVE,
        companies: [],
      };

      const updates: UserOperatorUpdateParams = {
        name: 'Judas',
      };

      expect(() => {
        UserOperator.updateByUser(user, updates, updater);
      }).toThrow(UserErrors.FORBIDDEN_TO_DIFFERENT_USER.message);
    });

    it('should update correctly role if updater is admin', () => {
      const user: UserEntity = {
        id: 'user-1',
        name: 'John',
        email: '<EMAIL>',
        lastName: 'Doe',
        phoneNumber: '3212121212',
        role: UserRole.USER,
        state: UserState.ACTIVE,
        companies: [],
      };

      const updater: UserEntity = {
        id: 'user-2',
        name: 'NotAdmin',
        lastName: 'Istrator',
        email: '<EMAIL>',
        phoneNumber: '000000000',
        role: UserRole.ADMIN,
        state: UserState.ACTIVE,
        companies: [],
      };

      const updates: UserOperatorUpdateParams = {
        role: UserRole.ADMIN,
        state: UserState.DISABLED,
      };

      const result = UserOperator.updateByUser(user, updates, updater);

      expect(result).toEqual({ ...user, ...updates });
    });

    it('should update correctly basic properties if the updater is the user', () => {
      const user: UserEntity = {
        id: 'user-1',
        name: 'John',
        email: '<EMAIL>',
        lastName: 'Doe',
        phoneNumber: '3212121212',
        role: UserRole.USER,
        state: UserState.ACTIVE,
        companies: [],
      };

      const updater:UserEntity = {
        id: 'user-1',
        name: 'John',
        email: '<EMAIL>',
        lastName: 'Doe',
        phoneNumber: '3212121212',
        role: UserRole.USER,
        state: UserState.ACTIVE,
        companies: [],
      };

      const updates: UserOperatorUpdateParams = {
        name: 'John Alex',
        lastName: 'Castano',
      };

      const result = UserOperator.updateByUser(user, updates, updater);

      expect(result).toEqual({ ...user, ...updates });
    });
  });
});
