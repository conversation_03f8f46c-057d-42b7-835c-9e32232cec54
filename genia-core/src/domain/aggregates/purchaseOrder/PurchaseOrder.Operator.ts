import Cause from '#composition/Cause.type';
import PurchaseOrderEntity, { PurchaseOrderItem, PurchaseOrderStatus } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Entity';
import { OrderStatus } from '#domain/common/aggregates/order/Order.Entity';
import OrderOperator, { OrderBuildParams, OrderItemBuildParams, OrderUpdateParams } from '#domain/common/aggregates/order/Order.Operator';
import { Modify } from '#domain/common/Common.Type';

export type PurchaseOrderItemBuildParams = Modify<OrderItemBuildParams, {
  id: string;
  referenceId: string;
  inventoryIds?: string[];
}>;

export type PurchaseOrderBuildParams = Modify<OrderBuildParams, {
  providerId: string;
  status: PurchaseOrderStatus;
  orderItems: PurchaseOrderItemBuildParams[];
  relatedSaleOrderId?: string | null;
}>;

function build(params: PurchaseOrderBuildParams): PurchaseOrderEntity {
  const order = OrderOperator.build(params);

  const {
    providerId,
    status,
    orderItems,
    relatedSaleOrderId = null,
  } = params;

  const builtOrderItems: PurchaseOrderItem[] = orderItems.map((item, i) => ({
    id: item.id,
    referenceId: item.referenceId,
    inventoryIds: item.inventoryIds || [],
    ...order.orderItems[i],
  }));

  return {
    providerId,
    ...order,
    status,
    orderItems: builtOrderItems,
    relatedSaleOrderId,
  };
}

function updateStatus(
  purchaseOrder: PurchaseOrderEntity,
  newStatus: PurchaseOrderStatus,
  modifierCompanyId: string,
): PurchaseOrderEntity {
  if (newStatus !== PurchaseOrderStatus.APPROVED_BY_CLIENT && purchaseOrder.relatedSaleOrderId && modifierCompanyId === purchaseOrder.companyId) {
    throw new Error('Cannot modify purchase order state for an order linked to a sale order', { cause: Cause.FORBIDDEN });
  }

  const orderToUpdate = {
    ...purchaseOrder,
    status: purchaseOrder.status as OrderStatus,
  };

  const updatedOrder = OrderOperator.updateStatus(orderToUpdate, newStatus);

  return {
    ...purchaseOrder,
    ...updatedOrder,
    orderItems: purchaseOrder.orderItems,
  };
}

function update(
  purchaseOrder: PurchaseOrderEntity,
  params: OrderUpdateParams<PurchaseOrderStatus>,
  modifierCompanyId: string,
): PurchaseOrderEntity {
  const {
    deliveryDate,
  } = params;

  const orderToUpdate = { ...purchaseOrder, orderItems: purchaseOrder.orderItems.map((item) => ({ ...item })) };

  const updatedOrder = OrderOperator.update<PurchaseOrderStatus>(orderToUpdate, params);

  if (purchaseOrder.relatedSaleOrderId && modifierCompanyId === purchaseOrder.companyId) {
    if (deliveryDate !== undefined) {
      throw new Error('Cannot modify delivery date for an order linked to a sale order', { cause: Cause.FORBIDDEN });
    }
  }

  return {
    ...purchaseOrder,
    ...updatedOrder,
    orderItems: purchaseOrder.orderItems,
  };
}

function availableStatuses(purchaseOrder: PurchaseOrderEntity): PurchaseOrderStatus[] {
  if (purchaseOrder.relatedSaleOrderId && purchaseOrder.status !== PurchaseOrderStatus.CLIENT_APPROVAL) {
    return [];
  }

  return OrderOperator.availableStatuses(purchaseOrder);
}

export default {
  build,
  updateStatus,
  update,
  availableStatuses,
};
