import Cause from '#composition/Cause.type';
import PurchaseOrderEntity, { PurchaseOrderStatus } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Entity';
import PurchaseOrderOperator, { PurchaseOrderBuildParams } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Operator';
import OrderOperator, { OrderBuildParams } from '#domain/common/aggregates/order/Order.Operator';

jest.unmock('./PurchaseOrder.Operator');

describe('PurchaseOrderOperator', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (OrderOperator.build as jest.Mock).mockImplementation((params: OrderBuildParams) => ({
      ...params,
      orderItems: params.orderItems.map((item) => ({
        ...item,
        discount: null,
        taxes: [],
      })),
      createdAt: new Date(),
      updatedAt: new Date(),
      reviewStartedAt: null,
      shippedAt: null,
      shippingAddress: null,
      shippingPrice: 0,
    }));

    (OrderOperator.updateStatus as jest.Mock).mockImplementation((order, status) => ({
      ...order,
      status,
      updatedAt: new Date(),
    }));

    (OrderOperator.update as jest.Mock).mockImplementation((order, params) => ({
      ...order,
      ...params,
      updatedAt: new Date(),
    }));

    // Add mock implementation for availableStatuses
    (OrderOperator.availableStatuses as jest.Mock).mockImplementation(() => [
      PurchaseOrderStatus.PENDING,
      PurchaseOrderStatus.PROCESSING,
      PurchaseOrderStatus.COMPLETED,
      PurchaseOrderStatus.CANCELLED,
    ]);
  });

  describe('build', () => {
    it('should build a PurchaseOrderEntity with valid params', () => {
      const params: PurchaseOrderBuildParams = {
        id: '1',
        readId: 'read-1',
        companyId: 'company-1',
        assignedUserId: 'user-1',
        status: PurchaseOrderStatus.PENDING,
        providerId: 'provider-1',
        subtotalBeforeDiscount: 100,
        subtotal: 100,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 100,
        notes: 'Test notes',
        shippingAddress: null,
        shippingPrice: 0,
        deliveryDate: null,
        reviewStartedAt: null,
        shippedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        relatedSaleOrderId: 'sale-order-1',
        orderItems: [
          {
            id: 'poi-1',
            referenceId: 'item-1',
            name: 'Item 1',
            productId: 'product-1',
            quantity: 2,
            unitPrice: 50,
            discount: null,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 50,
            total: 100,
            subtotal: 100,
            taxes: [],
          },
        ],
        taxes: [],
      };

      const purchaseOrder = PurchaseOrderOperator.build(params);

      expect(purchaseOrder).toEqual({
        ...params,
        orderItems: params.orderItems.map((item) => ({
          ...item,
          inventoryIds: [],
          referenceId: item.referenceId,
        })),
      });
    });
  });

  it('should build a PurchaseOrderEntity with default values', () => {
    const params: PurchaseOrderBuildParams = {
      id: '1',
      readId: 'read-1',
      companyId: 'company-1',
      assignedUserId: 'user-1',
      status: PurchaseOrderStatus.PENDING,
      providerId: 'provider-1',
      subtotalBeforeDiscount: 100,
      subtotal: 100,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 100,
      notes: null,
      shippingAddress: null,
      shippingPrice: 0,
      deliveryDate: null,
      reviewStartedAt: null,
      shippedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      orderItems: [],
      taxes: [],
    };

    const purchaseOrder = PurchaseOrderOperator.build(params);

    expect(purchaseOrder).toEqual({
      ...params,
      orderItems: [],
      relatedSaleOrderId: null,
    });
  });

  describe('updateStatus', () => {
    const mockPurchaseOrder: PurchaseOrderEntity = {
      id: '1',
      readId: 'read-1',
      companyId: 'company-1',
      assignedUserId: 'user-1',
      status: PurchaseOrderStatus.PENDING,
      providerId: 'provider-1',
      subtotalBeforeDiscount: 100,
      subtotal: 100,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 100,
      notes: 'Test notes',
      shippingAddress: null,
      shippingPrice: 0,
      deliveryDate: null,
      reviewStartedAt: null,
      shippedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      relatedSaleOrderId: null,
      orderItems: [
        {
          id: 'poi-1',
          referenceId: 'item-1',
          name: 'Item 1',
          productId: 'product-1',
          quantity: 2,
          unitPrice: 50,
          discount: null,
          unitPriceAfterDiscount: 50,
          unitPriceAfterDiscountAndTaxes: 50,
          total: 100,
          subtotal: 100,
          taxes: [],
          inventoryIds: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      taxes: [],
    };

    it('should update the status of a purchase order', () => {
      const updatedOrder = PurchaseOrderOperator.updateStatus(
        mockPurchaseOrder,
        PurchaseOrderStatus.PROCESSING,
        'company-1',
      );

      expect(updatedOrder.status).toBe(PurchaseOrderStatus.PROCESSING);
      expect(OrderOperator.updateStatus).toHaveBeenCalledWith(
        expect.objectContaining({ id: '1' }),
        PurchaseOrderStatus.PROCESSING,
      );
    });

    it('should throw an error when trying to update a purchase order linked to a sale order', () => {
      const linkedOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-1',
      };

      expect(() => {
        PurchaseOrderOperator.updateStatus(
          linkedOrder,
          PurchaseOrderStatus.CANCELLED,
          'company-1',
        );
      }).toThrow(new Error('Cannot modify purchase order state for an order linked to a sale order'));
    });

    it('should allow APPROVED_BY_CLIENT status change even for linked orders', () => {
      const linkedOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-1',
      };

      const updatedOrder = PurchaseOrderOperator.updateStatus(
        linkedOrder,
        PurchaseOrderStatus.APPROVED_BY_CLIENT,
        'company-1',
      );

      expect(updatedOrder.status).toBe(PurchaseOrderStatus.APPROVED_BY_CLIENT);
    });

    it('should allow status changes for linked orders if modifier is not the company', () => {
      const linkedOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-1',
      };

      const updatedOrder = PurchaseOrderOperator.updateStatus(
        linkedOrder,
        PurchaseOrderStatus.CANCELLED,
        'different-company-id',
      );

      expect(updatedOrder.status).toBe(PurchaseOrderStatus.CANCELLED);
    });
  });

  describe('update', () => {
    const mockPurchaseOrder: PurchaseOrderEntity = {
      id: '1',
      readId: 'read-1',
      companyId: 'company-1',
      assignedUserId: 'user-1',
      status: PurchaseOrderStatus.PENDING,
      providerId: 'provider-1',
      subtotalBeforeDiscount: 100,
      subtotal: 100,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 100,
      notes: 'Test notes',
      shippingAddress: null,
      shippingPrice: 0,
      deliveryDate: null,
      reviewStartedAt: null,
      shippedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      relatedSaleOrderId: null,
      orderItems: [
        {
          id: 'poi-1',
          referenceId: 'item-1',
          name: 'Item 1',
          productId: 'product-1',
          quantity: 2,
          unitPrice: 50,
          discount: null,
          unitPriceAfterDiscount: 50,
          unitPriceAfterDiscountAndTaxes: 50,
          total: 100,
          subtotal: 100,
          taxes: [],
          inventoryIds: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      taxes: [],
    };

    it('should update a purchase order with valid params', () => {
      const newDeliveryDate = new Date();
      const updatedOrder = PurchaseOrderOperator.update(
        mockPurchaseOrder,
        {
          notes: 'Updated notes',
          deliveryDate: newDeliveryDate,
        },
        'company-1',
      );

      expect(updatedOrder.notes).toBe('Updated notes');
      expect(updatedOrder.deliveryDate).toBe(newDeliveryDate);
      expect(OrderOperator.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          notes: 'Updated notes',
          deliveryDate: newDeliveryDate,
        }),
      );
    });

    it('should throw an error when trying to update delivery date for an order linked to a sale order', () => {
      const linkedOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-1',
      };

      expect(() => {
        PurchaseOrderOperator.update(
          linkedOrder,
          {
            deliveryDate: new Date(),
          },
          'company-1',
        );
      }).toThrow(new Error('Cannot modify delivery date for an order linked to a sale order'));
      expect(() => {
        PurchaseOrderOperator.update(
          linkedOrder,
          {
            deliveryDate: new Date(),
          },
          'company-1',
        );
      }).toThrow(expect.objectContaining({ cause: Cause.FORBIDDEN }));
    });

    it('should allow updating delivery date for a linked order if modifier is not from the same company', () => {
      const linkedOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-1',
      };

      const newDeliveryDate = new Date();
      const updatedOrder = PurchaseOrderOperator.update(
        linkedOrder,
        {
          deliveryDate: newDeliveryDate,
        },
        'different-company-id',
      );

      expect(updatedOrder.deliveryDate).toBe(newDeliveryDate);
    });

    it('should allow updating other fields for a linked order', () => {
      const linkedOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-1',
      };

      const updatedOrder = PurchaseOrderOperator.update(
        linkedOrder,
        {
          notes: 'Updated notes',
        },
        'company-1',
      );

      expect(updatedOrder.notes).toBe('Updated notes');
    });
  });

  describe('availableStatuses', () => {
    const mockPurchaseOrder: PurchaseOrderEntity = {
      id: '1',
      readId: 'read-1',
      companyId: 'company-1',
      assignedUserId: 'user-1',
      status: PurchaseOrderStatus.PENDING,
      providerId: 'provider-1',
      subtotalBeforeDiscount: 100,
      subtotal: 100,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 100,
      notes: 'Test notes',
      shippingAddress: null,
      shippingPrice: 0,
      deliveryDate: null,
      reviewStartedAt: null,
      shippedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      relatedSaleOrderId: null,
      orderItems: [
        {
          id: 'poi-1',
          referenceId: 'item-1',
          name: 'Item 1',
          productId: 'product-1',
          quantity: 2,
          unitPrice: 50,
          discount: null,
          unitPriceAfterDiscount: 50,
          unitPriceAfterDiscountAndTaxes: 50,
          total: 100,
          subtotal: 100,
          taxes: [],
          inventoryIds: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      taxes: [],
    };

    it('should return available statuses for a regular purchase order', () => {
      const availableStatuses = PurchaseOrderOperator.availableStatuses(mockPurchaseOrder);

      expect(OrderOperator.availableStatuses).toHaveBeenCalledWith(mockPurchaseOrder);
      expect(availableStatuses).toEqual([
        PurchaseOrderStatus.PENDING,
        PurchaseOrderStatus.PROCESSING,
        PurchaseOrderStatus.COMPLETED,
        PurchaseOrderStatus.CANCELLED,
      ]);
    });

    it('should return empty array for a purchase order linked to a sale order', () => {
      const linkedOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-1',
      };

      const availableStatuses = PurchaseOrderOperator.availableStatuses(linkedOrder);

      expect(availableStatuses).toEqual([]);
      expect(OrderOperator.availableStatuses).not.toHaveBeenCalled();
    });

    it('should return available statuses for a linked order with CLIENT_APPROVAL status', () => {
      const linkedOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-1',
        status: PurchaseOrderStatus.CLIENT_APPROVAL,
      };

      const availableStatuses = PurchaseOrderOperator.availableStatuses(linkedOrder);

      expect(OrderOperator.availableStatuses).toHaveBeenCalledWith(linkedOrder);
      expect(availableStatuses).toEqual([
        PurchaseOrderStatus.PENDING,
        PurchaseOrderStatus.PROCESSING,
        PurchaseOrderStatus.COMPLETED,
        PurchaseOrderStatus.CANCELLED,
      ]);
    });
  });
});
