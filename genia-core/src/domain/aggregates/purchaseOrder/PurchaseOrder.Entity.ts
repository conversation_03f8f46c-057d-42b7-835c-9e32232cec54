import OrderEntity, { OrderItem, OrderStatus } from '#domain/common/aggregates/order/Order.Entity';

export type PurchaseOrderStatus = OrderStatus;
export const PurchaseOrderStatus = {
  ...OrderStatus,
};

export interface PurchaseOrderItem extends OrderItem {
  id: string,
  referenceId: string,
  inventoryIds: string[],
}

export default interface PurchaseOrderEntity extends OrderEntity{
  providerId: string;
  orderItems: PurchaseOrderItem[];
  status: PurchaseOrderStatus,
  relatedSaleOrderId: string | null;
}
