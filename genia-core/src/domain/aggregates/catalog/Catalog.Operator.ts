import CatalogObject, { CatalogType, InventoryRelation } from '#domain/aggregates/catalog/Catalog.Entity';
import { AttributeType, Modify } from '#domain/common/Common.Type';

export type catalogBuildParams = Modify<
CatalogObject,
{
  disabledAt?: Date | null;
  description?: string;
  discountIds?: string[];
  inventoryRelations?: InventoryRelation[];
  taxIds?: string[];
  requiresStock?: boolean;
  attributes?: AttributeType[] | null;
  createdAt?: Date;
  updatedAt?: Date;
  mediaIds?: string[];
}
>;

function build(params: catalogBuildParams): CatalogObject {
  const disabledAt = params.disabledAt || null;
  const requiresStock = params.requiresStock || false;
  const description = params.description || '';
  const discountIds = params.discountIds || [];
  const taxIds = params.taxIds || [];
  const inventoryRelations = params.inventoryRelations || [];
  const attributes = params.attributes || [];
  const createdAt = params.createdAt || new Date();
  const updatedAt = params.updatedAt || new Date();
  const mediaIds = params.mediaIds || [];

  return {
    ...params,
    requiresStock,
    description,
    discountIds,
    taxIds,
    inventoryRelations,
    attributes,
    disabledAt,
    createdAt,
    updatedAt,
    mediaIds,
  };
}

export type catalogUpdateParams = {
  readId?: string;
  name?: string;
  description?: string;
  price?: number;
  requiresStock?: boolean;
  attributes?: AttributeType[];
  type?: CatalogType;
  discountIds?: string[];
  taxIds?: string[];
  inventoryRelations?: InventoryRelation[];
  disabledAt?: Date | null;
  mediaIds?: string[];
};

function update(catalog: CatalogObject, updates: catalogUpdateParams): CatalogObject {
  return build({
    id: catalog.id,
    readId: updates.readId ?? catalog.readId,
    name: updates.name ?? catalog.name,
    description: updates.description ?? catalog.description,
    price: updates.price ?? catalog.price,
    requiresStock: updates.requiresStock ?? catalog.requiresStock,
    type: updates.type ?? catalog.type,
    companyId: catalog.companyId,
    attributes: updates.attributes ?? catalog.attributes,
    discountIds: updates.discountIds ?? catalog.discountIds,
    taxIds: updates.taxIds ?? catalog.taxIds,
    inventoryRelations: updates.inventoryRelations ?? catalog.inventoryRelations,
    disabledAt: updates.disabledAt,
    createdAt: catalog.createdAt,
    updatedAt: catalog.updatedAt,
    mediaIds: updates.mediaIds ?? catalog.mediaIds,
  });
}

export default {
  build,
  update,
};
