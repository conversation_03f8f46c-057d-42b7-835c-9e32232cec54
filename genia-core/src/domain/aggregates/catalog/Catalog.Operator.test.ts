import CatalogEntity, { CatalogType } from '#domain/aggregates/catalog/Catalog.Entity';
import CatalogOperator, { catalogBuildParams, catalogUpdateParams } from '#domain/aggregates/catalog/Catalog.Operator';

jest.unmock('./Catalog.Operator');

describe('CatalogOperator', () => {
  const catalog: CatalogEntity = {
    id: '1234',
    readId: 'CAT_1234',
    name: 'rotor',
    description: '',
    requiresStock: false,
    price: 10,
    type: CatalogType.PRODUCT,
    companyId: 'COMP_1234',
    attributes: [],
    discountIds: [],
    taxIds: [],
    inventoryRelations: [],
    mediaIds: [],
    disabledAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  describe('build', () => {
    it('should build a catalog object with default values', () => {
      const params: catalogBuildParams = {
        id: '1234',
        readId: 'CAT_1234',
        name: 'rotor',
        price: 10,
        type: CatalogType.PRODUCT,
        companyId: 'COMP_1234',
        attributes: null,
      };

      const result = CatalogOperator.build(params);

      expect(result).toEqual({
        ...params,
        description: '',
        requiresStock: false,
        attributes: [],
        discountIds: [],
        taxIds: [],
        inventoryRelations: [],
        mediaIds: [],
        disabledAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    });

    it('should build a catalog object with custom values', () => {
      const params: catalogBuildParams = {
        id: '1234',
        readId: 'CAT_1234',
        name: 'rotor',
        price: 10,
        type: CatalogType.PRODUCT,
        companyId: 'COMP_1234',
        description: 'this rotor is the best',
        requiresStock: true,
        attributes: [
          { key: 'color', values: ['red'] },
          { key: 'size', categories: [{ key: 'model', values: ['2012'] }] },
        ],
        disabledAt: new Date(),
        discountIds: ['DISC_1234'],
        taxIds: ['TAX_1234'],
        inventoryRelations: [{ inventoryId: 'INV_1234', quantity: 10 }],
        mediaIds: ['MEDIA_1', 'MEDIA_2'],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = CatalogOperator.build(params);

      expect(result).toEqual(params);
    });
  });

  describe('update', () => {
    it('should update provided properties but preserve id', () => {
      const originalId = catalog.id;
      const updates: catalogUpdateParams = {
        discountIds: [],
        taxIds: ['tax_123'],
        mediaIds: ['media_updated'],
      };

      const updated = CatalogOperator.update(catalog, updates);

      expect(updated.id).toBe(originalId); // ID should not change
      expect(updated.discountIds).toEqual(updates.discountIds);
      expect(updated.taxIds).toEqual(updates.taxIds);
      expect(updated.mediaIds).toEqual(updates.mediaIds);
    });

    it('should ignore any attempt to update the id', () => {
      const originalId = catalog.id;
      const updates = {
        id: 'new-id-attempt', // This should be ignored
        name: 'Updated Name',
      };

      const updated = CatalogOperator.update(catalog, updates);

      expect(updated.id).toBe(originalId); // ID should not change
      expect(updated.name).toBe(updates.name); // Other fields should update
    });
  });
});
