import { AttributeType } from '#domain/common/Common.Type';

export enum CatalogType {
  SERVICE = 'service',
  PRODUCT = 'product',
  BUNDLE = 'bundle',
}

export interface InventoryRelation {
  inventoryId: string;
  quantity: number;
}

export default interface CatalogEntity {
  id: string;
  readId: string;
  name: string;
  description: string;
  price: number;
  requiresStock: boolean;
  attributes: AttributeType[];
  type: CatalogType;
  discountIds: string[];
  taxIds: string[];
  mediaIds: string[];
  inventoryRelations: InventoryRelation[];
  companyId: string;
  disabledAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}
