import { InventoryMediaSize } from '#domain/aggregates/inventoryMedia/InventoryMedia.Entity';
import { MediaType } from '#domain/common/aggregates/media/MediaEntity';

import InventoryMediaOperator, { InventoryMediaBuildParams } from './InventoryMedia.Operator';

jest.unmock('./InventoryMedia.Operator');
jest.unmock('#domain/common/aggregates/media/Media.Operator');

describe('InventoryMediaOperator', () => {
  describe('build', () => {
    it('should build a inventory media object with default values', () => {
      const params: InventoryMediaBuildParams = {
        id: 'media1',
        mediaType: MediaType.IMAGE,
        inventoryId: 'inv1',
        url: 'http://example.com/image.jpg',
      };

      const result = InventoryMediaOperator.build(params);

      expect(result).toMatchObject({
        id: 'media1',
        mediaType: MediaType.IMAGE,
        inventoryId: 'inv1',
        url: 'http://example.com/image.jpg',
        size: InventoryMediaSize.MEDIUM,
        processing: true,
      });
      expect(result.createdAt).toBeInstanceOf(Date);
    });

    it('should build a inventory media object with custom values', () => {
      const customDate = new Date('2021-01-01T00:00:00Z');
      const params: InventoryMediaBuildParams = {
        id: 'media2',
        mediaType: MediaType.VIDEO,
        inventoryId: 'cat2',
        url: 'http://example.com/video.mp4',
        size: InventoryMediaSize.LARGE,
        processing: false,
        createdAt: customDate,
        updatedAt: customDate,
      };

      const result = InventoryMediaOperator.build(params);

      expect(result).toEqual({
        id: 'media2',
        mediaType: MediaType.VIDEO,
        inventoryId: 'cat2',
        size: InventoryMediaSize.LARGE,
        url: 'http://example.com/video.mp4',
        processing: false,
        createdAt: customDate,
        updatedAt: customDate,
      });
    });
  });

  describe('update', () => {
    it('should update fields of a catalog media object', () => {
      const original = InventoryMediaOperator.build({
        id: 'media3',
        mediaType: MediaType.IMAGE,
        inventoryId: 'inv3',
        url: 'http://example.com',
        processing: true,
      });

      const updated = InventoryMediaOperator.update(original, { processing: false });

      expect(updated).toMatchObject({
        id: 'media3',
        mediaType: MediaType.IMAGE,
        inventoryId: 'inv3',
        url: 'http://example.com',
        processing: false,
      });
      expect(updated.createdAt).toBeInstanceOf(Date);
      expect(updated.updatedAt).toBeInstanceOf(Date);
    });
  });
});
