import InventoryMediaEntity, { InventoryMediaSize } from '#domain/aggregates/inventoryMedia/InventoryMedia.Entity';
import MediaOperator from '#domain/common/aggregates/media/Media.Operator';
import { Modify } from '#domain/common/Common.Type';

export type InventoryMediaBuildParams = Modify<InventoryMediaEntity, {
  createdAt?: Date;
  updatedAt?: Date;
  size?: InventoryMediaSize;
  processing?: boolean;
}>;

export type InventoryMediaUpdateParams = {
  processing: boolean;
};

function build(params: InventoryMediaBuildParams): InventoryMediaEntity {
  const {
    id,
    mediaType,
    inventoryId,
    url,
    size = InventoryMediaSize.MEDIUM,
    processing = true,
    createdAt = new Date(),
    updatedAt = new Date(),
  } = params;

  const media = MediaOperator.build({
    id,
    mediaType,
    url,
    processing,
    createdAt,
    updatedAt,
  });

  return {
    ...media,
    size,
    inventoryId,
  };
}

function update(CatalogMedia: InventoryMediaEntity, updates: InventoryMediaUpdateParams): InventoryMediaEntity {
  return build({
    ...CatalogMedia,
    ...updates,
  });
}

export default {
  build,
  update,
};
