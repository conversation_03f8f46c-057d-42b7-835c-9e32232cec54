import Cause from '#composition/Cause.type';

import InventoryEntity, { InventoryType, ProviderInventory } from './Inventory.Entity';
import InventoryOperator, { inventoryBuildParams } from './Inventory.Operator';

jest.unmock('./Inventory.Operator');

describe('InventoryOperator', () => {
  const mockDate = new Date('2023-01-01');

  beforeAll(() => {
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  const baseInventory: InventoryEntity = {
    id: '1',
    sku: 'SKU123',
    name: 'Test Product',
    description: 'Test Description',
    stock: 10,
    companyId: 'company1',
    type: InventoryType.PRODUCT_INPUT,
    standardIdentifier: null,
    restrictedStock: 0,
    measurementUnit: 'units',
    hasStockValidation: true,
    attributes: [],
    providers: [],
    createdAt: mockDate,
    updatedAt: mockDate,
    disabledAt: null,
  };

  describe('build', () => {
    it('should build an inventory object with default values', () => {
      const params: inventoryBuildParams = {
        id: '1',
        sku: 'SKU123',
        name: 'Test Product',
        stock: 10,
        companyId: 'company1',
        type: InventoryType.PRODUCT_INPUT,
        measurementUnit: 'units',
        hasStockValidation: true,
        disabledAt: null,
        providers: [],
      };

      const result = InventoryOperator.build(params);

      expect(result).toEqual({
        ...params,
        attributes: [],
        providers: [],
        standardIdentifier: null,
        description: null,
        createdAt: mockDate,
        updatedAt: mockDate,
        restrictedStock: 0,
      });
    });

    it('should throw error when stock is negative', () => {
      const params: inventoryBuildParams = {
        ...baseInventory,
        stock: -1,
      };

      expect(() => InventoryOperator.build(params))
        .toThrow(new Error('Stock cannot be negative', { cause: Cause.BAD_REQUEST }));
    });

    it('should throw error when restricted stock is negative', () => {
      const params: inventoryBuildParams = {
        ...baseInventory,
        restrictedStock: -1,
      };

      expect(() => InventoryOperator.build(params))
        .toThrow(new Error('Restricted stock cannot be negative', { cause: Cause.BAD_REQUEST }));
    });

    it('should throw error when restricted stock is greater than stock', () => {
      const params: inventoryBuildParams = {
        ...baseInventory,
        stock: 10,
        restrictedStock: 15,
      };

      expect(() => InventoryOperator.build(params))
        .toThrow(new Error('Restricted stock cannot be greater than stock', { cause: Cause.BAD_REQUEST }));
    });

    it('should build inventory with valid restricted stock', () => {
      const params: inventoryBuildParams = {
        ...baseInventory,
        stock: 10,
        restrictedStock: 5,
      };

      const result = InventoryOperator.build(params);

      expect(result.restrictedStock).toBe(5);
    });
  });

  describe('addStock', () => {
    it('should add stock when inventory has stock validation', () => {
      const inventory: InventoryEntity = { ...baseInventory };
      const quantity = 5;

      const result = InventoryOperator.addStock(inventory, quantity);

      expect(result.stock).toBe(15);
    });

    it('should add stock even when inventory has no stock validation', () => {
      const inventory: InventoryEntity = {
        ...baseInventory,
        hasStockValidation: false,
      };

      const result = InventoryOperator.addStock(inventory, 5);

      expect(result.stock).toBe(15); // Now it should add stock regardless of hasStockValidation
    });

    it('should throw error when inventory is disabled', () => {
      const inventory: InventoryEntity = {
        ...baseInventory,
        disabledAt: mockDate,
      };

      expect(() => InventoryOperator.addStock(inventory, 5))
        .toThrow(new Error('Cannot add stock to a disabled inventory', { cause: Cause.BAD_REQUEST }));
    });
  });

  describe('subtractStock', () => {
    it('should subtract stock when inventory has stock validation', () => {
      const inventory: InventoryEntity = { ...baseInventory };

      const result = InventoryOperator.subtractStock(inventory, 5);

      expect(result.stock).toBe(5);
    });

    it('should not modify stock when inventory has no stock validation', () => {
      const inventory: InventoryEntity = {
        ...baseInventory,
        hasStockValidation: false,
      };

      const result = InventoryOperator.subtractStock(inventory, 5);

      expect(result.stock).toBe(10);
    });

    it('should throw error when trying to subtract more than available', () => {
      const inventory: InventoryEntity = { ...baseInventory };

      expect(() => InventoryOperator.subtractStock(inventory, 15))
        .toThrow(new Error('Cannot subtract more than available', { cause: Cause.BAD_REQUEST }));
    });

    it('should throw error when inventory is disabled', () => {
      const inventory: InventoryEntity = {
        ...baseInventory,
        disabledAt: mockDate,
      };

      expect(() => InventoryOperator.subtractStock(inventory, 5))
        .toThrow(new Error('Cannot subtract stock from a disabled inventory', { cause: Cause.BAD_REQUEST }));
    });

    it('should consider restricted stock when checking available stock', () => {
      const inventory: InventoryEntity = {
        ...baseInventory,
        stock: 10,
        restrictedStock: 5,
      };

      // Should be able to subtract up to available stock (10-5=5)
      const result = InventoryOperator.subtractStock(inventory, 5);
      expect(result.stock).toBe(5);

      // Should throw when trying to subtract more than available (10-5=5)
      expect(() => InventoryOperator.subtractStock(inventory, 6))
        .toThrow(new Error('Cannot subtract more than available', { cause: Cause.BAD_REQUEST }));
    });
  });

  describe('update', () => {
    it('should update inventory fields and handle stock changes', () => {
      const current: InventoryEntity = { ...baseInventory };
      const toUpdate: Partial<Omit<InventoryEntity, 'id'>> = {
        stock: 15,
        name: 'Updated Name',
      };

      const result = InventoryOperator.update(current, toUpdate);

      expect(result).toEqual({
        ...current,
        ...toUpdate,
        attributes: [],
        updatedAt: mockDate,
      });
    });

    it('should set stock to zero when newStock is 0', () => {
      const current: InventoryEntity = { ...baseInventory };

      const result = InventoryOperator.update(current, { stock: 0 });

      expect(result.stock).toBe(0);
    });

    it('should decrease stock when new stock is less than current and hasStockValidation is true', () => {
      const current: InventoryEntity = { ...baseInventory }; // current stock is 10
      const toUpdate: Partial<Omit<InventoryEntity, 'id'>> = {
        stock: 3,
        name: 'Updated Name',
      };

      const result = InventoryOperator.update(current, toUpdate);

      expect(result).toEqual({
        ...current,
        ...toUpdate,
        attributes: [],
        updatedAt: mockDate,
      });
      expect(result.stock).toBe(3);
    });

    it('should not modify stock when stock is not provided', () => {
      const current: InventoryEntity = { ...baseInventory }; // current stock is 10
      const toUpdate: Partial<Omit<InventoryEntity, 'id'>> = {
        name: 'Updated Name Only',
      };

      const result = InventoryOperator.update(current, toUpdate);

      expect(result.stock).toBe(10); // Stock should remain unchanged
      expect(result.name).toBe('Updated Name Only');
    });

    it('should add stock when new stock is greater than current', () => {
      const current: InventoryEntity = { ...baseInventory }; // current stock is 10
      const toUpdate: Partial<Omit<InventoryEntity, 'id'>> = {
        stock: 15,
      };

      const result = InventoryOperator.update(current, toUpdate);

      expect(result.stock).toBe(15);
    });

    it('should throw an error when stock is modified and hasStockValidation is false', () => {
      const current: InventoryEntity = {
        ...baseInventory,
        hasStockValidation: false,
        stock: 10,
      };
      const toUpdate: Partial<Omit<InventoryEntity, 'id'>> = {
        stock: 5, // Attempting to decrease stock
      };

      expect(() => InventoryOperator.update(current, toUpdate))
        .toThrow('Cannot update stock when hasStockValidation is false');
    });
  });

  describe('areProviderInventoryEqual', () => {
    const providerInventory: ProviderInventory = {
      providerId: 'prov_123',
      currentPurchasePrice: 111,
      currentDiscount: 50,
      providerProductSku: 'SKU_123',
    };

    it('should return true when the ProviderInventories are equal', () => {
      const a = { ...providerInventory };
      const b = { ...providerInventory };

      const areEqual = InventoryOperator.areProviderInventoryEqual(a, b);

      expect(areEqual).toBeTruthy();
    });

    it('should return false when the ids are different', () => {
      const a = { ...providerInventory };
      const b = { ...providerInventory, providerId: 'different_id' };

      const areEqual = InventoryOperator.areProviderInventoryEqual(a, b);

      expect(areEqual).toBeFalsy();
    });

    it('should return false when the currentDiscount are different', () => {
      const a = { ...providerInventory };
      const b = { ...providerInventory, currentDiscount: 32123 };

      const areEqual = InventoryOperator.areProviderInventoryEqual(a, b);

      expect(areEqual).toBeFalsy();
    });

    it('should return false when the currentPurchasePrice are different', () => {
      const a = { ...providerInventory };
      const b = { ...providerInventory, currentPurchasePrice: 32123 };

      const areEqual = InventoryOperator.areProviderInventoryEqual(a, b);

      expect(areEqual).toBeFalsy();
    });

    it('should return false when the providerProductSku are different', () => {
      const a = { ...providerInventory };
      const b = { ...providerInventory, providerProductSku: 'SKU_DIFFERENT' };

      const areEqual = InventoryOperator.areProviderInventoryEqual(a, b);

      expect(areEqual).toBeFalsy();
    });
  });
});
