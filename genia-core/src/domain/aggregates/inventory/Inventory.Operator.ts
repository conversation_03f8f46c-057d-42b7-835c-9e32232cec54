import Cause from '#composition/Cause.type';
import { AttributeType, Modify } from '#domain/common/Common.Type';

import InventoryEntity, { ProviderInventory } from './Inventory.Entity';

export type providerInventoryParams = Modify<ProviderInventory, {
  currentDiscount?: number | null;
  providerProductSku?: string | null;
}>;

export type inventoryBuildParams = Modify<InventoryEntity, {
  attributes?: AttributeType[] | null;
  description?: string | null;
  providers?: providerInventoryParams[] | null;
  standardIdentifier?: string | null;
  restrictedStock?: number;
  createdAt?: Date;
  updatedAt?: Date;
  disabledAt?: Date | null;
}>;

export type inventoryUpdateParams = Modify<Partial<Omit<InventoryEntity, 'id'>>, { providers?: providerInventoryParams[] | null}>;

function build(params: inventoryBuildParams): InventoryEntity {
  const {
    id,
    sku,
    name,
    description,
    companyId,
    standardIdentifier,
    type,
    measurementUnit,
    stock,
    attributes,
    providers,
    restrictedStock,
    hasStockValidation,
    createdAt,
    updatedAt,
    disabledAt,
  } = params;

  if (stock < 0) throw new Error('Stock cannot be negative', { cause: Cause.BAD_REQUEST });

  if (restrictedStock) {
    if (restrictedStock < 0) throw new Error('Restricted stock cannot be negative', { cause: Cause.BAD_REQUEST });

    if (restrictedStock > stock && hasStockValidation) throw new Error('Restricted stock cannot be greater than stock', { cause: Cause.BAD_REQUEST });
  }

  const mappedProviders = providers?.map<ProviderInventory>(({ currentDiscount, providerProductSku, ...rest }) => ({
    currentDiscount: currentDiscount || null,
    providerProductSku: providerProductSku || null,
    ...rest,
  })) || [];

  return {
    id,
    sku,
    name,
    description: description || null,
    companyId,
    standardIdentifier: standardIdentifier || null,
    type,
    measurementUnit,
    stock,
    attributes: attributes || [],
    providers: mappedProviders,
    restrictedStock: restrictedStock || 0,
    hasStockValidation: hasStockValidation || false,
    disabledAt: disabledAt || null,
    createdAt: createdAt || new Date(),
    updatedAt: updatedAt || new Date(),
  };
}

function addStock(inventory: InventoryEntity, quantity: number): InventoryEntity {
  if (inventory.disabledAt) throw new Error('Cannot add stock to a disabled inventory', { cause: Cause.BAD_REQUEST });

  const modifiedInventory = { ...inventory };

  modifiedInventory.stock += quantity;

  return modifiedInventory;
}

function subtractStock(inventory: InventoryEntity, quantity: number): InventoryEntity {
  const modifiedInventory = { ...inventory };

  const availableStock = modifiedInventory.stock - modifiedInventory.restrictedStock;

  if (!modifiedInventory.hasStockValidation) return modifiedInventory;

  if (modifiedInventory.disabledAt) throw new Error('Cannot subtract stock from a disabled inventory', { cause: Cause.BAD_REQUEST });

  if (availableStock - quantity < 0) throw new Error('Cannot subtract more than available', { cause: Cause.BAD_REQUEST });

  modifiedInventory.stock -= quantity;

  return modifiedInventory;
}

function update(current: InventoryEntity, toUpdate: inventoryUpdateParams): InventoryEntity {
  const { stock: newStock, ...fieldsToUpdate } = toUpdate;

  let updatedInventory: InventoryEntity = build({ ...current, ...fieldsToUpdate, updatedAt: new Date() });

  if (newStock === null || newStock === undefined) {
    return updatedInventory;
  }

  const stockToAdd = newStock - current.stock;

  if (stockToAdd !== 0 && !updatedInventory.hasStockValidation) {
    throw new Error('Cannot update stock when hasStockValidation is false', { cause: Cause.BAD_REQUEST });
  }

  if (stockToAdd < 0 && updatedInventory.hasStockValidation) {
    updatedInventory = subtractStock(updatedInventory, Math.abs(stockToAdd));
  }

  if (stockToAdd > 0) {
    updatedInventory = addStock(updatedInventory, stockToAdd);
  }

  return updatedInventory;
}

function areProviderInventoryEqual(a: ProviderInventory, b: ProviderInventory): boolean {
  return a.providerId === b.providerId
    && a.currentDiscount === b.currentDiscount
    && a.currentPurchasePrice === b.currentPurchasePrice
    && a.providerProductSku === b.providerProductSku;
}

export default {
  build,
  addStock,
  subtractStock,
  update,
  areProviderInventoryEqual,
};
