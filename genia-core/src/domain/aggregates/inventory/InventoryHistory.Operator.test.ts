import { InventoryMovementType } from '#domain/aggregates/inventory/InventoryHistory.Entity';

import InventoryHistoryOperator from './InventoryHistory.Operator';

jest.unmock('./InventoryHistory.Operator');

describe('InventoryHistoryOperator.build', () => {
  it('should build an InventoryHistoryEntity with provided params and default createdAt', () => {
    const params = {
      id: 'history-1',
      inventoryId: 'inv-1',
      quantity: 5,
      measurementUnit: 'units',
      movementType: InventoryMovementType.INBOUND,
      userId: 'user-1',
      reason: 'Initial stock',
    };

    const result = InventoryHistoryOperator.build(params);

    expect(result).toMatchObject({
      ...params,
      createdAt: expect.any(Date),
    });
  });

  it('should use provided createdAt if given', () => {
    const date = new Date('2024-01-01T00:00:00.000Z');
    const params = {
      id: 'history-2',
      inventoryId: 'inv-2',
      quantity: 10,
      measurementUnit: 'kg',
      movementType: InventoryMovementType.OUTBOUND,
      userId: 'user-2',
      reason: 'Shipment',
      createdAt: date,
    };

    const result = InventoryHistoryOperator.build(params);

    expect(result).toMatchObject({
      ...params,
      createdAt: date,
    });
  });
});
