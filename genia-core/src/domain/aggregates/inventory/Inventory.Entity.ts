import { AttributeType } from '#domain/common/Common.Type';

export enum InventoryType {
  COMMODITY = 'commodity',
  PRODUCT_INPUT = 'product_input',
}

export interface ProviderInventory {
  providerId: string;
  currentPurchasePrice: number;
  currentDiscount: number | null;
  providerProductSku: string | null;
}

export default interface InventoryEntity {
  id: string;
  sku: string;
  name: string;
  description: string | null;
  stock: number;
  attributes: AttributeType[];
  companyId: string;
  standardIdentifier: string | null;
  type: InventoryType;
  measurementUnit: string;
  hasStockValidation: boolean;
  providers: ProviderInventory[];
  createdAt: Date;
  updatedAt: Date;
  disabledAt: Date | null;
  restrictedStock: number;
}
