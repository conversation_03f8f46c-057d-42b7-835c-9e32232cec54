import { InventoryHistoryEntity } from '#domain/aggregates/inventory/InventoryHistory.Entity';
import { Modify } from '#domain/common/Common.Type';

export type inventoryHistoryBuildParams = Modify<InventoryHistoryEntity, {
  createdAt?: Date;
}>;

function build(params: inventoryHistoryBuildParams): InventoryHistoryEntity {
  const createdAt = params.createdAt || new Date();

  return {
    ...params,
    createdAt,
  };
}

export default {
  build,
};
