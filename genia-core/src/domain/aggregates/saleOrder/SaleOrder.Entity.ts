import OrderEntity, { OrderItem, OrderStatus } from '#domain/common/aggregates/order/Order.Entity';

export type SaleOrderStatus = OrderStatus;
export const SaleOrderStatus = {
  ...OrderStatus,
};

export interface SaleOrderItem extends OrderItem {
  catalogId: string;
}

export default interface SaleOrderEntity extends OrderEntity<SaleOrderStatus>{
  clientId: string;
  status: SaleOrderStatus;
  orderItems: SaleOrderItem[];
  relatedPurchaseOrderId: string | null;
}
