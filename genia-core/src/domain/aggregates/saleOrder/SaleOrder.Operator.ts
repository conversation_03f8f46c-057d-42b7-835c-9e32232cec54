import Cause from '#composition/Cause.type';
import SaleOrderEntity, { SaleOrderItem, SaleOrderStatus } from '#domain/aggregates/saleOrder/SaleOrder.Entity';
import { OrderStatus } from '#domain/common/aggregates/order/Order.Entity';
import OrderOperator, { OrderBuildParams, OrderItemBuildParams, OrderUpdateParams } from '#domain/common/aggregates/order/Order.Operator';
import { Modify } from '#domain/common/Common.Type';

export type SaleOrderItemBuildParams = Modify<OrderItemBuildParams, {
  catalogId: string;
}>;

export type SaleOrderBuildParams = Modify<OrderBuildParams, {
  clientId: string;
  status: SaleOrderStatus;
  orderItems: SaleOrderItemBuildParams[];
  relatedPurchaseOrderId?: string | null;
}>;

function build(params: SaleOrderBuildParams): SaleOrderEntity {
  const { status: orderStatus, ...order } = OrderOperator.build({ ...params, status: params.status as OrderStatus });

  const {
    clientId,
    status,
    orderItems,
    relatedPurchaseOrderId = null,
  } = params;

  const builtOrderItems: SaleOrderItem[] = orderItems.map((item, i) => ({
    ...order.orderItems[i],
    catalogId: item.catalogId,
  }));

  return {
    clientId,
    ...order,
    status,
    orderItems: builtOrderItems,
    relatedPurchaseOrderId,
  };
}

function updateStatus(
  saleOrder: SaleOrderEntity,
  newStatus: SaleOrderStatus,
  modifierCompanyId: string,
): SaleOrderEntity {
  if (
    newStatus === SaleOrderStatus.APPROVED_BY_CLIENT
     && saleOrder.status === SaleOrderStatus.CLIENT_APPROVAL
      && saleOrder.relatedPurchaseOrderId
        && modifierCompanyId === saleOrder.companyId
  ) {
    throw new Error('Cannot self approve sale order with a related purchase order', { cause: Cause.FORBIDDEN });
  }

  if (newStatus !== SaleOrderStatus.APPROVED_BY_CLIENT && modifierCompanyId !== saleOrder.companyId) {
    return saleOrder;
  }

  const orderToUpdate = {
    ...saleOrder,
    status: saleOrder.status as OrderStatus,
  };

  const updatedOrder = OrderOperator.updateStatus(orderToUpdate, newStatus);

  return {
    ...saleOrder,
    ...updatedOrder,
    orderItems: saleOrder.orderItems,
  };
}

function update(
  saleOrder: SaleOrderEntity,
  params: OrderUpdateParams<SaleOrderStatus>,
): SaleOrderEntity {
  const { status, ...otherParams } = params;

  const orderToUpdate = { ...saleOrder, orderItems: saleOrder.orderItems.map((item) => ({ ...item })) };

  const updatedOrder = OrderOperator.update<SaleOrderStatus>(orderToUpdate, otherParams);

  return {
    ...saleOrder,
    ...updatedOrder,
    orderItems: saleOrder.orderItems,
  };
}

function availableStatuses(saleOrder: SaleOrderEntity): SaleOrderStatus[] {
  if (saleOrder.relatedPurchaseOrderId && saleOrder.status === SaleOrderStatus.CLIENT_APPROVAL) {
    return [];
  }

  return OrderOperator.availableStatuses(saleOrder);
}

export default {
  build,
  update,
  updateStatus,
  availableStatuses,
};
