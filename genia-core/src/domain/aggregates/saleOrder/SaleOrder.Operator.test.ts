import Cause from '#composition/Cause.type';
import { SaleOrderStatus } from '#domain/aggregates/saleOrder/SaleOrder.Entity';
import SaleOrderOperator, { SaleOrderBuildParams } from '#domain/aggregates/saleOrder/SaleOrder.Operator';
import OrderOperator, { OrderBuildParams } from '#domain/common/aggregates/order/Order.Operator';

jest.unmock('./SaleOrder.Operator');

describe('SaleOrderOperator', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (OrderOperator.build as jest.Mock).mockImplementation((params: OrderBuildParams) => ({
      ...params,
      orderItems: params.orderItems.map((item) => ({
        ...item,
        discount: null,
        taxes: [],
      })),
      createdAt: new Date(),
      updatedAt: new Date(),
      reviewStartedAt: null,
      shippedAt: null,
      shippingAddress: null,
      shippingPrice: 0,
    }));

    (OrderOperator.updateStatus as jest.Mock).mockImplementation((order, status) => ({
      ...order,
      status,
      updatedAt: new Date(),
    }));

    (OrderOperator.update as jest.Mock).mockImplementation((order, params) => ({
      ...order,
      ...params,
      updatedAt: new Date(),
    }));

    (OrderOperator.availableStatuses as jest.Mock).mockImplementation(() => [
      SaleOrderStatus.PENDING,
      SaleOrderStatus.PROCESSING,
      SaleOrderStatus.CLIENT_APPROVAL,
      SaleOrderStatus.APPROVED_BY_CLIENT,
    ]);
  });

  describe('build', () => {
    it('should build a SaleOrderEntity with valid params', () => {
      const params: SaleOrderBuildParams = {
        id: '1',
        readId: 'read-1',
        companyId: 'company-1',
        assignedUserId: 'user-1',
        status: SaleOrderStatus.PENDING,
        clientId: 'provider-1',
        subtotalBeforeDiscount: 100,
        subtotal: 100,
        totalDiscount: 0,
        totalTaxes: 0,
        total: 100,
        notes: 'Test notes',
        shippingAddress: null,
        shippingPrice: 0,
        deliveryDate: null,
        reviewStartedAt: null,
        shippedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        orderItems: [
          {
            catalogId: 'item-1',
            name: 'Item 1',
            productId: 'product-1',
            quantity: 2,
            unitPrice: 50,
            discount: null,
            unitPriceAfterDiscount: 50,
            unitPriceAfterDiscountAndTaxes: 50,
            total: 100,
            subtotal: 100,
            taxes: [],
          },
        ],
        taxes: [],
        relatedPurchaseOrderId: 'purchase-order-1',
      };

      const SaleOrder = SaleOrderOperator.build(params);

      expect(SaleOrder).toEqual({
        ...params,
        orderItems: params.orderItems.map((item) => ({
          ...item,
          catalogId: item.catalogId,
        })),
      });
    });
  });

  it('Should build a SaleOrderEntity with default values', () => {
    const params: SaleOrderBuildParams = {
      id: '1',
      readId: 'read-1',
      companyId: 'company-1',
      assignedUserId: 'user-1',
      status: SaleOrderStatus.PENDING,
      clientId: 'provider-1',
      subtotalBeforeDiscount: 0,
      subtotal: 0,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 0,
      notes: null,
      shippingAddress: null,
      shippingPrice: 0,
      deliveryDate: null,
      reviewStartedAt: null,
      shippedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      orderItems: [],
      taxes: [],
    };

    const SaleOrder = SaleOrderOperator.build(params);

    expect(SaleOrder).toEqual({
      ...params,
      orderItems: [],
      relatedPurchaseOrderId: null,
    });
  });

  describe('updateStatus', () => {
    const mockSaleOrder = {
      id: '1',
      readId: 'read-1',
      companyId: 'company-1',
      assignedUserId: 'user-1',
      status: SaleOrderStatus.CLIENT_APPROVAL,
      clientId: 'client-1',
      subtotalBeforeDiscount: 100,
      subtotal: 100,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 100,
      notes: 'Test notes',
      shippingAddress: null,
      shippingPrice: 0,
      deliveryDate: null,
      reviewStartedAt: null,
      shippedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      relatedPurchaseOrderId: null,
      orderItems: [
        {
          id: 'soi-1',
          catalogId: 'item-1',
          name: 'Item 1',
          productId: 'product-1',
          quantity: 2,
          unitPrice: 50,
          discount: null,
          unitPriceAfterDiscount: 50,
          unitPriceAfterDiscountAndTaxes: 50,
          total: 100,
          subtotal: 100,
          taxes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      taxes: [],
    };

    it('should update the status of a sale order', () => {
      const updatedOrder = SaleOrderOperator.updateStatus(
        mockSaleOrder,
        SaleOrderStatus.PROCESSING,
        'company-1',
      );

      expect(updatedOrder.status).toBe(SaleOrderStatus.PROCESSING);
      expect(OrderOperator.updateStatus).toHaveBeenCalledWith(
        expect.objectContaining({ id: '1' }),
        SaleOrderStatus.PROCESSING,
      );
    });

    it('should throw an error when trying to self-approve a sale order with a related purchase order', () => {
      const linkedOrder = {
        ...mockSaleOrder,
        relatedPurchaseOrderId: 'purchase-order-1',
      };

      expect(() => {
        SaleOrderOperator.updateStatus(
          linkedOrder,
          SaleOrderStatus.APPROVED_BY_CLIENT,
          'company-1',
        );
      }).toThrow(new Error('Cannot self approve sale order with a related purchase order'));
      expect(() => {
        SaleOrderOperator.updateStatus(
          linkedOrder,
          SaleOrderStatus.APPROVED_BY_CLIENT,
          'company-1',
        );
      }).toThrow(expect.objectContaining({ cause: Cause.FORBIDDEN }));
    });

    it('should allow other-company to approve a sale order', () => {
      const linkedOrder = {
        ...mockSaleOrder,
        relatedPurchaseOrderId: 'purchase-order-1',
      };

      const updatedOrder = SaleOrderOperator.updateStatus(
        linkedOrder,
        SaleOrderStatus.APPROVED_BY_CLIENT,
        'different-company-id',
      );

      expect(updatedOrder.status).toBe(SaleOrderStatus.APPROVED_BY_CLIENT);
    });

    it('should return unchanged order when trying to update status by different company (not APPROVED_BY_CLIENT)', () => {
      const result = SaleOrderOperator.updateStatus(
        mockSaleOrder,
        SaleOrderStatus.PROCESSING,
        'different-company-id',
      );

      expect(result).toEqual(mockSaleOrder);
      expect(OrderOperator.updateStatus).not.toHaveBeenCalled();
    });
  });

  describe('update', () => {
    const mockSaleOrder = {
      id: '1',
      readId: 'read-1',
      companyId: 'company-1',
      assignedUserId: 'user-1',
      status: SaleOrderStatus.PENDING,
      clientId: 'client-1',
      subtotalBeforeDiscount: 100,
      subtotal: 100,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 100,
      notes: 'Test notes',
      shippingAddress: null,
      shippingPrice: 0,
      deliveryDate: null,
      reviewStartedAt: null,
      shippedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      relatedPurchaseOrderId: null,
      orderItems: [
        {
          id: 'soi-1',
          catalogId: 'item-1',
          name: 'Item 1',
          productId: 'product-1',
          quantity: 2,
          unitPrice: 50,
          discount: null,
          unitPriceAfterDiscount: 50,
          unitPriceAfterDiscountAndTaxes: 50,
          total: 100,
          subtotal: 100,
          taxes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      taxes: [],
    };

    it('should update a sale order with valid params', () => {
      const newDeliveryDate = new Date();
      const updatedOrder = SaleOrderOperator.update(
        mockSaleOrder,
        {
          notes: 'Updated notes',
          deliveryDate: newDeliveryDate,
        },
      );

      expect(updatedOrder.notes).toBe('Updated notes');
      expect(updatedOrder.deliveryDate).toBe(newDeliveryDate);
      expect(OrderOperator.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          notes: 'Updated notes',
          deliveryDate: newDeliveryDate,
        }),
      );
    });

    it('should not apply status updates via update method', () => {
      // The implementation of update() in SaleOrder.Operator.ts extracts
      // status from params but doesn't use it, so status won't change
      const updatedOrder = SaleOrderOperator.update(
        mockSaleOrder,
        {
          status: SaleOrderStatus.PROCESSING,
        },
      );

      // Status should remain unchanged since it's not passed to OrderOperator.update
      expect(updatedOrder.status).toBe(SaleOrderStatus.PENDING);

      // Status was destructured out of params passed to OrderOperator.update
      expect(OrderOperator.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.not.objectContaining({ status: SaleOrderStatus.PROCESSING }),
      );
    });

    it('should only update non-status fields when both status and other fields are provided', () => {
      const updatedOrder = SaleOrderOperator.update(
        mockSaleOrder,
        {
          status: SaleOrderStatus.APPROVED_BY_CLIENT,
          notes: 'New notes',
        },
      );

      expect(updatedOrder.status).toBe(SaleOrderStatus.PENDING); // Status shouldn't change
      expect(updatedOrder.notes).toBe('New notes'); // Other fields should update

      // Verify that status wasn't passed to OrderOperator.update
      expect(OrderOperator.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({ notes: 'New notes' }),
      );
      expect(OrderOperator.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.not.objectContaining({ status: expect.anything() }),
      );
    });
  });

  describe('availableStatuses', () => {
    const mockSaleOrder = {
      id: '1',
      readId: 'read-1',
      companyId: 'company-1',
      assignedUserId: 'user-1',
      status: SaleOrderStatus.CLIENT_APPROVAL,
      clientId: 'client-1',
      subtotalBeforeDiscount: 100,
      subtotal: 100,
      totalDiscount: 0,
      totalTaxes: 0,
      total: 100,
      notes: 'Test notes',
      shippingAddress: null,
      shippingPrice: 0,
      deliveryDate: null,
      reviewStartedAt: null,
      shippedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      relatedPurchaseOrderId: null,
      orderItems: [],
      taxes: [],
    };

    it('should return empty array when sale order has related purchase order and is in CLIENT_APPROVAL status', () => {
      const linkedOrder = {
        ...mockSaleOrder,
        relatedPurchaseOrderId: 'purchase-order-1',
        status: SaleOrderStatus.CLIENT_APPROVAL,
      };

      const availableStatuses = SaleOrderOperator.availableStatuses(linkedOrder);

      expect(availableStatuses).toEqual([]);
      expect(OrderOperator.availableStatuses).not.toHaveBeenCalled();
    });

    it('should use OrderOperator.availableStatuses when either condition is not met', () => {
      // No related purchase order
      const result1 = SaleOrderOperator.availableStatuses(mockSaleOrder);
      expect(OrderOperator.availableStatuses).toHaveBeenCalledWith(mockSaleOrder);
      expect(result1).toEqual([
        SaleOrderStatus.PENDING,
        SaleOrderStatus.PROCESSING,
        SaleOrderStatus.CLIENT_APPROVAL,
        SaleOrderStatus.APPROVED_BY_CLIENT,
      ]);

      // Has related purchase order but different status
      const linkedOrderDifferentStatus = {
        ...mockSaleOrder,
        relatedPurchaseOrderId: 'purchase-order-1',
        status: SaleOrderStatus.PENDING,
      };

      const result2 = SaleOrderOperator.availableStatuses(linkedOrderDifferentStatus);
      expect(OrderOperator.availableStatuses).toHaveBeenCalledWith(linkedOrderDifferentStatus);
      expect(result2).toEqual([
        SaleOrderStatus.PENDING,
        SaleOrderStatus.PROCESSING,
        SaleOrderStatus.CLIENT_APPROVAL,
        SaleOrderStatus.APPROVED_BY_CLIENT,
      ]);
    });
  });
});
