import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import ContactInformationOperator from '#domain/common/aggregates/contactInformation/ContactInformation.Operator';

import CompanyOperator, { CompanyBuildParams, CompanyUpdateParams } from './Company.Operator';

jest.unmock('./Company.Operator');

describe('Company Operator', () => {
  describe('build', () => {
    it('should create a company object with all required fields', () => {
      const companyParams: CompanyBuildParams = {
        id: '123',
        name: 'Test Company',
        description: 'A test company description',
        country: 'US',
        tributaryId: '12345678',
      };

      const result = CompanyOperator.build(companyParams);

      expect(result).toEqual({
        id: '123',
        name: 'Test Company',
        description: 'A test company description',
        country: 'US',
        tributaryId: '12345678',
        contactInformation: null,
      });
    });

    it('should preserve all fields from the input params', () => {
      const companyParams: CompanyBuildParams = {
        id: 'company-id-456',
        name: 'Another Company',
        description: 'Another description',
        country: 'CA',
        tributaryId: '87654321',
      };

      const result = CompanyOperator.build(companyParams);

      expect(result).toEqual({ ...companyParams, contactInformation: null });
    });

    it('should handle empty description', () => {
      const companyParams: CompanyBuildParams = {
        id: 'company-id-789',
        name: 'Company with empty description',
        description: '',
        country: 'MX',
        tributaryId: '11111111',
      };

      const result = CompanyOperator.build(companyParams);

      expect(result).toEqual({ ...companyParams, contactInformation: null });
      expect(result.description).toBe('');
    });

    it('should handle contact information', () => {
      const companyParams: CompanyBuildParams = {
        id: 'company-id-789',
        name: 'Company with empty description',
        description: '',
        country: 'MX',
        tributaryId: '11111111',
        contactInformation: {
          billingAddress: '123 Main St',
          mainAddress: '123 Main St',
          representativeName: 'John Doe',
          shippingAddress: '456 Side St',
          billingPhoneNumber: '555-1234',
          billingEmail: '<EMAIL>',
          billingWhatsapp: null,
          mainPhoneNumber: '555-1234',
          mainEmail: '<EMAIL>',
          mainWhatsapp: null,
          salesEmail: null,
          salesPhoneNumber: null,
          salesWhatsapp: null,
          purchasesEmail: null,
          purchasesPhoneNumber: null,
          purchasesWhatsapp: null,
        },
      };

      const result = CompanyOperator.build(companyParams);

      expect(result).toEqual(companyParams);
    });
  });

  describe('update', () => {
    const company: CompanyEntity = {
      id: 'comp_123',
      name: 'Biiztral',
      description: 'A tech company',
      tributaryId: '*********',
      country: 'CO',
      contactInformation: null,
    };

    const contactInformation = {
      mainEmail: 'mainEmail',
      mainPhoneNumber: 'mainPhoneNumber',
      mainWhatsapp: 'mainWhatsapp',
      billingEmail: 'billingEmail',
      billingPhoneNumber: 'billingPhoneNumber',
      billingWhatsapp: 'billingWhatsapp',
      purchasesEmail: 'purchasesEmail',
      purchasesPhoneNumber: 'purchasesPhoneNumber',
      purchasesWhatsapp: 'purchasesWhatsapp',
      salesEmail: 'salesEmail',
      salesPhoneNumber: 'salesPhoneNumber',
      salesWhatsapp: 'salesWhatsapp',
      shippingAddress: 'shippingAddress',
      mainAddress: 'mainAddress',
      billingAddress: 'billingAddress',
      representativeName: 'representativeName',
    };

    it('should update a company correctly', () => {
      const params = {
        name: 'Biiztral Updated',
        description: 'An updated tech company',
        tributaryId: '*********',
      };

      const updated = CompanyOperator.update(company, params);

      expect(updated).toEqual({ ...company, ...params });
    });

    it('should add contact information correctly when there was no previous one', () => {
      (ContactInformationOperator.build as jest.Mock).mockImplementation((ci) => ci);

      const params: CompanyUpdateParams = {
        contactInformation: {
          mainEmail: '<EMAIL>',
          mainAddress: '123 Main St',
        },
      };

      const updated = CompanyOperator.update(company, params);

      expect(updated).toEqual({ ...company, ...params });
    });

    it('should add contact information correctly when there was previous one', () => {
      (ContactInformationOperator.build as jest.Mock).mockImplementation((ci) => ci);

      const params: CompanyUpdateParams = {
        contactInformation: {
          mainEmail: '<EMAIL>',
          mainAddress: '123 Main St',
        },
      };

      const updated = CompanyOperator.update({ ...company, contactInformation }, params);

      const expected = { ...company, contactInformation: { ...contactInformation, ...params.contactInformation } };

      expect(updated).toEqual(expected);
    });

    it('should remove contact information when null is passed', () => {
      const updated = CompanyOperator.update({ ...company, contactInformation }, { contactInformation: null });

      expect(updated).toEqual({ ...company, contactInformation: null });
    });
  });
});
