import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import ContactInformationOperator, { ContactInformationBuildParams } from '#domain/common/aggregates/contactInformation/ContactInformation.Operator';
import { ContactInformationValueObject } from '#domain/common/aggregates/contactInformation/ContactInformation.ValueObject';
import { Modify } from '#domain/common/Common.Type';

export type CompanyBuildParams = Modify<
CompanyEntity,
{
  contactInformation?: ContactInformationValueObject | null;
}
>;

export type CompanyUpdateParams = {
  name?: string;
  description?: string;
  tributaryId?: string;
  contactInformation?: Partial<ContactInformationValueObject> | null;
};

function build(params: CompanyBuildParams) : CompanyEntity {
  const {
    id, name, country, description, tributaryId, contactInformation = null,
  } = params;

  return {
    id,
    name,
    description,
    country,
    tributaryId,
    contactInformation,
  };
}

function update(company: CompanyEntity, params: CompanyUpdateParams): CompanyEntity {
  const { contactInformation: newContactInformation, ...baseUpdates } = params;

  const updatedCompany: CompanyEntity = {
    ...company,
    ...baseUpdates,
  };

  if (newContactInformation) {
    const oldContactInformation = company.contactInformation || {};

    const newContactInformationParams: ContactInformationBuildParams = {
      ...oldContactInformation,
      ...newContactInformation,
    };

    updatedCompany.contactInformation = ContactInformationOperator.build(newContactInformationParams);
  }

  if (newContactInformation === null) updatedCompany.contactInformation = null;

  return build(updatedCompany);
}

export default {
  build,
  update,
};
