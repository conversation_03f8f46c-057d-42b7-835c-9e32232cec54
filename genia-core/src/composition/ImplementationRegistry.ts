import CatalogRepository from '#application/catalog/repositories/Catalog.Repository';
import CatalogDiscountRepository from '#application/catalogDiscount/repositories/CatalogDiscount.Repository';
import CatalogMediaRepository from '#application/catalogMedia/repositories/CatalogMedia.Repository';
import ClientRepository from '#application/client/repositories/ClientRepository';
import CompanyRepository from '#application/company/repositories/Company.Repository';
import EmailService from '#application/Email.Service';
import FileService from '#application/FileService';
import IdentificationService from '#application/Identification.Service';
import { IntegrationService } from '#application/integration/services/Integration.Service';
import InventoryRepository from '#application/inventory/repositories/Inventory.Repository';
import InventoryHistoryRepository from '#application/inventory/repositories/InventoryHistory.Repository';
import InventoryMediaRepository from '#application/inventoryMedia/repositories/InventoryMedia.Repository';
import InvitationService from '#application/invitation/services/Invitation.Service';
import MessageService from '#application/Message.Service';
import NotificationService from '#application/notification/services/Notification.Service';
import ProviderRepository from '#application/provider/repositories/Provider.Repository';
import PurchaseOrderRepository from '#application/purchaseOrders/PurchaseOrder.Repository';
import QuoteRepository from '#application/quote/repositories/Quote.Repository';
import SaleOrderRepository from '#application/saleOrder/SaleOrder.Repository';
import StoreDiscountRepository from '#application/storeDiscount/repositories/StoreDiscount.Repository';
import TaxRepository from '#application/tax/repositories/Tax.Repository';
import TransactionService from '#application/Transaction.Service';
import UserRepository from '#application/user/repositories/User.Repository';
import Email from '#infrastructure/apis/Email.Api';
import GoogleCloudStorageApi from '#infrastructure/apis/GoogleCloudStorage.Api';
import WhatsappApi from '#infrastructure/apis/Whatsapp.Api';
import CoreCatalogRepository from '#infrastructure/implementations/catalog/repositories/CoreCatalog.Repository';
import CoreCatalogDiscountRepository from '#infrastructure/implementations/catalogDiscount/repositories/CoreCatalogDiscount.Repository';
import CoreCatalogMediaRepository from '#infrastructure/implementations/catalogMedia/repositories/CoreCatalogMedia.Repository';
import CoreClientRepository from '#infrastructure/implementations/client/repositories/CoreClient.Repository';
import CoreCompanyRepository from '#infrastructure/implementations/company/repositories/CoreCompany.Repository';
import CoreInvitationService from '#infrastructure/implementations/CoreInvitation.Service';
import CoreNotificationService from '#infrastructure/implementations/CoreNotification.Service';
import CoreTransactionService from '#infrastructure/implementations/CoreTransaction.Service';
import CoreIntegrationService from '#infrastructure/implementations/integrations/CoreIntegration.Service';
import CoreInventoryRepository from '#infrastructure/implementations/inventory/repositories/CoreInventory.Repository';
import CoreInventoryMovementRepository from '#infrastructure/implementations/inventory/repositories/CoreInventoryHistory.Repository';
import CoreInventoryMediaRepository from '#infrastructure/implementations/inventoryMedia/repositories/CoreInventoryMedia.Repository';
import CoreIdentificationService from '#infrastructure/implementations/invitation/CoreIdentification.Service';
import CoreProviderRepository from '#infrastructure/implementations/provider/repositories/CoreProvider.Repository';
import CorePurchaseOrderRepository from '#infrastructure/implementations/purchaseOrder/CorePurchaseOrder.Repository';
import CoreQuoteRepository from '#infrastructure/implementations/quote/CoreQuote.Repository';
import CoreSaleOrderRepository from '#infrastructure/implementations/saleOrder/CoreSaleOrder.Repository';
import CoreStoreDiscountRepository from '#infrastructure/implementations/storeDiscounts/CoreStoreDiscount.Repository';
import CoreTaxRepository from '#infrastructure/implementations/tax/repositories/CoreTax.Repository';
import CoreUserRepository from '#infrastructure/implementations/user/repositories/CoreUser.Repository';

// This type definitions should NEVER change, only implementations, new types can be added to the registry
export interface Registry {
  UserRepository: UserRepository;
  CompanyRepository: CompanyRepository;
  InventoryRepository: InventoryRepository;
  CatalogRepository: CatalogRepository;
  CatalogDiscountRepository: CatalogDiscountRepository;
  ClientRepository: ClientRepository;
  TaxRepository: TaxRepository;
  IdentificationService: IdentificationService;
  TransactionService: TransactionService;
  ProviderRepository: ProviderRepository;
  StoreDiscountRepository: StoreDiscountRepository;
  CatalogMediaRepository: CatalogMediaRepository
  PurchaseOrderRepository: PurchaseOrderRepository;
  SaleOrderRepository: SaleOrderRepository;
  QuoteRepository: QuoteRepository;
  InventoryHistoryRepository: InventoryHistoryRepository;
  InventoryMediaRepository: InventoryMediaRepository;
  EmailService: EmailService;
  FileService: FileService;
  MessageService: MessageService;
  InvitationService: InvitationService;
  NotificationService: NotificationService;
  IntegrationService: IntegrationService;
}

const Registry: Registry = {
  UserRepository: CoreUserRepository,
  CompanyRepository: CoreCompanyRepository,
  IdentificationService: CoreIdentificationService,
  InventoryRepository: CoreInventoryRepository,
  TransactionService: CoreTransactionService,
  CatalogRepository: CoreCatalogRepository,
  TaxRepository: CoreTaxRepository,
  CatalogDiscountRepository: CoreCatalogDiscountRepository,
  ProviderRepository: CoreProviderRepository,
  ClientRepository: CoreClientRepository,
  StoreDiscountRepository: CoreStoreDiscountRepository,
  CatalogMediaRepository: CoreCatalogMediaRepository,
  PurchaseOrderRepository: CorePurchaseOrderRepository,
  SaleOrderRepository: CoreSaleOrderRepository,
  QuoteRepository: CoreQuoteRepository,
  InventoryHistoryRepository: CoreInventoryMovementRepository,
  InventoryMediaRepository: CoreInventoryMediaRepository,
  EmailService: Email,
  FileService: GoogleCloudStorageApi,
  MessageService: WhatsappApi,
  InvitationService: CoreInvitationService,
  NotificationService: CoreNotificationService,
  IntegrationService: CoreIntegrationService,

};

export default Registry;
