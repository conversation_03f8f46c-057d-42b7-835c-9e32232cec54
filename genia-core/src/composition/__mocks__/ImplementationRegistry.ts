import CatalogRepository from '#application/catalog/repositories/Catalog.Repository';
import CatalogDiscountRepository from '#application/catalogDiscount/repositories/CatalogDiscount.Repository';
import CatalogMediaRepository from '#application/catalogMedia/repositories/CatalogMedia.Repository';
import ClientRepository from '#application/client/repositories/ClientRepository';
import CompanyRepository from '#application/company/repositories/Company.Repository';
import IdentificationService from '#application/Identification.Service';
import { IntegrationService } from '#application/integration/services/Integration.Service';
import InventoryRepository from '#application/inventory/repositories/Inventory.Repository';
import InventoryHistoryRepository from '#application/inventory/repositories/InventoryHistory.Repository';
import InventoryMediaRepository from '#application/inventoryMedia/repositories/InventoryMedia.Repository';
import ProviderRepository from '#application/provider/repositories/Provider.Repository';
import PurchaseOrderRepository from '#application/purchaseOrders/PurchaseOrder.Repository';
import SaleOrderRepository from '#application/saleOrder/SaleOrder.Repository';
import StoreDiscountRepository from '#application/storeDiscount/repositories/StoreDiscount.Repository';
import TaxRepository from '#application/tax/repositories/Tax.Repository';
import TransactionService from '#application/Transaction.Service';
import UserRepository from '#application/user/repositories/User.Repository';
import { Registry } from '#composition/ImplementationRegistry';

const user = {
  id: '1', password: 'password', email: 'email', name: 'name',
};

const userRepository: UserRepository = {
  findOneById: jest.fn().mockResolvedValue(user),
  findOneByEmail: jest.fn().mockResolvedValue(user),
  findOneByPhoneNumber: jest.fn().mockResolvedValue(user),
  createOne: jest.fn(),
  update: jest.fn(),
  findEmails: jest.fn().mockResolvedValue([user.email]),
  saveInvitedBy: jest.fn(),
  save: jest.fn().mockResolvedValue([user]),
};

const companyRepository: CompanyRepository = {
  findOneByTributaryIdAndCountry: jest.fn(),
  findManyByTributaryIdAndCountry: jest.fn(),
  save: jest.fn(),
  findManyByIds: jest.fn(),
  findOneById: jest.fn(),
};

const inventoryRepository: InventoryRepository = {
  findOneById: jest.fn().mockResolvedValue({}),
  findManyByIds: jest.fn().mockResolvedValue([]),
  save: jest.fn().mockResolvedValue({}),
  findManyBySkus: jest.fn().mockResolvedValue([]),
  findManyForCompanyByProviderSkus: jest.fn().mockResolvedValue([]),
  findManyForCompanyBySkus: jest.fn().mockResolvedValue([]),
};

const catalogRepository: CatalogRepository = {
  save: jest.fn().mockResolvedValue({}),
  findOneById: jest.fn().mockResolvedValue({}),
  findManyByIds: jest.fn().mockResolvedValue([]),
  findManyForCompanyByIds: jest.fn().mockResolvedValue([]),
  deleteById: jest.fn().mockResolvedValue(undefined),
  getIdsFromReadIds: jest.fn().mockResolvedValue([]),
  findPaginated: jest.fn().mockResolvedValue({}),
};

const catalogDiscountRepository: CatalogDiscountRepository = {
  findManyByIds: jest.fn().mockResolvedValue([]),
  deleteByIds: jest.fn().mockResolvedValue([]),
  findManyByClients: jest.fn().mockResolvedValue([]),
  saveMany: jest.fn().mockResolvedValue([]),
  findManyForCompaniesByCatalogIdsAndClientIds: jest.fn().mockResolvedValue([]),
};

const storeDiscountRepository: StoreDiscountRepository = {
  saveMany: jest.fn().mockResolvedValue([]),
  findManyByIds: jest.fn().mockResolvedValue([]),
  findManyByClients: jest.fn().mockResolvedValue([]),
  findManyForCompaniesByClientIds: jest.fn().mockResolvedValue([]),
  deleteByIds: jest.fn().mockResolvedValue([]),
};

const taxRepository: TaxRepository = {
  findManyByIds: jest.fn().mockResolvedValue([]),
};

const identificationService: IdentificationService = {
  generateId: jest.fn().mockResolvedValue('1'),
  generateObjectSequenceForCompany: jest.fn().mockResolvedValue('PREFIX-1'),
};

const transactionService: TransactionService = {
  transactional: jest.fn().mockImplementation((fn) => fn()),
};

const providerRepository: ProviderRepository = {
  saveMany: jest.fn().mockResolvedValue([]),
  findOneById: jest.fn().mockResolvedValue(undefined),
  deleteById: jest.fn().mockResolvedValue(undefined),
  save: jest.fn().mockResolvedValue({}),
  findOneByNameAndCompanyId: jest.fn().mockResolvedValue({}),
  findManyByNamesAndCompanyId: jest.fn().mockResolvedValue({}),
  findProvidersForCompany: jest.fn().mockResolvedValue([]),
  findProviderForCompanyByProviderCompanyId: jest.fn().mockResolvedValue({}),
};

const clientRepository: ClientRepository = {
  saveMany: jest.fn().mockResolvedValue([]),
  save: jest.fn().mockResolvedValue({}),
  findOneById: jest.fn().mockResolvedValue(undefined),
  findManyByIds: jest.fn().mockResolvedValue([]),
  findManyByClientCompanyIds: jest.fn().mockResolvedValue([]),
};

const catalogMediaRepository: CatalogMediaRepository = {
  findManyByIds: jest.fn().mockResolvedValue([]),
  findManyByCatalogId: jest.fn().mockResolvedValue([]),
  save: jest.fn().mockResolvedValue([]),
  deleteByIds: jest.fn().mockResolvedValue(undefined),
};

const inventoryMediaRepository: InventoryMediaRepository = {
  findManyByIds: jest.fn().mockResolvedValue([]),
  findManyByInventoryId: jest.fn().mockResolvedValue([]),
  save: jest.fn().mockResolvedValue([]),
  deleteByIds: jest.fn().mockResolvedValue(undefined),
};

const purchaseOrderRepository: PurchaseOrderRepository = {
  save: jest.fn().mockResolvedValue({}),
  findOneById: jest.fn().mockResolvedValue({}),
};

const saleOrderRepository: SaleOrderRepository = {
  save: jest.fn().mockResolvedValue({}),
  findOneById: jest.fn().mockResolvedValue({}),
};

const inventoryHistoryRepository: InventoryHistoryRepository = {
  save: jest.fn().mockResolvedValue([]),
};

const quoteRepository = {
  save: jest.fn().mockResolvedValue({}),
};

const invitationService = {
  save: jest.fn().mockResolvedValue({}),
  findOneById: jest.fn().mockResolvedValue({}),
};

const notificationService = {
  save: jest.fn().mockResolvedValue({}),
  deleteByInvitationId: jest.fn().mockResolvedValue(undefined),
};

const emailService = {
  sendInvitation: jest.fn().mockResolvedValue(undefined),
};

const messageService = {
  sendInvitation: jest.fn().mockResolvedValue(undefined),
};

const fileService = {
  deleteFromStorage: jest.fn().mockResolvedValue(undefined),
};

const integrationService: IntegrationService = {
  createIntegrations: jest.fn().mockResolvedValue({}),
  removeIntegration: jest.fn().mockResolvedValue({}),
  retrieveIntegrationById: jest.fn().mockResolvedValue({}),
  retrieveIntegrationsForCompany: jest.fn().mockResolvedValue([]),
};

const ImplementationRegistry: Registry = {
  UserRepository: userRepository,
  CompanyRepository: companyRepository,
  InventoryRepository: inventoryRepository,
  CatalogRepository: catalogRepository,
  CatalogDiscountRepository: catalogDiscountRepository,
  TaxRepository: taxRepository,
  IdentificationService: identificationService,
  TransactionService: transactionService,
  ProviderRepository: providerRepository,
  ClientRepository: clientRepository,
  StoreDiscountRepository: storeDiscountRepository,
  CatalogMediaRepository: catalogMediaRepository,
  PurchaseOrderRepository: purchaseOrderRepository,
  SaleOrderRepository: saleOrderRepository,
  QuoteRepository: quoteRepository,
  InventoryHistoryRepository: inventoryHistoryRepository,
  InventoryMediaRepository: inventoryMediaRepository,
  EmailService: emailService,
  FileService: fileService,
  MessageService: messageService,
  InvitationService: invitationService,
  NotificationService: notificationService,

  IntegrationService: integrationService,

};

export default ImplementationRegistry;
