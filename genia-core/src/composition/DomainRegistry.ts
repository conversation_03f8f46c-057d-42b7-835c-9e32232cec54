import DomainIdentificationService from '#domain/services/DomainIdentification.Service';
import CoreIdentificationService from '#infrastructure/implementations/invitation/CoreIdentification.Service';

// This type definitions should NEVER change, only implementations, new types can be added to the registry
export interface DomainRegistry {
  IdentificationService: DomainIdentificationService;
}

const DomainRegistry: DomainRegistry = {
  IdentificationService: CoreIdentificationService,

};

export default DomainRegistry;
