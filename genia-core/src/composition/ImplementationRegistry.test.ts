import Registry from './ImplementationRegistry';

jest.unmock('./ImplementationRegistry');
jest.unmock('pg');

describe('ImplementationRegistry', () => {
  it('should contain only the expected keys', () => {
    const expectedKeys = [
      'UserRepository',
      'CompanyRepository',
      'InventoryRepository',
      'IdentificationService',
      'TransactionService',
      'CatalogRepository',
      'CatalogDiscountRepository',
      'TaxRepository',
      'ProviderRepository',
      'ClientRepository',
      'StoreDiscountRepository',
      'CatalogMediaRepository',
      'PurchaseOrderRepository',
      'SaleOrderRepository',
      'QuoteRepository',
      'InventoryHistoryRepository',
      'InventoryMediaRepository',
      'EmailService',
      'FileService',
      'MessageService',
      'InvitationService',
      'NotificationService',
      'IntegrationService',

    ];
    const registryKeys = Object.keys(Registry);

    // Check if all expected keys are present
    expectedKeys.forEach((key) => {
      expect(registryKeys).toContain(key);
    });

    // Check if there are no unexpected keys
    expect(registryKeys.length).toBe(expectedKeys.length);
  });

  it('should have all implementations defined and not null', () => {
    const registryKeys = Object.keys(Registry);

    registryKeys.forEach((key) => {
      expect(Registry[key as keyof typeof Registry]).toBeDefined();
      expect(Registry[key as keyof typeof Registry]).not.toBeNull();
    });
  });

  it('should maintain consistency between interface and implementation', () => {
    // Verify that the registry object structure matches the Registry interface
    const registryKeys = Object.keys(Registry);

    // These are the keys that should exist based on the Registry interface
    const interfaceKeys = [
      'UserRepository',
      'CompanyRepository',
      'InventoryRepository',
      'CatalogRepository',
      'CatalogDiscountRepository',
      'ClientRepository',
      'TaxRepository',
      'IdentificationService',
      'TransactionService',
      'ProviderRepository',
      'StoreDiscountRepository',
      'CatalogMediaRepository',
      'PurchaseOrderRepository',
      'SaleOrderRepository',
      'QuoteRepository',
      'InventoryHistoryRepository',
      'InventoryMediaRepository',
      'EmailService',
      'FileService',
      'InvitationService',
      'NotificationService',
      'MessageService',
      'IntegrationService',
    ];

    expect(registryKeys.sort()).toEqual(interfaceKeys.sort());
  });
});
