const getSignedUrlMock = jest.fn().mockResolvedValue(['https://mock-signed-url.com']);
const deleteMock = jest.fn().mockResolvedValue(undefined);

const fileMock = jest.fn(() => ({
  getSignedUrl: getSignedUrlMock,
  delete: deleteMock,
}));

const mockGetFiles = jest.fn().mockResolvedValue([[fileMock()]]);

const bucketMock = jest.fn(() => ({
  file: fileMock,
  getFiles: mockGetFiles,
}));

class Storage {
  bucket = bucketMock;
}

export { Storage };
export default { Storage };
