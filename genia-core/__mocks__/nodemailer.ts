import * as nodemailer from 'nodemailer';
import { createTransport as createTransportNM } from 'nodemailer';

const sendMail = jest.fn().mockResolvedValue(undefined);

const createTransportMock = jest.fn().mockReturnValue({
  sendMail,
});

const nodemailerMock: Partial<typeof nodemailer> = {
  createTransport: createTransportMock as unknown as typeof createTransportNM,
};

export const { createTransport } = nodemailerMock;

export default nodemailerMock;
