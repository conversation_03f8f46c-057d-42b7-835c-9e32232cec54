export interface ClientsTextMap {
  client: string;
  clients: string;
  addClient: string;
  phonePlaceholder: string;
  // label used in AddClient.Module when creating a new client
  new: string;
  editDescription: string;
  // description shown when creating a new client
  createDescription: string;
  clientCompanyName: string;
  noCompanyName: string;
  tributaryId: string;
  noTributaryId: string;
  addFailed: string;
  updateFailed: string;
  updateSuccess: string;
  addSuccess: string;
  edit: string;
  // action labels
  saving: string;
  saveChanges: string;
  // Form labels
  mainInformation: string;
  mainInformationDescription: string;
  clientType: string;
  clientTypeDescription: string;
  personOption: string;
  companyOption: string;
  contactPersonName: string;
  firstName: string;
  lastName: string;
  companyName: string;
  tributaryIdLabel: string;
  tributaryIdPlaceholder: string;
  mainAddress: string;
  mainAddressPlaceholder: string;
  shippingAddress: string;
  shippingAddressPlaceholder: string;
  billingAddress: string;
  billingAddressPlaceholder: string;
  useMainAddress: string;
  clientAddress: string;
  clientAddressPlaceholder: string;
  email: string;
  emailPlaceholder: string;
  phone: string;
  isWhatsApp: string;
  whatsappNumber: string;
  whatsappPlaceholder: string;
  location: string;
  locationPlaceholder: string;
  alternativeContacts: string;
  alternativeContactsDescription: string;
  billing: string;
  purchases: string;
  emailLabel: string;
  phoneLabel: string;
  whatsappLabel: string;
  notes: string;
  notesDescription: string;
  notesPlaceholder: string;
  fullName: string;
  contactPersonPlaceholder: string;
  firstNamePlaceholder: string;
  lastNamePlaceholder: string;
  companyNamePlaceholder: string;
  addClientDescription: string;
  // Search / list
  searchPlaceholder: string;
  // Client details
  detailsTitle: string;
  detailsSubtitle: string;
  propertiesLabel: string;
  saveBtnLabel: string;
  storeDiscountListTooltip: string;
  labelsName: string;
  labelsTributaryId: string;
  labelsMedia: string;
  placeholdersName: string;
  placeholdersTributaryId: string;
  tooltipsName: string;
  tooltipsTributaryId: string;
  tooltipsMedia: string;
  contact: string;
  loadError: string;
  addNewClientButton: string;
  // Dynamic phone labels
  phoneWhatsAppLabel: string;
  phoneOnlyLabel: string;
  // Email placeholders for alternative contacts
  billingEmailPlaceholder: string;
  purchasesEmailPlaceholder: string;
}

export const ClientsTextMap = {
  client: {
    spanish: 'Cliente',
  },
  clients: {
    spanish: 'Clientes',
  },
  addClient: {
    spanish: 'Nuevo Cliente',
  },
  new: {
    spanish: 'Nuevo Cliente',
  },
  createDescription: {
    spanish: 'Crea un nuevo cliente en el sistema',
  },
  saving: {
    spanish: 'Guardando...',
  },
  saveChanges: {
    spanish: 'Guardar cambios',
  },
  clientCompanyName: {
    spanish: 'Nombre de la Empresa',
  },
  noCompanyName: {
    spanish: 'Sin empresa asociada',
  },
  tributaryId: {
    spanish: 'ID Tributario',
  },
  noTributaryId: {
    spanish: 'Sin ID tributario',
  },
  addFailed: {
    spanish: 'Error al agregar el cliente',
  },
  updateFailed: {
    spanish: 'Error al actualizar el cliente',
  },
  updateSuccess: {
    spanish: 'Cliente actualizado con éxito',
  },
  addSuccess: {
    spanish: 'Cliente agregado con éxito',
  },
  edit: {
    spanish: 'Editar Cliente',
  },
  // Form labels
  mainInformation: {
    spanish: 'Información Principal',
  },
  mainInformationDescription: {
    spanish: 'Datos principales del cliente',
  },
  clientType: {
    spanish: 'Tipo de cliente',
  },
  clientTypeDescription: {
    spanish: 'Selecciona si es una persona o empresa',
  },
  personOption: {
    spanish: 'Persona',
  },
  companyOption: {
    spanish: 'Empresa',
  },
  contactPersonName: {
    spanish: 'Nombre de encargado',
  },
  firstName: {
    spanish: 'Nombre',
  },
  lastName: {
    spanish: 'Apellido',
  },
  companyName: {
    spanish: 'Nombre de la empresa',
  },
  tributaryIdLabel: {
    spanish: 'ID Tributario',
  },
  tributaryIdPlaceholder: {
    spanish: 'Ingresa el ID tributario de la empresa',
  },
  mainAddress: {
    spanish: 'Dirección de la Empresa',
  },
  mainAddressPlaceholder: {
    spanish: 'Dirección completa de la empresa',
  },
  shippingAddress: {
    spanish: 'Dirección de envío',
  },
  shippingAddressPlaceholder: {
    spanish: 'Dirección donde se deben enviar los pedidos',
  },
  billingAddress: {
    spanish: 'Dirección de facturación',
  },
  billingAddressPlaceholder: {
    spanish: 'Dirección usada para facturación',
  },
  useMainAddress: {
    spanish: 'Usar dirección de la empresa para envío y facturación',
  },
  clientAddress: {
    spanish: 'Dirección del cliente',
  },
  clientAddressPlaceholder: {
    spanish: 'Dirección completa',
  },
  email: {
    spanish: 'Email principal',
  },
  emailPlaceholder: {
    spanish: '<EMAIL>',
  },
  phone: {
    spanish: 'Teléfono / WhatsApp principal',
  },
  phonePlaceholder: {
    spanish: '+57 ************',
  },
  isWhatsApp: {
    spanish: '¿Este número es también WhatsApp?',
  },
  whatsappNumber: {
    spanish: 'Número de WhatsApp',
  },
  whatsappPlaceholder: {
    spanish: '+57 ************',
  },
  location: {
    spanish: 'Ubicación',
  },
  locationPlaceholder: {
    spanish: 'Ciudad, Estado, País',
  },
  alternativeContacts: {
    spanish: 'Información de contacto por area de negocio',
  },
  alternativeContactsDescription: {
    spanish: `
      Puedes agregar email, teléfono y WhatsApp alternativos para Facturación y Compras. 
      Si algún contacto alternativo no está definido, se usará el contacto principal del cliente.
    `,
  },
  billing: {
    spanish: 'Facturación',
  },
  purchases: {
    spanish: 'Compras',
  },
  emailLabel: {
    spanish: 'Email',
  },
  phoneLabel: {
    spanish: 'Teléfono',
  },
  whatsappLabel: {
    spanish: 'WhatsApp',
  },
  notes: {
    spanish: 'Notas',
  },
  notesDescription: {
    spanish: 'Información adicional sobre el cliente',
  },
  notesPlaceholder: {
    spanish: 'Escribe notas adicionales sobre el cliente...',
  },
  fullName: {
    spanish: 'Nombre completo',
  },
  contactPersonPlaceholder: {
    spanish: 'Nombre del encargado',
  },
  firstNamePlaceholder: {
    spanish: 'Nombre',
  },
  lastNamePlaceholder: {
    spanish: 'Apellido',
  },
  companyNamePlaceholder: {
    spanish: 'Nombre de la empresa',
  },
  addClientDescription: {
    spanish: 'Crea un nuevo cliente en el sistema',
  },
  editDescription: {
    spanish: 'Edita los datos del cliente',
  },
  // Client detail labels
  detailsTitle: {
    spanish: 'Detalles del Cliente',
  },
  detailsSubtitle: {
    spanish: 'Información completa y historial de actividad',
  },
  // Search / list
  searchPlaceholder: {
    spanish: 'Buscar por nombre, email o empresa...',
  },
  propertiesLabel: {
    spanish: 'Propiedades',
  },
  saveBtnLabel: {
    spanish: 'Guardar',
  },
  storeDiscountListTooltip: {
    // eslint-disable-next-line max-len
    spanish: 'Esta es la lista de descuentos de tienda que se han creado para este cliente. Puedes ver los detalles de cada descuento haciendo clic en el nombre del descuento.',
  },
  labelsName: {
    spanish: 'Nombre',
  },
  labelsTributaryId: {
    spanish: 'ID Tributario',
  },
  labelsMedia: {
    spanish: 'Agregar Imagen',
  },
  placeholdersName: {
    spanish: 'Ejemplo: Cliente marca Acme',
  },
  placeholdersTributaryId: {
    spanish: 'Ejemplo: XAXX010101000',
  },
  tooltipsName: {
    spanish: 'Ingresa el nombre de tu cliente. Ejemplo: Cliente marca Acme',
  },
  tooltipsTributaryId: {
    spanish: 'Ingresa el Identificador Tributario de tu cliente de acuerdo a las leyes fiscales de tu país. Ejemplo: XAXX010101000',
  },
  tooltipsMedia: {
    spanish: 'Sube una imagen clara y representativa de la identidad gráfica de tu cliente. Funcionalidad temporalmente no disponible',
  },
  contact: {
    spanish: 'CONTACTO',
  },
  loadError: {
    spanish: 'No se pudo cargar la información del cliente.',
  },
  addNewClientButton: {
    spanish: 'Agregar Cliente',
  },
  // Dynamic phone labels
  phoneWhatsAppLabel: {
    spanish: 'Teléfono / WhatsApp principal',
  },
  phoneOnlyLabel: {
    spanish: 'Teléfono',
  },
  // Email placeholders for alternative contacts
  billingEmailPlaceholder: {
    spanish: '<EMAIL>',
  },
  purchasesEmailPlaceholder: {
    spanish: '<EMAIL>',
  },
};
