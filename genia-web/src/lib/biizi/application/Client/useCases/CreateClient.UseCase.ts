import ClientEntity, { Client } from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import ApplicationRegistry from '#composition/Application.Registry';

async function apply(params: Omit<Client, 'createdAt' | 'updatedAt' | 'id'>): Promise<ClientEntity> {
  const newClient = await ApplicationRegistry.ClientRepository.createClient(params);

  return newClient;
}

const CreateClientUseCase = {
  apply,
};

export default CreateClientUseCase;
