import ClientEntity, { Client } from '#/lib/biizi/domain/aggregates/Client/Client.Entity';

export interface ClientRepositoryCreateCommands extends Omit<Client, 'createdAt' | 'updatedAt' | 'id'> {}

export default interface ClientRepository {
  createClient(params: ClientRepositoryCreateCommands): Promise<ClientEntity>;
  findAllClients(): Promise<ClientEntity[]>;
  updateClient(params: { id: string; params: Partial<Client> }): Promise<ClientEntity>;
}
