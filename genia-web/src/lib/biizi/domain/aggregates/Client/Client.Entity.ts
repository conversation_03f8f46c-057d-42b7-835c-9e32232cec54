export enum ClientType {
  CLIENT = 'client',
  COMPANY = 'company'
}

export type ContactInformation = {
  mainEmail: string;
  mainPhoneNumber?: string;
  mainWhatsapp?: string;
  mainAddress?: string;
  representativeName?: string;
  billingEmail?: string;
  billingPhoneNumber?: string;
  billingWhatsapp?: string;
  billingAddress?: string;
  purchasesEmail?: string;
  purchasesPhoneNumber?: string;
  purchasesWhatsapp?: string;
  salesEmail?: string;
  salesPhoneNumber?: string;
  salesWhatsapp?: string;
  shippingAddress?: string;
};

export type Client = {
  id: string;
  name: string;
  tributaryId?: string | null;
  clientCompany?: { name?: string } | null;
  contactInformation?: Partial<ContactInformation> | null;
  city?: string;
  status?: 'activo' | 'prospecto' | 'inactivo';
  conversations: number;
  tags?: string[];
  lastContact?: string;
  createdAt?: string;
  updatedAt?: string;
  notes?: string;
  clientCompanyId?: string | null;
};

export default class ClientEntity {
  id: string;

  name: string;

  tributaryId: string;

  clientCompanyId: string | null;

  storeDiscounts: string[];

  contactInformation: ContactInformation;

  createdAt: Date;

  updatedAt: Date;

  constructor(
    id: string,
    name: string,
    tributaryId: string,
    clientCompanyId: string | null,
    storeDiscounts: string[],
    contactInformation: ContactInformation,
    createdAt: Date,
    updatedAt: Date,
  ) {
    this.id = id;
    this.name = name;
    this.tributaryId = tributaryId;
    this.clientCompanyId = clientCompanyId;
    this.storeDiscounts = storeDiscounts;
    this.contactInformation = contactInformation;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
