import { Button } from '@pitsdepot/storybook';
import { useEffect } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import { ClientType } from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import { ClientFormComponent } from '#/lib/biizi/ui/components/ClientForm.Component';
import { ClientFormData, ClientFormProvider, ClientPayload } from '#/lib/biizi/ui/contexts/ClientFormContext';
import { useClientForm, useCreateClient, useUpdateClient } from '#/lib/biizi/ui/hooks/Client.Hook';
import { useGetClient } from '#/lib/biizi/ui/hooks/useGetClient.Hook';
import { Notification } from '#appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';

type LocationState = {
  initialFormData?: {
    id?: string;
    isEdit?: boolean;
  };
};

function buildApiPayload(formPayload: ClientPayload) {
  const nonEmpty = (val?: string) => (val && val.trim() !== '' ? val : undefined);

  const name = formPayload.clientType === ClientType.COMPANY
    ? (formPayload.companyName || '')
    : `${formPayload.firstName} ${formPayload.lastName}`.trim();

  const tributaryId = formPayload.clientType === ClientType.COMPANY
    ? nonEmpty(formPayload.tributaryId)
    : nonEmpty(`${formPayload.firstName}${formPayload.lastName}`.toLowerCase().replace(/\s+/g, ''));

  // Determine main phone and main whatsapp according to isWhatsApp flag
  const mainPhoneNumberToSend = nonEmpty(formPayload.mainPhoneNumber) || undefined;
  const mainWhatsappToSend = formPayload.isWhatsApp
    ? (nonEmpty(formPayload.mainPhoneNumber) || undefined)
    : (nonEmpty(formPayload.whatsappNumber) || undefined);

  return {
    name,
    tributaryId: nonEmpty(tributaryId || ''),
    notes: nonEmpty(formPayload.notes),
    clientCompanyId: undefined,
    storeDiscounts: [],
    conversations: 0,
    contactInformation: {
      // billing
      billingEmail: nonEmpty(formPayload.alternativeContacts?.billing?.email),
      billingPhoneNumber: nonEmpty(formPayload.alternativeContacts?.billing?.phone),
      // Only use the explicit Billing Whatsapp field from the Billing section
      billingWhatsapp: nonEmpty(formPayload.alternativeContacts?.billing?.whatsapp) || undefined,
      // purchases: take only the explicit purchases fields from the Purchases section
      purchasesEmail: nonEmpty(formPayload.alternativeContacts?.purchases?.email) || undefined,
      purchasesPhoneNumber: nonEmpty(formPayload.alternativeContacts?.purchases?.phone) || undefined,
      purchasesWhatsapp: nonEmpty(formPayload.alternativeContacts?.purchases?.whatsapp) || undefined,
      // Do not send any sales_* fields �" moved primary contacts to mainEmail/mainPhoneNumber
      mainEmail: nonEmpty(formPayload.email) || undefined,
      // explicit main/company phone
      mainPhoneNumber: mainPhoneNumberToSend,
      mainWhatsapp: mainWhatsappToSend,
      // representative name (contact person)
      representativeName: nonEmpty(formPayload.contactPerson) || undefined,
      // explicit main/company address
      mainAddress: nonEmpty(formPayload.mainAddress) || undefined,
      // Prefer explicit shippingAddress from form, fallback to mainAddress or older clientAddress
      shippingAddress: nonEmpty(formPayload.shippingAddress) || nonEmpty(formPayload.mainAddress) || undefined,
      // Use explicit billingAddress when provided; otherwise fallback to mainAddress then shippingAddress
      billingAddress: nonEmpty(formPayload.billingAddress) || nonEmpty(formPayload.mainAddress) || nonEmpty(formPayload.shippingAddress) || undefined,
    },
  };
}

function validatePayload(apiPayload: ReturnType<typeof buildApiPayload>, formPayload: ClientPayload): boolean {
  if (!apiPayload.name || apiPayload.name.trim() === '') {
    Notification({ message: 'El nombre es requerido.', type: MSG_ERROR_TYPES.ERROR });
    return false;
  }

  if (!formPayload.email || !formPayload.email.trim()) {
    Notification({ message: 'El email principal es requerido.', type: MSG_ERROR_TYPES.ERROR });
    return false;
  }
  return true;
}

function populateFormWithRemoteData(
  remoteClient: Record<string, unknown>,
): Partial<ClientFormData> {
  const rc = remoteClient as Record<string, unknown>;

  // Try multiple locations where company name might be present
  const companyNameCandidates = [
    (rc.client_company as Record<string, unknown>)?.name,
    rc.client_company_name,
    (rc.clientCompany as Record<string, unknown>)?.name,
    // sometimes the read-model may return an array or nested structure
    Array.isArray(rc.client_company) ? (rc.client_company as unknown as Record<string, unknown>[])[0]?.name : undefined,
    // fallback to the client name itself
    rc.name,
  ];

  const companyNameFromRemote = companyNameCandidates.find((c) => typeof c === 'string' && c && c.trim()) || '';

  // As a last resort, prefer the top-level name field if present
  const topLevelName = typeof rc.name === 'string' && rc.name.trim() ? (rc.name as string) : '';
  const finalCompanyName = companyNameFromRemote || topLevelName;

  const tributaryIdFromRemote = (rc.tributaryId as string) || (rc.tributary_id as string) || '';

  // Normalize contact object (read-model sometimes returns snake_case or camelCase)
  const contact = (rc.contactInformation as Record<string, unknown>) || {};

  // Helper to pick the first non-empty string from multiple possible keys
  const pick = (...keys: string[]) => keys
    .map((k) => (contact as Record<string, unknown>)[k] ?? (rc as Record<string, unknown>)[k])
    .find((v) => typeof v === 'string' && v && (v as string).trim()) as string | undefined;

  const pickTop = (...keys: string[]) => keys
    .map((k) => (rc as Record<string, unknown>)[k])
    .find((v) => typeof v === 'string' && v && (v as string).trim()) as string | undefined;

  const emailFromRemote = (pick('mainEmail', 'main_email')
    || pick('billingEmail', 'billing_email')
    || pick('purchasesEmail', 'purchases_email')
    || pickTop('email')) || '';

  const mainPhoneFromRemote = (pick('mainPhoneNumber', 'main_phone_number')
    || pick('purchasesPhoneNumber', 'purchases_phone_number')
    || pickTop('phone')
    || pick('billingPhoneNumber', 'billing_phone_number')) || '';

  const mainAddressFromRemote = (pick('mainAddress', 'main_address')) || '';
  const shippingAddressFromRemote = (pick('shippingAddress', 'shipping_address')) || '';
  const billingAddressFromRemote = (pick('billingAddress', 'billing_address')) || '';

  const representativeNameFromRemote = (pick('representativeName', 'representative_name')) || '';

  const billingEmailFromRemote = (pick('billingEmail', 'billing_email')) || '';
  const billingPhoneFromRemote = (pick('billingPhoneNumber', 'billing_phone_number', 'billingPhone')) || '';
  const billingWhatsappFromRemote = (pick('billingWhatsapp', 'billing_whatsapp', 'billing_whatsapp_number')) || '';

  const purchasesEmailFromRemote = (pick('purchasesEmail', 'purchases_email')) || '';
  const purchasesPhoneFromRemote = (pick('purchasesPhoneNumber', 'purchases_phone_number', 'purchasesPhone')) || '';
  const purchasesWhatsappFromRemote = (pick('purchasesWhatsapp', 'purchases_whatsapp', 'purchases_whatsapp_number')) || '';

  const salesWhatsappFromRemote = (pick('salesWhatsapp', 'sales_whatsapp', 'salesWhatsapp')) || '';
  const mainWhatsappFromRemote = (pick('mainWhatsapp', 'main_whatsapp') || purchasesWhatsappFromRemote || salesWhatsappFromRemote) || '';

  const notesFromRemote = (rc.notes as string) || '';
  const tagsFromRemote = Array.isArray(rc.tags) ? rc.tags : [];

  const normalizeDigits = (v?: string) => (v ? (v as string).replace(/[^\d]/g, '') : '');

  const prefill: Partial<ClientFormData> = {};

  if (finalCompanyName) prefill.clientType = ClientType.COMPANY;

  // Ensure finalCompanyName is a string before assigning to form field
  prefill.companyName = (typeof finalCompanyName === 'string' && finalCompanyName) ? finalCompanyName : undefined;
  prefill.tributaryId = tributaryIdFromRemote || undefined;

  // Prefer authoritative mainEmail when present
  prefill.email = emailFromRemote || undefined;

  // Main phone should prefer mainPhoneNumber, then purchases, then top-level phone, then billing
  prefill.mainPhoneNumber = mainPhoneFromRemote || undefined;

  // Keep addresses separate: main, shipping and billing must map to distinct fields
  prefill.mainAddress = mainAddressFromRemote || undefined;
  prefill.shippingAddress = shippingAddressFromRemote || (mainAddressFromRemote || undefined);
  prefill.billingAddress = billingAddressFromRemote || undefined;

  // Representative / contact person
  prefill.contactPerson = representativeNameFromRemote || undefined;

  prefill.notes = notesFromRemote || undefined;
  prefill.tags = tagsFromRemote as string[];

  // Alternative contacts
  prefill.billingEmail = billingEmailFromRemote || undefined;
  prefill.billingPhone = billingPhoneFromRemote || undefined;
  prefill.billingWhatsApp = billingWhatsappFromRemote || undefined;

  prefill.purchasesEmail = purchasesEmailFromRemote || undefined;
  prefill.purchasesPhone = purchasesPhoneFromRemote || undefined;
  prefill.purchasesWhatsApp = purchasesWhatsappFromRemote || undefined;

  // Whatsapp handling: if mainWhatsapp equals mainPhone (digits) -> isWhatsApp true
  const mainPhoneDigits = normalizeDigits(prefill.mainPhoneNumber);
  const mainWhatsappDigits = normalizeDigits(mainWhatsappFromRemote || prefill.billingWhatsApp || '');

  const isMainWhatsApp = mainPhoneDigits && mainWhatsappDigits && mainPhoneDigits === mainWhatsappDigits;

  if (isMainWhatsApp) {
    prefill.isWhatsApp = true;
    // clear separate whatsappNumber because it's the same as main
    prefill.whatsappNumber = '';
    // keep mainWhatsapp field for transparency
    prefill.mainWhatsapp = prefill.mainPhoneNumber || '';
  } else if (mainWhatsappFromRemote) {
    // there is a whatsapp number but it differs from main phone -> store it as whatsappNumber
    prefill.isWhatsApp = false;
    prefill.whatsappNumber = mainWhatsappFromRemote || undefined;
    prefill.mainWhatsapp = mainWhatsappFromRemote || undefined;
  } else {
    // fallback: if billing whatsapp matches billing phone, mark isWhatsApp
    const billingPhoneDigits = normalizeDigits(prefill.billingPhone);
    const billingWhatsappDigits = normalizeDigits(prefill.billingWhatsApp);
    const isBillingWhatsApp = billingPhoneDigits && billingWhatsappDigits && billingPhoneDigits === billingWhatsappDigits;
    prefill.isWhatsApp = isBillingWhatsApp || undefined;
    prefill.whatsappNumber = isBillingWhatsApp ? '' : (prefill.whatsappNumber || undefined);
  }

  return prefill;
}

function AddClientContent() {
  const { getPayload, resetForm, updateField } = useClientForm();
  const navigate = useNavigate();
  const location = useLocation() as { state?: LocationState };
  const params = new URLSearchParams(window.location.search);
  const routeParams = useParams<{ id?: string }>();
  const idFromUrl = params.get('id') || routeParams.id || undefined;

  const editingIdFromState = location.state?.initialFormData?.id;

  const editingId = idFromUrl || editingIdFromState;

  const isEditMode = Boolean(editingId) || Boolean(location.state?.initialFormData?.isEdit);

  const text = TextService.getText();

  const createHook = useCreateClient();
  const updateHook = useUpdateClient();
  const data = isEditMode ? updateHook.data : createHook.data;
  const isLoading = isEditMode ? updateHook.isLoading : createHook.isLoading;
  const error = isEditMode ? updateHook.error : createHook.error;
  const { client: remoteClient } = useGetClient(editingId, Boolean(editingId));

  const hasIncomingState = Boolean(location.state?.initialFormData);

  useEffect(() => {
    if (isEditMode && editingId && !hasIncomingState && remoteClient) {
      const prefillData = populateFormWithRemoteData(remoteClient as unknown as Record<string, unknown>);
      const keys = Object.keys(prefillData) as Array<keyof ClientFormData>;
      keys.forEach((key) => {
        const value = prefillData[key] as ClientFormData[typeof key];
        updateField(key, value);
      });
    }
  }, [isEditMode, editingId, hasIncomingState, remoteClient, updateField]);

  const handleSaveClick = async () => {
    try {
      const formPayload = getPayload();
      const apiPayload = buildApiPayload(formPayload);

      if (!validatePayload(apiPayload, formPayload)) {
        return;
      }

      if (isEditMode && editingId) {
        updateHook.apply({ id: editingId, params: apiPayload });
      } else {
        createHook.apply(apiPayload);
      }
    } catch (err) {
      Notification({ message: 'Error al crear el cliente. Por favor, intenta nuevamente.', type: MSG_ERROR_TYPES.ERROR });
    }
  };

  useEffect(() => {
    if (data) {
      resetForm();
      navigate(isEditMode && editingId ? ApplicationRegistry.PathService.clients.viewClient(editingId) : '/app/clients');
    }
  }, [createHook.data, updateHook.data, navigate, resetForm, isEditMode, editingId]);

  return (
    <div className="min-h-screen bg-transparent p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between w-full mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{isEditMode ? text.clients.edit : (text.clients?.new ?? 'Nuevo Cliente')}</h1>
            <p className="text-sm text-gray-500 mt-1">
              {isEditMode
                ? (text.clients?.editDescription)
                : (text.clients?.createDescription)}
            </p>

          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outlined"
              size="small"
              onClick={() => navigate(-1)}
              disabled={isLoading}
            >
              {text.common?.toBack}
            </Button>

            <Button
              variant="primary"
              size="small"
              onClick={handleSaveClick}
              disabled={isLoading}
            >
              {isLoading ? (text.clients?.saving) : (text.clients?.saveChanges)}
            </Button>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error.message}</p>
          </div>
        )}

        <ClientFormComponent />
      </div>
    </div>
  );
}

export function AddClientModule() {
  return (
    <ClientFormProvider>
      <AddClientContent />
    </ClientFormProvider>
  );
}
