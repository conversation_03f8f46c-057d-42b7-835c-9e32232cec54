import { Avatar, IconImporter } from '@pitsdepot/storybook';
import { Link } from 'react-router-dom';

import { Client } from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import ApplicationRegistry from '#composition/Application.Registry';
import TextService from '#composition/textService/Text.Service';

export default function ClientCard({ client }: { client: Client }) {
  const textService = TextService.getText();

  const contactInfo = client.contactInformation;

  const getStr = (obj: unknown, ...keys: string[]) => {
    const o = obj as Record<string, unknown> | undefined;
    if (!o) return null;
    const foundKey = keys.find((k) => {
      const v = o[k];
      return typeof v === 'string' && (v as string).trim() !== '';
    });

    return foundKey ? (o[foundKey] as string) : null;
  };

  const emailMain = getStr(contactInfo, 'mainEmail');
  const emailFallbacks = getStr(contactInfo, 'billingEmail', 'purchasesEmail');
  const email = emailMain || emailFallbacks || getStr(client as unknown as Record<string, unknown>, 'email');

  const phoneMain = getStr(contactInfo, 'mainPhoneNumber');
  const phoneFallback = getStr(contactInfo, 'billingPhoneNumber');
  const phone = phoneMain || phoneFallback || getStr(client as unknown as Record<string, unknown>, 'phone');

  const addressMain = getStr(contactInfo, 'mainAddress');
  const addressBilling = getStr(contactInfo, 'billingAddress');
  const addressShipping = getStr(contactInfo, 'shippingAddress');
  const address = addressMain || addressBilling || addressShipping || getStr(client as unknown as Record<string, unknown>, 'city');

  // const statusLabel = (client.status || 'activo').toString();

  return (
    <div className="bg-white rounded-lg border p-4 shadow-sm h-full relative">
      {/* Status badge top-right (commented out)
      <div className="absolute top-3 right-3">
        <span
          className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
            statusLabel.toLowerCase() === 'activo' || statusLabel.toLowerCase() === 'active'
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {statusLabel}
        </span>
      </div>
      */}

      <div className="flex items-stretch gap-4 h-full">
        <Avatar
          name={client.name.toUpperCase()}
          className="w-12 h-12 rounded-full"
        />

        <div className="flex-1 flex flex-col">
          <div>
            <div className="flex items-center justify-between gap-2">
              <div>
                <div className="font-semibold text-gray-900">{client.name}</div>
                {client.clientCompany?.name && <div className="text-sm text-gray-500">{client.clientCompany.name}</div>}
              </div>
            </div>

            <div className="mt-3 text-sm text-gray-600 space-y-1">
              <div className="flex items-center gap-2">
                <IconImporter name="paperPlaneRight" size={16} className="text-dark-500" aria-hidden />
                {email ? (
                  <span>{email}</span>
                ) : (
                  <span className="text-gray-400">{textService.common.noInformation}</span>
                )}
              </div>

              <div className="flex items-center gap-2">
                <IconImporter name="phone" size={16} className="text-dark-500" aria-hidden />
                {phone ? (
                  <span>{phone}</span>
                ) : (
                  <span className="text-gray-400">{textService.common.noInformation}</span>
                )}
              </div>

              <div className="flex items-center gap-2">
                <IconImporter name="addressBook" size={16} className="text-dark-500" aria-hidden />
                {address ? (
                  <span>{address}</span>
                ) : (
                  <span className="text-gray-400">{textService.common.noInformation}</span>
                )}
              </div>
            </div>

            {/* conversaciones row (count + fecha) (commented out)
            <div className="mt-4 border-t pt-3 flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center gap-3">
                <span>{client.conversations ?? 0} conversaciones</span>
              </div>

              <div className="flex items-center gap-2 text-sm text-gray-400">
                <IconImporter name="calendar" size={16} className="text-dark-300" aria-hidden />
                <span>{client.lastContact ?? ''}</span>
              </div>
            </div>
            */}

            <div className="mt-3 flex items-center gap-2 flex-wrap">
              {(client.tags || []).map((t) => (
                <span key={t} className="text-xs px-2 py-1 rounded-full bg-fadedGray text-dark-700">
                  {t}
                </span>
              ))}
            </div>
          </div>

          <div className="mt-auto self-end flex gap-3">
            <Link to={ApplicationRegistry.PathService.clients.viewClient(client.id)} className="px-3 py-2 rounded border text-sm text-gray-700 bg-primary">
              Ver Detalles
            </Link>

            {/* Conversaciones button (commented out)
            <button type="button" className="px-3 py-2 rounded text-sm bg-slate-900 text-white">
              Conversaciones
            </button>
            */}
          </div>
        </div>
      </div>
    </div>
  );
}
