import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
  FormInputType,
  InputComponent,
  Label,
  TextAreaInput,
} from '@pitsdepot/storybook';
import { motion } from 'framer-motion';
import { useCallback, useEffect } from 'react';

import { ClientType } from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import { useClientForm } from '#/lib/biizi/ui/hooks/Client.Hook';
import textService from '#/lib/composition/textService/Text.Service';

export function ClientFormComponent() {
  const {
    formData, updateField,
  } = useClientForm();

  const text = textService.getText();

  const sanitizePhone = useCallback((val?: string) => {
    if (!val) return '';
    const hasPlus = /^\+/.test(val);
    const digits = val.replace(/[^\d]/g, '');
    return hasPlus ? `+${digits}` : digits;
  }, []);

  const handleInputChange = useCallback(<K extends keyof typeof formData>(field: K, value: typeof formData[K]) => {
    updateField(field, value);
  }, [updateField]);

  const setFields = useCallback((fields: Partial<Record<keyof typeof formData, typeof formData[keyof typeof formData]>>) => {
    Object.entries(fields).forEach(([key, value]) => {
      const field = key as keyof typeof formData;
      if (value === undefined) return;
      const normalized = value === null ? '' : value;
      handleInputChange(field, normalized as typeof formData[keyof typeof formData]);
    });
  }, [handleInputChange]);

  const onPhoneChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const val = sanitizePhone(e.target.value) as typeof formData.mainPhoneNumber;
    if (formData.isWhatsApp) {
      setFields({ mainPhoneNumber: val, mainWhatsapp: val });
    } else {
      setFields({ mainPhoneNumber: val });
    }
  }, [sanitizePhone, formData.isWhatsApp, setFields]);

  const onWhatsappNumberChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const val = sanitizePhone(e.target.value) as typeof formData.whatsappNumber;
    if (!formData.isWhatsApp) {
      setFields({ whatsappNumber: val, mainWhatsapp: val });
    } else {
      setFields({ whatsappNumber: val });
    }
  }, [sanitizePhone, formData.isWhatsApp, setFields]);

  const handleIsWhatsAppChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { checked } = e.target;
    setFields({ isWhatsApp: checked, mainWhatsapp: checked ? formData.mainPhoneNumber : formData.whatsappNumber });
  }, [formData.mainPhoneNumber, formData.whatsappNumber, setFields]);

  const onBillingPhoneChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const val = sanitizePhone(e.target.value) as typeof formData.billingPhone;
    setFields({ billingPhone: val });
  }, [sanitizePhone, setFields]);

  const onBillingWhatsAppChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const val = sanitizePhone(e.target.value) as typeof formData.billingWhatsApp;
    setFields({ billingWhatsApp: val });
  }, [sanitizePhone, setFields]);

  const onPurchasesPhoneChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const val = sanitizePhone(e.target.value) as typeof formData.purchasesPhone;
    setFields({ purchasesPhone: val });
  }, [sanitizePhone, setFields]);

  const onPurchasesWhatsAppChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const val = sanitizePhone(e.target.value) as typeof formData.purchasesWhatsApp;
    setFields({ purchasesWhatsApp: val });
  }, [sanitizePhone, setFields]);

  useEffect(() => {
    if (formData.useMainAddress) {
      setFields({ shippingAddress: formData.mainAddress, billingAddress: formData.mainAddress });
    }
  }, [formData.useMainAddress, formData.mainAddress, setFields]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.4 }}
      className="max-w-4xl mx-auto space-y-6"
    >
      <Card className='bg-white'>
        <CardHeader >
          <CardTitle className="text-lg font-semibold text-gray-900">
            {text.clients.mainInformation}
          </CardTitle>
          <p className="text-sm text-gray-600">{text.clients.mainInformationDescription}</p>
        </CardHeader>
        <CardContent className="space-y-6">

          <div className="grid grid-cols-1 gap-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {text.clients.clientType}
                </Label>
                <DropdownSimple
                  options={clientTypeOptions}
                  setSelectedOption={(option: OptionsDropdownProps) => handleInputChange('clientType', option.id as ClientType)}
                  showAvatar={false}
                >
                  <div
                    className="w-full px-3 py-[6px] border border-gray-300 rounded-md
                    focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer
                    flex items-center justify-between"
                  >
                    <span>{clientTypeOptions.find((type) => type.id === formData.clientType)?.name}</span>
                    <IconImporter name="caretDown" />
                  </div>
                </DropdownSimple>
              </div> */}

              {formData.clientType === ClientType.COMPANY ? (
                <div className="space-y-2 md:col-span-3">
                  <Label className="text-sm font-medium text-gray-700">
                    {text.clients.contactPersonName}
                  </Label>
                  <InputComponent
                    name="contactPerson"
                    placeholder={text.clients.contactPersonName}
                    value={formData.contactPerson || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ contactPerson: e.target.value })}
                  />
                </div>
              ) : (
                <div className="space-y-2 md:col-span-3">
                  <Label className="text-sm font-medium text-gray-700">
                    {text.clients.fullName}
                  </Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <InputComponent
                        name="firstName"
                        placeholder={text.clients.firstNamePlaceholder}
                        value={formData.firstName}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ firstName: e.target.value })}
                      />
                    </div>
                    <div className="space-y-1">
                      <InputComponent
                        name="lastName"
                        placeholder={text.clients.lastNamePlaceholder}
                        value={formData.lastName}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ lastName: e.target.value })}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {formData.clientType === ClientType.COMPANY && (
              <>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {text.clients.companyName} <span className="text-red-600">*</span>
                  </Label>
                  <InputComponent
                    name="companyName"
                    placeholder={text.clients.companyNamePlaceholder}
                    value={formData.companyName || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ companyName: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {text.clients.tributaryIdLabel}
                  </Label>
                  <InputComponent
                    name="tributaryId"
                    placeholder={text.clients.tributaryIdPlaceholder}
                    value={formData.tributaryId || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ tributaryId: e.target.value })}
                  />
                </div>
              </>
            )}
          </div>

          <div className="space-y-2 md:col-span-2">
            <Label className="text-sm font-medium text-gray-700">
              {text.clients.mainAddress}
            </Label>
            <InputComponent
              name="mainAddress"
              placeholder={text.clients.mainAddressPlaceholder}
              value={formData.mainAddress}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ mainAddress: e.target.value })}
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="useMainAddress"
              checked={formData.useMainAddress}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const { checked } = e.target;
                setFields({ useMainAddress: checked });
                if (checked) {
                  setFields({ shippingAddress: formData.mainAddress, billingAddress: formData.mainAddress });
                }
              }}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <Label htmlFor="useMainAddress" className="text-sm font-medium text-gray-700">{text.clients.useMainAddress}</Label>
          </div>

          <div className="space-y-2 md:col-span-2">
            <Label className="text-sm font-medium text-gray-700">
              {text.clients.shippingAddress}
            </Label>
            <InputComponent
              name="shippingAddress"
              placeholder={text.clients.shippingAddressPlaceholder}
              value={formData.shippingAddress}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ shippingAddress: e.target.value })}
              disabled={formData.useMainAddress}
            />
          </div>

          <div className="space-y-2 md:col-span-2">
            <Label className="text-sm font-medium text-gray-700">
              {text.clients.billingAddress}
            </Label>
            <InputComponent
              name="billingAddress"
              placeholder={text.clients.billingAddressPlaceholder}
              value={formData.billingAddress}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ billingAddress: e.target.value })}
              disabled={formData.useMainAddress}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:col-span-2">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {text.clients.email} <span className="text-red-600">*</span>
              </Label>
              <InputComponent
                name="email"
                inputType={'email' as FormInputType}
                placeholder={text.clients.emailPlaceholder}
                value={formData.email}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ email: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {formData.isWhatsApp ? text.clients.phoneWhatsAppLabel : text.clients.phoneOnlyLabel}
              </Label>
              <InputComponent
                name="mainPhoneNumber"
                placeholder={text.clients.phonePlaceholder}
                value={formData.mainPhoneNumber}
                onChange={onPhoneChange}
              />
              <div className="flex items-center space-x-2 pt-2">
                <input
                  type="checkbox"
                  id="isWhatsApp"
                  checked={formData.isWhatsApp}
                  onChange={handleIsWhatsAppChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <Label
                  htmlFor="isWhatsApp"
                  className="text-sm font-medium text-gray-700"
                >
                  {text.clients.isWhatsApp}
                </Label>
              </div>
            </div>
          </div>

          {!formData.isWhatsApp && (
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              {text.clients.whatsappNumber}
            </Label>
            <InputComponent
              name="whatsappNumber"
              placeholder={text.clients.whatsappPlaceholder}
              value={formData.whatsappNumber}
              onChange={onWhatsappNumberChange}
            />
          </div>
          )}
        </CardContent>
      </Card>

      <Card className='bg-white'>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            {text.clients.alternativeContacts}
          </CardTitle>
          <p className="text-sm text-gray-600">
            {text.clients.alternativeContactsDescription}
          </p>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-900">{text.clients.billing}</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.emailLabel}</Label>
                <InputComponent
                  name="billingEmail"
                  placeholder={text.clients.billingEmailPlaceholder}
                  value={formData.billingEmail}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ billingEmail: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.phoneLabel}</Label>
                <InputComponent
                  name="billingPhone"
                  placeholder={text.clients.phonePlaceholder}
                  value={formData.billingPhone}
                  onChange={onBillingPhoneChange}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.whatsappLabel}</Label>
                <InputComponent
                  name="billingWhatsApp"
                  placeholder={text.clients.whatsappPlaceholder}
                  value={formData.billingWhatsApp}
                  onChange={onBillingWhatsAppChange}
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-900">{text.clients.purchases}</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.emailLabel}</Label>
                <InputComponent
                  name="purchasesEmail"
                  placeholder={text.clients.purchasesEmailPlaceholder}
                  value={formData.purchasesEmail}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFields({ purchasesEmail: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.phoneLabel}</Label>
                <InputComponent
                  name="purchasesPhone"
                  placeholder={text.clients.phonePlaceholder}
                  value={formData.purchasesPhone}
                  onChange={onPurchasesPhoneChange}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">{text.clients.whatsappLabel}</Label>
                <InputComponent
                  name="purchasesWhatsApp"
                  placeholder={text.clients.phonePlaceholder}
                  value={formData.purchasesWhatsApp}
                  onChange={onPurchasesWhatsAppChange}
                />
              </div>
            </div>
          </div>

        </CardContent>
      </Card>

      <Card className='bg-white'>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            {text.clients.notes}
          </CardTitle>
          <p className="text-sm text-gray-600">{text.clients.notesDescription}</p>
        </CardHeader>
        <CardContent>
          <TextAreaInput
            name="notes"
            placeholder={text.clients.notesPlaceholder}
            value={formData.notes}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFields({ notes: e.target.value })}
            rows={4}
            className="w-full"
          />
        </CardContent>
      </Card>

    </motion.div>
  );
}
