import { GeniaClient } from './HttpClient.Http';

const client = GeniaClient();
const CLIENTS_PATH = '/clients';
export interface ClientsPayload {
  name: string;
  tributaryId?: string | null | undefined;
  clientCompanyId?: string | null;
  storeDiscounts?: unknown[];
  notes?: string | null;
  contactInformation?: {
    billingEmail: string | null;
    billingPhoneNumber: string | null;
    billingWhatsapp: string | null;
    purchasesEmail: string | null;
    purchasesPhoneNumber: string | null;
    purchasesWhatsapp: string | null;
    salesEmail: string | null;
    salesPhoneNumber: string | null;
    salesWhatsapp: string | null;
    shippingAddress: string | null;
    billingAddress: string | null;
  };
}

const postClients = (payload: Partial<ClientsPayload>[]) => client.post(CLIENTS_PATH, [...payload]);

const updateClient = (id: string, payload: Partial<ClientsPayload>) => client.patch(`${CLIENTS_PATH}/${id}`, payload);

const getClients = () => client.get(CLIENTS_PATH);

export const ClientsHttps = {
  postClients,
  updateClient,
  getClients,
};
